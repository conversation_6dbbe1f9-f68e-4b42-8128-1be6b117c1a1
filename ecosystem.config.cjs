/** Paste the run script
 * pm2 start ecosystem.config.cjs --only UpOfficeDEV-3046 --attach
 * pm2 start ecosystem.config.cjs --only UpOfficeProd-3047 --attach
 */

const path = require('path');
const vitePath = path.normalize('./node_modules/vite/bin/vite.js');
const interpreterPath = path.normalize('/home/<USER>/.nvm/versions/node/v18.20.2/bin/node'); // -- ubuntu
// const interpreterPath = path.normalize('C:\\Program Files\\nodejs\\node.exe'); // -- windows

module.exports = {
  apps: [
    {
      name: 'UpOfficeDEV-3046',
      interpreter: interpreterPath,
      script: vitePath,
      args: '--host',
    },
    {
      name: 'UpOfficeProd-3047',
      script: 'serve',
      env: {
        PM2_SERVE_PATH: './dist/',
        PM2_SERVE_PORT: 3047,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOST: '127.0.0.1',
      },
    },
  ],
};
