// libs
import { Space, InputNumber, SpaceProps } from 'antd';
import { useTranslation } from 'react-i18next';

// utils
import { tTon } from '@/utils';

interface AmountRangeInterface {
  from: number | null;
  to: number | null;
}

interface IAmountRangeProps extends Omit<SpaceProps, 'onChange'> {
  value?: AmountRangeInterface;
  onChange?: (newRange: AmountRangeInterface) => void;
}

const AmountRange: React.FunctionComponent<IAmountRangeProps> = (props) => {
  // props
  const { value, onChange, ...spaceProps } = props || {};

  // hooks
  const { t } = useTranslation('amountRange');

  return (
    <Space {...spaceProps}>
      <InputNumber
        value={value?.from}
        onChange={(newFrom) => {
          if (onChange) onChange(value ? { ...value, from: newFrom } : { from: newFrom, to: null });
        }}
        min={0.01}
        placeholder={t('fromPlaceholder')}
      />
      {t('to')}
      <InputNumber
        value={value?.to}
        onChange={(newTo) => {
          const newToValue = tTon(newTo);
          if (Number.isNaN(newToValue)) return;
          if (onChange) onChange(value ? { ...value, to: newToValue } : { to: newToValue, from: null });
        }}
        placeholder={t('toPlaceholder')}
      />
    </Space>
  );
};

export default AmountRange;
export type { AmountRangeInterface, IAmountRangeProps };
