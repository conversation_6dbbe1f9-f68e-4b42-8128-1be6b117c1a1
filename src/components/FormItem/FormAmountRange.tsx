// libs
import { Form, FormItemProps } from 'antd';

// components
import AmountRange from './AmountRange';

interface IFormAmountRangeProps extends FormItemProps {}

const FormAmountRange: React.FunctionComponent<IFormAmountRangeProps> = (props) => {
  // props
  const { ...formItemProps } = props || {};

  return (
    <Form.Item {...formItemProps}>
      <AmountRange />
    </Form.Item>
  );
};

export default FormAmountRange;
export type { IFormAmountRangeProps };
