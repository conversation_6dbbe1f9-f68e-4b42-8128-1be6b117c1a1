// libs
import { Typography } from 'antd';
import { TextProps } from 'antd/es/typography/Text';

interface ITxtProps extends TextProps {
  wrap?: boolean;
}
const Txt: React.FunctionComponent<ITxtProps> = (props) => {
  // props
  const { className, wrap, ...textProps } = props || {};

  return (
    <Typography.Text
      {...{
        ...textProps,
        className: `
          ${wrap ? '' : 'text-nowrap'} ${className}
        `,
      }}
    />
  );
};

export default Txt;
