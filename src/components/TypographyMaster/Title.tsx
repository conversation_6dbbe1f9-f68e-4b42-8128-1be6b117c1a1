// libs
import { useMemo } from 'react';
import { Typography } from 'antd';
import { TitleProps } from 'antd/es/typography/Title';

// store
import { useUserStore } from '@/store';

const colors: { [key: string]: { light: string; dark: string } } = {
  geekblue: { light: '#52c41a', dark: '#10239e' },
  blue: { light: '#1677ff', dark: '#003eb3' },
  red: { light: '#f5222d', dark: '#a8071a' },
  green: { light: '#52c41a', dark: '#237804' },
  yellow: { light: '#fadb14', dark: '#ad8b00' },
  purple: { light: '#722ed1', dark: '#391085' },
  orange: { light: '#fa8c16', dark: '#ad4e00' },
  magenta: { light: '#eb2f96', dark: '#9e1068' },
  teal: { light: '#009688', dark: '#004D40' },
  gray: { light: '#9E9E9E', dark: '#212121' },
  // 可根據需求擴展顏色
};

interface ITitleProps extends TitleProps {
  tight?: boolean;
  color?: string;
}

const Title: React.FunctionComponent<ITitleProps> = (props) => {
  // props
  const { tight, style, color, className, ...titleProps } = props || {};

  // hooks
  const { isDark } = useUserStore();

  // compute
  const colorResult = useMemo(() => {
    if (!color) return undefined;
    const colorInfo = colors[color];
    if (!colorInfo) return isDark ? '#FFFFFF' : '#000000'; // 預設為白或黑

    return isDark ? colorInfo.light : colorInfo.dark;
  }, [color, isDark]);

  return (
    <Typography.Title
      className={`
        text-nowrap
        text-blue-500
        ${className}
      `}
      {...{
        ...titleProps,
        style: {
          marginBottom: tight ? 0 : undefined,
          color: colorResult,
          ...style,
        },
      }}
    />
  );
};

export default Title;
