// libs
import { Space } from 'antd';

// components
import BtnFuncs, { BtnCopy } from '../BtnFuncs';
import Txt from './Txt';

interface ITxtCompressibleProps {
  isDark: boolean;
  isHovered: boolean;
  isExpanded: boolean;
  text: string | number;
  setIsHover: ReactSet<Array<string>>;
  setIsExpanded: ReactSet<Array<string>>;
}

const TxtCompressible: React.FunctionComponent<ITxtCompressibleProps> = (props) => {
  // props
  const { isDark, isHovered, text = '', isExpanded, setIsHover, setIsExpanded } = props || {};

  return (
    <Space size='small'>
      <section
        className={`
           relative

        `}
        onMouseEnter={() => setIsHover((pre) => [...pre, text.toString()])}
        onMouseLeave={() => setIsHover((pre) => pre.filter((filterP) => filterP !== text))}
      >
        <Txt
          ellipsis={
            isExpanded
              ? undefined
              : {
                  suffix: text.toString().slice(-5),
                }
          }
          className={`
            transition-[width]
            ${isExpanded ? 'w-96' : 'w-24'}
          `}
        >
          {text}
        </Txt>
        {/* mask */}
        <BtnCopy
          text={text.toString()}
          className={`
            black absolute
            left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] overflow-hidden border border transition-all
            ${isDark ? 'border-blue-700' : 'border-blue-500'}
            ${isHovered ? 'border' : 'border-none'}
            ${isHovered ? 'w-[calc(100%+20px)]' : 'w-[80%]'}
            ${isHovered ? 'h-[calc(100%+20px)]' : 'h-[80%]'}
            ${isHovered ? 'opacity-100' : 'opacity-0'}
            ${isHovered ? 'rounded' : 'rounded-none'}
          `}
        />
      </section>

      <BtnFuncs
        size='small'
        type='link'
        iconType={isExpanded ? 'compression' : 'expand'}
        onClick={() =>
          setIsExpanded((pre) => (isExpanded ? pre.filter((filterP) => filterP !== text) : [...pre, text.toString()]))
        }
      />
    </Space>
  );
};

export default TxtCompressible;
