// libs
import { HtmlHTMLAttributes, useCallback } from 'react';

// hooks
import { useResizeObserver } from '@/hooks';

type ObserveProps = { clientHeight: number; scrollHeight: number; clientWidth: number; scrollWidth: number };

interface IObserveBoxProps extends HtmlHTMLAttributes<HTMLDivElement> {
  onObserve?: (props: ObserveProps) => void;
}

const ObserveBox: React.FunctionComponent<IObserveBoxProps> = (props: IObserveBoxProps) => {
  // props
  const { onObserve, ...divProps } = props;

  // hooks
  const ref = useResizeObserver<HTMLDivElement>(
    useCallback(
      (rezEntry) => {
        const { clientHeight, scrollHeight, clientWidth, scrollWidth } = rezEntry.target;
        if (onObserve) onObserve({ clientHeight, scrollHeight, clientWidth, scrollWidth });
      },
      [onObserve],
    ),
  );
  return <div {...{ ...divProps, ref }} />;
};

export default ObserveBox;
export type { ObserveProps };
