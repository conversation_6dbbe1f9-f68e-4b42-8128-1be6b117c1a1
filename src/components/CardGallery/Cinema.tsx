// libs
import { HtmlHTMLAttributes, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Input } from 'antd';

// hooks
import { useResizeObserver } from '@/hooks';

// styles
import styles from './cinema.module.scss';

// components
import ObserveBox from './ObserveBox';

const isTest = false;

interface ICinemaProps<KeyOfMovie extends string | undefined = string | undefined>
  extends HtmlHTMLAttributes<HTMLDivElement> {
  currentMv: KeyOfMovie;
  screens: KeyOfMovie extends string ? { [key in KeyOfMovie]: React.ReactNode } : {};
  onChangeMv?: (newKey: KeyOfMovie) => void;
}

const Cinema: React.FunctionComponent<ICinemaProps> = (props) => {
  // props
  const { currentMv, screens, style, className, onChangeMv, ...divProps } = props || {};

  // states
  const [containerWidth, setContainerWidth] = useState(0);
  const [paintingsHeight, setPaintingsHeight] = useState(300);
  const [deg, setDeg] = useState(40);

  // compute
  const dIndex = useMemo(() => (currentMv ? Object.keys(screens).indexOf(currentMv) : -1), [currentMv, screens]);

  // 透過 Container 最大寬度設定畫作寬度
  const screensContainerRef = useResizeObserver<HTMLDivElement>(
    useCallback((rezEntry) => {
      const { clientWidth } = rezEntry.target;
      setContainerWidth(clientWidth);
    }, []),
  );

  // 變換 Mv 的 callback
  const lastMv = useRef<string>();
  useEffect(() => {
    if (!onChangeMv || lastMv.current === currentMv) return;
    lastMv.current = currentMv;
    onChangeMv(currentMv);
  }, [onChangeMv, currentMv]);

  return (
    <div
      className={`${styles['screens-container']} relative ${className}`}
      style={{
        height: paintingsHeight,
        transition: 'height 0.5s',
        ...style,
      }}
      ref={screensContainerRef}
      {...divProps}
    >
      {Object.entries(screens).map(([key, paintings], index) => {
        const currentIndex = index - dIndex;
        const width = containerWidth;
        const thisMovie = currentIndex === 0;
        return (
          <div
            key={key}
            className={`${styles.paintings}
							${thisMovie ? styles.active : ''}
						`}
            style={{
              minWidth: width,
              maxWidth: width,
              top: 0,
              left: currentIndex * width + 100 * currentIndex,
            }}
          >
            <div className={`${styles.rotate} ${thisMovie ? styles.active : ''}`}>
              <div className={`${styles['rotate-2']} ${thisMovie ? styles.active : ''}`}>
                {/* 內容監視器: 在固定外層寬度的條件下，取得內容自動換行後的高度 */}
                <ObserveBox
                  onObserve={(obserProps) => {
                    if (key !== currentMv) return;
                    const { scrollHeight } = obserProps;
                    setPaintingsHeight(scrollHeight);
                  }}
                >
                  {import.meta.env.DEV && isTest && (
                    <div>
                      <div className='flex'>
                        <Input
                          value={deg}
                          onChange={(e) => setDeg(Number(e.target.value))}
                        />
                        <div className='flex space-x-4'>
                          <button
                            type='button'
                            onClick={() => setDeg((pre) => pre + 1)}
                          >
                            upper
                          </button>
                          <button
                            type='button'
                            onClick={() => setDeg((pre) => pre - 1)}
                          >
                            lower
                          </button>
                        </div>
                      </div>

                      <div className='flex flex-col'>
                        <p>currentIndex: {currentIndex}</p>
                        <p>paintingsHeight: {paintingsHeight}</p>
                        <p>containerWidth: {containerWidth}</p>
                      </div>
                    </div>
                  )}

                  {paintings as React.ReactNode}
                </ObserveBox>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default Cinema;
export type { ICinemaProps };
