.screens-container {
  display: flex;
  scroll-behavior: smooth; /* 確保在大多數瀏覽器中滾動時有平滑效果 */
  scrollbar-width: none; /* Firefox 隱藏滾軸 */
  -ms-overflow-style: none; /* Internet Explorer 隱藏滾軸 */
  overflow: hidden;
  white-space: nowrap;
}

.screens-container::-webkit-scrollbar {
  display: none; /* Webkit 隱藏滾軸 */
}

.paintings {
  opacity: 0;
  overflow-y: hidden;
  // overflow-wrap: break-word; // 固定寬度自動推擠最高的高度
  // word-wrap: break-word;
  scrollbar-width: thin;
  position: absolute;
  &.active {
    opacity: 1;
  }
  transition:
    left 0.2s linear,
    opacity 0.4s linear;
  -webkit-transition:
    left 0.2s linear,
    opacity 0.4s linear;
  -moz-transition:
    left 0.2s linear,
    opacity 0.4s linear;
  -ms-transition:
    left 0.2s linear,
    opacity 0.4s linear;
  -o-transition:
    left 0.2s linear,
    opacity 0.4s linear;
  transition-delay: 200ms, 200ms;
  transition-property: left, opacity;
}

.rotate {
  box-sizing: border-box;
  &::after {
    // 光暈開啟
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 1), rgba(100, 100, 100, 0.3) 110%);

    transition: width 0.2s;
    -webkit-transition: width 0.2s;
    -moz-transition: width 0.2s;
    -ms-transition: width 0.2s;
    -o-transition: width 0.2s;
  }

  &.active {
    &::after {
      // 光暈關閉
      width: 0%;
    }
    transform: rotateY(0deg);
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
  }
  transform: rotateY(45deg);
  -webkit-transform: rotateY(45deg);
  -moz-transform: rotateY(45deg);
  -ms-transform: rotateY(45deg);
  -o-transform: rotateY(45deg);

  transition: transform 0.2s linear;
  -webkit-transition: transform 0.2s linear;
  -moz-transition: transform 0.2s linear;
  -ms-transition: transform 0.2s linear;
  -o-transition: transform 0.2s linear;
}

.rotate-2 {
  box-sizing: border-box;
  &::after {
    // 光暈開啟
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 1), rgba(100, 100, 100, 0.3) 110%);

    transition: width 0.2s linear 400ms;
    -webkit-transition: width 0.2s linear 400ms;
    -moz-transition: width 0.2s linear 400ms;
    -ms-transition: width 0.2s linear 400ms;
    -o-transition: width 0.2s linear 400ms;
  }

  &.active {
    &::after {
      // 光暈關閉
      width: 0%;
    }
    transform: rotateY(0deg);
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
  }
  transform: rotateY(45deg);
  -webkit-transform: rotateY(45deg);
  -moz-transform: rotateY(45deg);
  -ms-transform: rotateY(45deg);
  -o-transform: rotateY(45deg);

  transition: transform 0.2s linear 400ms;
  -webkit-transition: transform 0.2s linear 400ms;
  -moz-transition: transform 0.2s linear 400ms;
  -ms-transition: transform 0.2s linear 400ms;
  -o-transition: transform 0.2s linear 400ms;
}
