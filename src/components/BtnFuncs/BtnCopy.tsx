import { memo, useCallback, useMemo, useState } from 'react';
import { ButtonProps, Tooltip } from 'antd';
import { CheckCircleFilled, CloseCircleOutlined, CopyOutlined } from '@ant-design/icons';
import { useUserStore } from '@/store';
import { logInfo } from '@/utils';

const copyOnClick = (text: string) => {
  const tempInput = document.createElement('input');
  tempInput.setAttribute('style', 'position: fixed; left: -1000px; top: -1000px');
  tempInput.value = text;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand('copy');
  document.body.removeChild(tempInput);
  logInfo(`Copy success ${text}`);
};

interface IBtnCopyProps extends Omit<ButtonProps, 'onClick' | 'type'> {
  text: string;
  onClick?: (text: string) => void;
  tooltips?: [string, string];
}

const BtnCopy: React.FunctionComponent<IBtnCopyProps> = (props) => {
  // props
  const { text, onClick, tooltips, className, ...btnProps } = props || {};

  // states
  const [isCoping, setIsCoping] = useState(false);
  const [loadingDelay, setLoadingDelay] = useState(false);
  const [isCopyFailed, setIsCopyFailed] = useState(false);

  // hooks
  const { isDark } = useUserStore();

  // handlers
  const handleOnClick = useCallback(() => {
    if (onClick) onClick(text);
    setIsCoping(true);
    setLoadingDelay(true);
    try {
      if (!navigator.clipboard) {
        copyOnClick(text);
        setIsCoping(false);
      } else
        navigator.clipboard
          .writeText(text)
          .catch((e) => {
            logInfo({ Title: 'Copy failed with clipboard', e });
            copyOnClick(text);
          })
          .finally(() => {
            setIsCoping(false);
          });
    } catch (e) {
      logInfo({ Title: 'Copy failed with document', e });
      setIsCopyFailed(true);
      setTimeout(() => setIsCopyFailed(false), 1000);
    }

    setTimeout(() => {
      setLoadingDelay(false);
    }, 1000);
  }, [onClick, text]);

  // compute
  const isLoading = useMemo(() => isCoping || loadingDelay, [isCoping, loadingDelay]);
  const tooltipTitle = useMemo(() => {
    if (isLoading) {
      return tooltips?.at(2) || 'Copied';
    }
    return tooltips?.at(0) || 'Copy';
  }, [isLoading, tooltips]);

  return (
    <Tooltip title={tooltipTitle}>
      <button
        type='button'
        onClick={handleOnClick}
        className={`
          ${isDark ? 'text-blue-700' : 'text-blue-500'}
          ${className}
        `}
        {...{ ...btnProps }}
      >
        {isCopyFailed && (
          <CloseCircleOutlined
            className={`
             ${isDark ? 'text-red-700' : 'text-red-500'}
           `}
          />
        )}
        {isLoading && !isCopyFailed && (
          <CheckCircleFilled
            className={`
             ${isDark ? 'text-green-700' : 'text-green-500'}
           `}
          />
        )}

        {!isLoading && !isCopyFailed && <CopyOutlined />}
      </button>
    </Tooltip>
  );
};

export default memo(BtnCopy);
