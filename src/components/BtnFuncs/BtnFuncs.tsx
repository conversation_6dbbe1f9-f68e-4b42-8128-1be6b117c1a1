import { RefObject, useMemo } from 'react';
import { Tooltip } from 'antd';
// prettier-ignore
import { AuditOutlined, CalculatorOutlined, CloseCircleOutlined, DeleteOutlined, EditOutlined, ExpandOutlined, ExportOutlined, FileExcelTwoTone, FullscreenExitOutlined, PlusCircleOutlined, PrinterOutlined, RedoOutlined, SearchOutlined, SendOutlined, UnorderedListOutlined, } from '@ant-design/icons';
import { IconBaseProps } from '@ant-design/icons/lib/components/Icon';
import ButtonAlpha, { IButtonAlphaProps } from '../ButtonAlpha';

interface IBtnFuncsProps extends IButtonAlphaProps {
  iconType?:
    | 'export'
    | 'search'
    | 'compression'
    | 'expand'
    | 'edit'
    | 'remove'
    | 'add'
    | 'submit'
    | 'viewList'
    | 'redo'
    | 'disabled'
    | 'calc'
    | 'excel'
    | 'print'
    | 'approve';
  iconProps?: IconBaseProps;
  tooltip?: string;
  ref?: RefObject<HTMLButtonElement>;
}

const BtnFuncs: React.FunctionComponent<IBtnFuncsProps> = (props) => {
  // props
  const { iconType, icon, iconProps, tooltip, ...btnProps } = props || {};

  // compute
  const iconDefinition = useMemo(() => {
    if (icon) return icon;
    if (iconType === 'export') return <ExportOutlined {...iconProps} />;
    if (iconType === 'search') return <SearchOutlined {...iconProps} />;
    if (iconType === 'edit') return <EditOutlined {...iconProps} />;
    if (iconType === 'remove') return <DeleteOutlined {...iconProps} />;
    if (iconType === 'add') return <PlusCircleOutlined {...iconProps} />;
    if (iconType === 'submit') return <SendOutlined {...iconProps} />;
    if (iconType === 'viewList') return <UnorderedListOutlined {...iconProps} />;
    if (iconType === 'redo') return <RedoOutlined {...iconProps} />;
    if (iconType === 'disabled') return <CloseCircleOutlined {...iconProps} />;
    if (iconType === 'expand') return <ExpandOutlined {...iconProps} />;
    if (iconType === 'compression') return <FullscreenExitOutlined {...iconProps} />;
    if (iconType === 'calc') return <CalculatorOutlined {...iconProps} />;
    if (iconType === 'approve') return <AuditOutlined {...iconProps} />;
    if (iconType === 'excel')
      return (
        <FileExcelTwoTone
          twoToneColor='green'
          {...{
            style: {
              transform: 'translate(5%, 0%)',
            },
            ...iconProps,
          }}
        />
      );
    if (iconType === 'print') return <PrinterOutlined {...iconProps} />;
    return undefined;
  }, [icon, iconType, iconProps]);

  return (
    <Tooltip title={tooltip}>
      <ButtonAlpha
        {...btnProps}
        icon={iconDefinition}
      />
    </Tooltip>
  );
};

export default BtnFuncs;
