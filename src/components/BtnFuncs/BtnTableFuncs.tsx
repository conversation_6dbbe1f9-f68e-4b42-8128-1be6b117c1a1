import { Space, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import BtnFuncs from './BtnFuncs';

interface IBtnTableFuncsProps {
  loading?: boolean;
  onPrintClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  onExcelClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
}

const BtnTableFuncs: React.FunctionComponent<IBtnTableFuncsProps> = (props) => {
  // props
  const { loading, onPrintClick, onExcelClick } = props || {};

  // hooks
  const { t } = useTranslation('btnTableFuncs');

  return (
    <Space>
      <Tooltip title={t('print')}>
        <BtnFuncs
          onClick={(e) => {
            if (onPrintClick) onPrintClick(e);
          }}
          iconType='print'
          shape='circle'
          loading={loading}
        />
      </Tooltip>

      <Tooltip title={t('exportExcel')}>
        <BtnFuncs
          onClick={(e) => {
            if (onExcelClick) onExcelClick(e);
          }}
          iconType='excel'
          shape='circle'
          loading={loading}
        />
      </Tooltip>
    </Space>
  );
};

export default BtnTableFuncs;
