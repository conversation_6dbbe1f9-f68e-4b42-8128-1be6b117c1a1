/* eslint-disable react/require-default-props */
// libs
import { forwardRef, useImperativeHandle, useRef } from 'react';
import { useTranslation } from 'react-i18next';

// store
import { useNotifyStore } from '@/store';

// components
import { Title } from '@/components/TypographyMaster';
import ModalAlpha, { IModalAlphaProps } from '../ModalAlpha';
import OpwVfy, { IOpwVfyProps, OpwVfyRef } from './OpwVfy';

interface OpwVfyModalRef extends OpwVfyRef {}

interface IOpwVfyModalProps extends IModalAlphaProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<IOpwVfyModalProps['open']>>;
  vfyProps?: IOpwVfyProps;
}

const OpwVfyModal = forwardRef<OpwVfyModalRef, IOpwVfyModalProps>((props, ref) => {
  //  props
  const { open, setOpen, vfyProps, ...modalProps } = props || {};

  // refs
  const vfyRef = useRef<OpwVfyRef>(null);

  // hooks
  const { pushBEQ } = useNotifyStore();
  const { t } = useTranslation('opwVfyModal');

  // init
  useImperativeHandle(
    ref,
    () => ({
      setError: () => {
        pushBEQ([{ title: 'UXM Settlement Corp', des: t('errorDescription') }]);
      },
      resetForm: () => {
        vfyRef.current?.resetForm();
      },
    }),
    [pushBEQ, t],
  );

  return (
    <ModalAlpha
      {...{ open }}
      onCancel={() => {
        if (setOpen) setOpen(false);
        vfyRef.current?.resetForm();
      }}
      title={<Title level={3}>{t('title')}</Title>}
      footer={null}
      {...modalProps}
    >
      <OpwVfy
        ref={vfyRef}
        {...vfyProps}
      />
    </ModalAlpha>
  );
});

export default OpwVfyModal;
export type { IOpwVfyModalProps };
