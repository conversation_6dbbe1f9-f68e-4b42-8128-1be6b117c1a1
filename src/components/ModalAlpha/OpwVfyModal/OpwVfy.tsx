/* eslint-disable react/require-default-props */
// libs
import { HtmlHTMLAttributes, forwardRef, useImperativeHandle, useState } from 'react';
import { Button, Form, FormProps, Input } from 'antd';
import { useTranslation } from 'react-i18next';

// components
import { Title } from '@/components/TypographyMaster';

type OpwVfyRef = {
  setError: () => void;
  resetForm: () => void;
};

interface IOpwVfyProps extends FormProps {
  onFinish?: (values: { code: string }) => void;
  loading?: boolean;
  containerProps?: HtmlHTMLAttributes<HTMLDivElement>;
}

const OpwVfy = forwardRef<OpwVfyRef, IOpwVfyProps>((props, ref) => {
  // props
  const { onFinish, loading, containerProps, ...formProps } = props || {};

  // states
  const [isError, setIsError] = useState(false);

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('opwVfy');

  useImperativeHandle(
    ref,
    () => ({
      setError: () => {
        setIsError(true);
        setTimeout(() => setIsError(false), 1000);
      },
      resetForm: () => {
        form.resetFields(['code']);
      },
    }),
    [form],
  );

  return (
    <div {...containerProps}>
      <Form
        {...{ form, ...formProps }}
        onFinish={(values) => {
          form.resetFields(['code']);
          onFinish?.(values);
        }}
        variant='filled'
        layout='vertical'
      >
        <Form.Item
          name='code'
          label={t('label')}
        >
          <Input.Password
            variant='filled'
            placeholder={t('placeholder')}
            disabled={loading}
          />
        </Form.Item>

        <footer className='flex w-full justify-between'>
          <Title
            level={5}
            type='danger'
            tight
            className={`text-nowrap overflow-x-hidden transition-all duration-200
              ${isError ? 'max-w-24' : 'max-w-0'}
            `}
          >
            {t('errorMessage')}
          </Title>
          <Form.Item className='mb-0'>
            <Button
              loading={loading}
              htmlType='submit'
              type='primary'
            >
              {t('confirm')}
            </Button>
          </Form.Item>
        </footer>
      </Form>
    </div>
  );
});

export default OpwVfy;
export type { IOpwVfyProps, OpwVfyRef };
