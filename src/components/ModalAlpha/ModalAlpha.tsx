// libs
import { HtmlHTMLAttributes, memo, useMemo, useState } from 'react';
import { Modal, ModalProps } from 'antd';

// components
import ObserveBox from '../CardGallery/ObserveBox';

const isTest = false;

interface IModalAlphaProps extends ModalProps {
  titleContainerProps?: HtmlHTMLAttributes<HTMLDivElement>;
  childrenContainerProps?: HtmlHTMLAttributes<HTMLDivElement>;
  maximize?: boolean;
}

const ModalAlpha: React.FunctionComponent<IModalAlphaProps> = (props) => {
  // props
  const { width, maximize, title, children, styles, ...modalProps } = props || {};
  const { titleContainerProps, childrenContainerProps, style, ...otherProps } = modalProps;
  const { content: contentStyle, ...otherStyles } = styles || {};

  // states
  const [titleHeight, setTitleHeight] = useState<number>();
  const [childrenHeight, setChildrenHeight] = useState<number>();
  const [originDt, setOriginDt] = useState(0);

  // compute
  const top = useMemo(() => {
    const dt = (document.documentElement.clientHeight - ((childrenHeight ?? 0) + (titleHeight ?? 0) + 50)) / 2 - 100;

    setOriginDt(dt);
    if (dt < -80) return -80;
    if (dt > 0) return 0;
    return dt;
  }, [childrenHeight, titleHeight]);

  return (
    <Modal
      width={maximize ? '95vw' : width}
      style={{ overflowY: 'visible', ...style }}
      styles={{
        content: {
          top,
          maxHeight: 'calc(100vh - 45px)',
          overflowY: 'auto',
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(150, 150, 150, 1) transparent',
          position: 'relative',
          margin: 0,
          ...contentStyle,
        },
        ...otherStyles,
      }}
      title={
        <ObserveBox
          onObserve={(observeProps) => {
            const { scrollHeight } = observeProps;
            setTitleHeight(scrollHeight);
          }}
          {...titleContainerProps}
        >
          {title}
        </ObserveBox>
      }
      {...otherProps}
    >
      <ObserveBox
        onObserve={(observeProps) => {
          const { scrollHeight } = observeProps;
          setChildrenHeight(scrollHeight);
        }}
        {...childrenContainerProps}
      >
        {children}
      </ObserveBox>

      {isTest && import.meta.env.DEV && (
        <div className='test-fixed'>
          <div>Origin dt: {originDt}</div>
          <div>top: {top}</div>
        </div>
      )}
    </Modal>
  );
};

export default memo(ModalAlpha);
export type { IModalAlphaProps };
