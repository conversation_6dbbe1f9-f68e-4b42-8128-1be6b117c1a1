/* eslint-disable react/require-default-props */
// libs
import { forwardRef, useImperativeHandle, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';

// api
import { DepositAnomalyItemInterface } from '@/api/order';

// store
import { useNotifyStore } from '@/store';

// components
import { Title } from '@/components/TypographyMaster';
import ModalAlpha, { IModalAlphaProps } from '../ModalAlpha';
import TxVfy, { ITxVfyProps, TxVfyRef } from './TxVfy';
import TxVfyPreSelectHash from './TxVfyPreSelectHash';

interface TxVfyModalRef extends TxVfyRef {}

interface ITxVfyModalProps extends IModalAlphaProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<ITxVfyModalProps['open']>>;
  vfyProps?: ITxVfyProps;
  matchAnomalyDespoit?: DepositAnomalyItemInterface | undefined;
  needMatch?: boolean;
}

const TxVfyModal = forwardRef<TxVfyModalRef, ITxVfyModalProps>((props, ref) => {
  // props
  const { open, setOpen, vfyProps, matchAnomalyDespoit, ...modalProps } = props || {};

  // refs
  const vfyRef = useRef<TxVfyRef>(null);

  // hooks
  const { pushBEQ } = useNotifyStore();
  const { t } = useTranslation('txVfyModal');

  // compute
  const title = useMemo(() => {
    if (matchAnomalyDespoit) return <Title level={3}>{t('matchAnomalyTitle')}</Title>;
    return <Title level={3}>{t('defaultTitle')}</Title>;
  }, [matchAnomalyDespoit, t]);

  // init
  useImperativeHandle(
    ref,
    () => ({
      setError: () => {
        pushBEQ([{ title: 'UXM Settlement Corp', des: t('errorDescription') }]);
      },
      resetForm: () => {
        vfyRef.current?.resetForm();
      },
    }),
    [pushBEQ, t],
  );

  return (
    <ModalAlpha
      {...{ open, title }}
      onCancel={() => {
        if (setOpen) setOpen(false);
        vfyRef.current?.resetForm();
      }}
      footer={null}
      width='40vw'
      {...modalProps}
    >
      {matchAnomalyDespoit && (
        <TxVfyPreSelectHash
          {...{ matchAnomalyDespoit }}
          ref={vfyRef}
          {...vfyProps}
        />
      )}

      {!matchAnomalyDespoit && (
        <TxVfy
          ref={vfyRef}
          {...vfyProps}
        />
      )}
    </ModalAlpha>
  );
});

export default TxVfyModal;
export type { ITxVfyModalProps };
