/* eslint-disable react/require-default-props */
// libs
import { HtmlHTMLAttributes, forwardRef, useImperativeHandle, useState } from 'react';
import { Button, Form, FormProps, Input } from 'antd';
import { useTranslation } from 'react-i18next';

// components
import { Title } from '@/components/TypographyMaster';

type GaVfyRef = {
  setError: () => void;
};

interface IGaVfyProps extends FormProps {
  onFinish?: (values: { code: string }) => void;
  loading?: boolean;
  containerProps?: HtmlHTMLAttributes<HTMLDivElement>;
}

const GaVfy = forwardRef<GaVfyRef, IGaVfyProps>((props, ref) => {
  // props
  const { onFinish, loading, containerProps, ...formProps } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('gaVfy');

  // states
  const [isError, setIsError] = useState(false);

  useImperativeHandle(
    ref,
    () => {
      return {
        setError: () => {
          setIsError(true);
          setTimeout(() => setIsError(false), 1000);
        },
      };
    },
    [],
  );

  return (
    <div {...containerProps}>
      <Form
        {...{ form, ...formProps, onFinish }}
        variant='filled'
        layout='vertical'
      >
        <Form.Item
          name='code'
          label={t('codeLabel')}
        >
          <Input.Password
            variant='filled'
            placeholder={t('codePlaceholder')}
            disabled={loading}
          />
        </Form.Item>

        <footer className='flex w-full justify-between'>
          <Title
            level={5}
            type='danger'
            tight
            className={`text-nowrap overflow-x-hidden transition-all duration-200
              ${isError ? 'max-w-24' : 'max-w-0'}
            `}
          >
            {t('_2faError')}
          </Title>
          <Form.Item className='mb-0'>
            <Button
              loading={loading}
              htmlType='submit'
              type='primary'
            >
              {t('submit')}
            </Button>
          </Form.Item>
        </footer>
      </Form>
    </div>
  );
});

export default GaVfy;
export type { IGaVfyProps, GaVfyRef };
