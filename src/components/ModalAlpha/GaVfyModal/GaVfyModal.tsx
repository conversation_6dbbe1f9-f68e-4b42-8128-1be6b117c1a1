/* eslint-disable react/require-default-props */
// libs
import { forwardRef, useImperativeHandle, useRef } from 'react';
import { useTranslation } from 'react-i18next';

// store
import { useNotifyStore } from '@/store';

// components
import { Title } from '@/components/TypographyMaster';
import ModalAlpha, { IModalAlphaProps } from '../ModalAlpha';
import GaVfy, { IGaVfyProps, GaVfyRef } from './GaVfy';

interface GaVfyModalRef extends GaVfyRef {}

interface IGaVfyModalProps extends IModalAlphaProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<IGaVfyModalProps['open']>>;
  vfyProps?: IGaVfyProps;
}

const GaVfyModal = forwardRef<GaVfyModalRef, IGaVfyModalProps>((props, ref) => {
  // props
  const { open, setOpen, vfyProps, ...modalProps } = props || {};

  // refs
  const vfyRef = useRef<GaVfyModalRef>(null);

  // hooks
  const { pushBEQ } = useNotifyStore();
  const { t } = useTranslation('gaVfyModal');

  // init
  useImperativeHandle(
    ref,
    () => {
      if (!vfyRef.current)
        return {
          setError: () => {
            pushBEQ([{ title: 'UXM Settlement Corp', des: t('errorNotificationDescription') }]);
          },
        };
      return {
        ...vfyRef.current,
      };
    },
    [pushBEQ, t],
  );

  return (
    <ModalAlpha
      {...{ open }}
      onCancel={() => {
        if (setOpen) setOpen(false);
      }}
      title={<Title level={3}>{t('title')}</Title>}
      footer={null}
      {...modalProps}
    >
      <GaVfy
        ref={vfyRef}
        {...vfyProps}
      />
    </ModalAlpha>
  );
});

export default GaVfyModal;
export type { IGaVfyModalProps };
