// libs
import { memo } from 'react';
import { Input, Form } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useCreateAnomalyDeposit } from '@/api/order/useCreateDepositAnamoly';

// utils
import { logWarn } from '@/utils';

// components
import ModalAlpha, { IModalAlphaProps } from '@/components/ModalAlpha';

interface ICreateRecordModalProps extends IModalAlphaProps {
  visible: boolean;
  onClose: () => void;
  onRecordCreated?: () => void;
}

const CreateRecordModal: React.FunctionComponent<ICreateRecordModalProps> = (props) => {
  // props
  const { visible, onClose, onRecordCreated } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('createAnomalyRecordModal');
  const { mutate: createAnomalyDeposit, isPending: isLoading } = useCreateAnomalyDeposit({
    onSuccess: () => {
      form.resetFields();
      if (onRecordCreated) onRecordCreated();
      onClose();
    },
  });

  // handlers
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      createAnomalyDeposit(values);
    } catch (error) {
      logWarn({ Title: 'Create anomaly deposit error', error });
    }
  };

  return (
    <ModalAlpha
      open={visible}
      onCancel={onClose}
      onOk={handleOk}
      title={t('title')}
      confirmLoading={isLoading}
      width='30vw'
      maximize={false}
    >
      <Form
        form={form}
        layout='vertical'
        initialValues={{
          hash: '',
          to: '',
          from: '',
          amount: 0,
          gas: 0,
          remark: '',
          operationPassword: '',
        }}
      >
        <Form.Item
          label={t('hashLabel')}
          name='hash'
          rules={[{ required: true, message: t('hashErrorMessage') }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label={t('fromLabel')}
          name='from'
          rules={[{ required: true, message: t('fromErrorMessage') }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label={t('toLabel')}
          name='to'
          rules={[{ required: true, message: t('toErrorMessage') }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label={t('amountLabel')}
          name='amount'
          rules={[{ required: true, message: t('amountErrorMessage') }]}
        >
          <Input type='number' />
        </Form.Item>

        <Form.Item
          label={t('gasLabel')}
          name='gas'
        >
          <Input type='number' />
        </Form.Item>

        <Form.Item
          label={t('remarkLabel')}
          name='remark'
        >
          <Input.TextArea rows={3} />
        </Form.Item>

        <Form.Item
          label={t('operationPasswordLabel')}
          name='operationPassword'
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password />
        </Form.Item>
      </Form>
    </ModalAlpha>
  );
};

export default memo(CreateRecordModal);
