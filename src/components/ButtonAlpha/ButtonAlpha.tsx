import { useIsDemo } from '@/hooks';
import { Button, ButtonProps } from 'antd';
import { useCallback } from 'react';
import birdJo from '@/assets/sound/bird_jo.wav';

const birdJoAudio = new Audio(birdJo);

interface IButtonAlphaProps extends ButtonProps {}
const ButtonAlpha: React.FunctionComponent<IButtonAlphaProps> = (props) => {
  const { onClick, ...btnProps } = props || {};
  const isDemo = useIsDemo();

  const handleClick = useCallback(
    (e: React.MouseEvent<HTMLElement>) => {
      if (isDemo) birdJoAudio.play();
      if (onClick) onClick(e);
    },
    [isDemo, onClick],
  );

  return (
    <Button
      onClick={handleClick}
      {...btnProps}
    />
  );
};

export default ButtonAlpha;
export type { IButtonAlphaProps };
