import { useCallback, useMemo } from 'react';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { LeaseInOrderStatusEnum, leaseInOrderStatusOptions } from '@/utils';
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagLeaseStatusProps extends Omit<ITagAlphaProps, 'onClick'> {
  status: LeaseInOrderStatusEnum | undefined | null;
  onClick?: (status: ITagLeaseStatusProps['status']) => void;
  tooltip?: string;
}

const TagLeaseStatus: React.FunctionComponent<ITagLeaseStatusProps> = (props) => {
  const { status, onClick, tooltip, className, ...tagProps } = props || {};

  const { t } = useTranslation('options');

  const icon = useMemo(() => {
    if (status === LeaseInOrderStatusEnum.Pending) return <LoadingOutlined />;
    if (status === LeaseInOrderStatusEnum.Transferring) return <SyncOutlined />;
    if (status === LeaseInOrderStatusEnum.TransferSuccess) return <CheckCircleOutlined />;
    if (status === LeaseInOrderStatusEnum.EnergyReceived) return <DownloadOutlined />;
    if (status === LeaseInOrderStatusEnum.TransferFailed) return <CloseCircleOutlined />;

    return <ExclamationCircleOutlined />;
  }, [status]);
  const color = useMemo(() => {
    if (status === LeaseInOrderStatusEnum.Pending) return 'gold';
    if (status === LeaseInOrderStatusEnum.Transferring) return 'processing';
    if (status === LeaseInOrderStatusEnum.TransferSuccess) return 'success';
    if (status === LeaseInOrderStatusEnum.EnergyReceived) return 'cyan';
    if (status === LeaseInOrderStatusEnum.TransferFailed) return 'error';

    return 'default';
  }, [status]);
  const label = useMemo(
    () => t(leaseInOrderStatusOptions.find((option) => option.value === status)?.label || ''),
    [t, status],
  );

  const handleClick = useCallback(() => {
    if (onClick) onClick(status);
  }, [onClick, status]);

  return (
    <TagAlpha
      className={className}
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagLeaseStatus;
export type { ITagLeaseStatusProps };
