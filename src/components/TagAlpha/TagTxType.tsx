// libs
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// utils
import { TxCategoryNum } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';
import { Txt } from '../TypographyMaster';

interface ITagTxTypeProps extends ITagAlphaProps {
  type: TxCategoryNum;
}

const TagTxType: React.FunctionComponent<ITagTxTypeProps> = (props) => {
  // props
  const { type, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('tagTxType');

  // compute
  const label = useMemo(() => {
    if (type === TxCategoryNum.Deposit) return t('deposit');
    if (type === TxCategoryNum.Commission) return t('commission');
    if (type === TxCategoryNum.Transfer) return t('transfer');
    if (type === TxCategoryNum.Withdraw) return t('withdraw');
    return t('undefined');
  }, [type, t]);
  const color = useMemo(() => {
    if (type === TxCategoryNum.Deposit) return 'bg-green-500';
    if (type === TxCategoryNum.Commission) return 'bg-gold-500';
    if (type === TxCategoryNum.Transfer) return 'bg-purple-500';
    if (type === TxCategoryNum.Withdraw) return 'bg-red-500';
    return 'bg-grey-500';
  }, [type]);

  return (
    <TagAlpha
      className={`${color} ${className}`}
      {...tagProps}
    >
      <Txt>{label}</Txt>
    </TagAlpha>
  );
};

export default TagTxType;
export type { ITagTxTypeProps };
