// libs
import { useCallback, useMemo } from 'react';
import { Tooltip } from 'antd';
import { CheckCircleOutlined, WarningOutlined, CloseOutlined, LoadingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// utils
import { ServerStatusEnum } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagServerStatusProps extends Omit<ITagAlphaProps, 'onClick'> {
  status: ServerStatusEnum;
  onClick?: (status: ITagServerStatusProps['status']) => void;
  tooltip?: string;
}

const TagServerStatus: React.FunctionComponent<ITagServerStatusProps> = (props) => {
  // props
  const { status, onClick, tooltip, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('tagServerStatus');

  // compute
  const icon = useMemo(() => {
    if (status === ServerStatusEnum.Synchronous) return <CheckCircleOutlined />;
    if (status === ServerStatusEnum.Delay) return <WarningOutlined />;
    if (status === ServerStatusEnum.Disconnected) return <CloseOutlined />;
    return <LoadingOutlined />;
  }, [status]);
  const color = useMemo(() => {
    if (status === ServerStatusEnum.Synchronous) return 'success';
    if (status === ServerStatusEnum.Delay) return 'warning';
    if (status === ServerStatusEnum.Disconnected) return 'error';
    return 'default';
  }, [status]);
  const label = useMemo(() => {
    if (status === ServerStatusEnum.Synchronous) return t('synchronous');
    if (status === ServerStatusEnum.Delay) return t('delay');
    if (status === ServerStatusEnum.Disconnected) return t('disconnected');
    return t('undefined');
  }, [status, t]);

  // handlers
  const handleClick = useCallback(() => {
    if (onClick) onClick(status);
  }, [onClick, status]);

  return (
    <TagAlpha
      className='cursor-pointer'
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagServerStatus;
export type { ITagServerStatusProps };
