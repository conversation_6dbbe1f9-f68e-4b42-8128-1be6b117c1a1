// libs
import { useMemo } from 'react';
import { Space, Avatar } from 'antd';

// assets
import usdt from '@/assets/usdt.png';

// utils
import { CryptoEnum, cryptoEnumOptions } from '@/utils';

// components
import { Txt } from '../TypographyMaster';

interface ITagCryptoTypeProps {
  cryptoType: CryptoEnum;
  includeName?: boolean;
  customSize?: number;
}

const TagCryptoType: React.FunctionComponent<ITagCryptoTypeProps> = (props) => {
  // props
  const { cryptoType, includeName = true, customSize } = props || {};

  // compute
  const src = (() => {
    if (cryptoType === CryptoEnum.TRC20_USDT) return usdt;
    if (cryptoType === CryptoEnum.ERC20_USDT) return usdt;
    return '';
  })();
  const cryptoOption = useMemo(() => {
    return cryptoEnumOptions.find((findC) => findC.value === cryptoType);
  }, [cryptoType]);

  return (
    <Space>
      <Avatar
        {...{ src }}
        size={customSize ?? 'small'}
      />
      {includeName && <Txt>{cryptoOption?.label}</Txt>}
    </Space>
  );
};

export default TagCryptoType;
