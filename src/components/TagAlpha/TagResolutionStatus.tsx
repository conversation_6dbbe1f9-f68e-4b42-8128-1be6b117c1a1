// libs
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// utils
import { AnomalyResolutionStatusEnum } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';
import { Txt } from '../TypographyMaster';

interface ITagResolutionStatusProps extends ITagAlphaProps {
  status: AnomalyResolutionStatusEnum;
}

const TagResolutionStatus: React.FunctionComponent<ITagResolutionStatusProps> = (props) => {
  // props
  const { status, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('options');

  // compute
  const label = useMemo(() => {
    if (status === AnomalyResolutionStatusEnum.Pending) return t('pendingAnomalyResolutionStatus');
    if (status === AnomalyResolutionStatusEnum.InProgress) return t('inProgressAnomalyResolutionStatus');
    if (status === AnomalyResolutionStatusEnum.Completed) return t('completedAnomalyResolutionStatus');
    if (status === AnomalyResolutionStatusEnum.Failed) return t('failedAnomalyResolutionStatus');
    return t('undefined');
  }, [status, t]);
  const color = useMemo(() => {
    if (status === AnomalyResolutionStatusEnum.Pending) return 'bg-orange-500';
    if (status === AnomalyResolutionStatusEnum.InProgress) return 'bg-purple-500';
    if (status === AnomalyResolutionStatusEnum.Completed) return 'bg-green-500';
    if (status === AnomalyResolutionStatusEnum.Failed) return 'bg-red-500';
    return 'bg-grey-500';
  }, [status]);

  return (
    <TagAlpha
      className={`${color} ${className}`}
      {...tagProps}
    >
      <Txt className='text-white'>{label}</Txt>
    </TagAlpha>
  );
};

export default TagResolutionStatus;
export type { ITagResolutionStatusProps };
