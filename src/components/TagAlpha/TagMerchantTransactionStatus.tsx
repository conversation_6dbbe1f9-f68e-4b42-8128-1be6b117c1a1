// libs
import { useCallback, useMemo } from 'react';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  CloudServerOutlined,
  FileExcelOutlined,
  WarningOutlined,
  StopOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined,
  CloudUploadOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

// utils
import { MerchantTransactionStatus } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagMerchantTxStatusProps extends Omit<ITagAlphaProps, 'onClick'> {
  status: MerchantTransactionStatus | undefined | null;
  onClick?: (status: ITagMerchantTxStatusProps['status']) => void;
  tooltip?: string;
}

const TagMerchantTxStatus: React.FunctionComponent<ITagMerchantTxStatusProps> = (props) => {
  // props
  const { status, onClick, tooltip, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('tagMerchantTransactionStatus');

  // compute
  const icon = useMemo(() => {
    switch (status) {
      case MerchantTransactionStatus.Retry:
        return <ReloadOutlined />;
      case MerchantTransactionStatus.Created:
        return <LoadingOutlined />;
      case MerchantTransactionStatus.BlockchainBroadcast:
        return <CloudUploadOutlined />;
      case MerchantTransactionStatus.BlockchainConfirmed:
        return <CloudServerOutlined />;
      case MerchantTransactionStatus.Completed:
        return <CheckCircleOutlined />;
      case MerchantTransactionStatus.Canceled:
        return <FileExcelOutlined />;
      case MerchantTransactionStatus.Timeout:
        return <ClockCircleOutlined />;
      case MerchantTransactionStatus.CallbackFailed:
        return <ExclamationCircleOutlined />;
      case MerchantTransactionStatus.BlockchainFailed:
        return <CloseCircleOutlined />;
      case MerchantTransactionStatus.OtherError:
        return <WarningOutlined />;

      default:
        return <StopOutlined />;
    }
  }, [status]);
  const color = useMemo(() => {
    switch (status) {
      case MerchantTransactionStatus.Retry:
        return 'volcano';
      case MerchantTransactionStatus.Created:
        return 'orange';
      case MerchantTransactionStatus.BlockchainBroadcast:
        return 'gold';
      case MerchantTransactionStatus.BlockchainConfirmed:
        return 'cyan';
      case MerchantTransactionStatus.Completed:
        return 'success';
      case MerchantTransactionStatus.Canceled:
        return 'red';
      case MerchantTransactionStatus.Timeout:
        return 'error';
      case MerchantTransactionStatus.CallbackFailed:
        return 'purple';
      case MerchantTransactionStatus.BlockchainFailed:
        return 'geekblue';
      case MerchantTransactionStatus.OtherError:
        return 'magenta';

      default:
        return 'default';
    }
  }, [status]);
  const label = useMemo(() => {
    switch (status) {
      case MerchantTransactionStatus.Retry:
        return t('retry');
      case MerchantTransactionStatus.Created:
        return t('created');
      case MerchantTransactionStatus.BlockchainBroadcast:
        return t('blockchainBroadcast');
      case MerchantTransactionStatus.BlockchainConfirmed:
        return t('blockchainConfirmed');
      case MerchantTransactionStatus.Completed:
        return t('completed');
      case MerchantTransactionStatus.Canceled:
        return t('canceled');
      case MerchantTransactionStatus.Timeout:
        return t('timeout');
      case MerchantTransactionStatus.CallbackFailed:
        return t('callbackFailed');
      case MerchantTransactionStatus.BlockchainFailed:
        return t('blockchainFailed');
      case MerchantTransactionStatus.OtherError:
        return t('otherError');

      default:
        return t('undefined');
    }
  }, [status, t]);

  // handlers
  const handleClick = useCallback(() => {
    if (onClick) onClick(status);
  }, [onClick, status]);

  return (
    <TagAlpha
      className={onClick ? 'cursor-pointer' : ''}
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagMerchantTxStatus;
export type { ITagMerchantTxStatusProps };
