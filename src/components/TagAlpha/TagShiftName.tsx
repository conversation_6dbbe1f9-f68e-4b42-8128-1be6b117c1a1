// libs
import { useCallback, useMemo } from 'react';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { SunOutlined, CloudOutlined, MoonOutlined, LoadingOutlined } from '@ant-design/icons';

// utils
import { ShiftNameEnum } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagShiftNameProps extends Omit<ITagAlphaProps, 'onClick'> {
  shiftName: ShiftNameEnum | undefined | null;
  onClick?: (shift: ITagShiftNameProps['shiftName']) => void;
  tooltip?: string;
}

const TagShiftName: React.FunctionComponent<ITagShiftNameProps> = (props) => {
  // props
  const { shiftName, onClick, tooltip, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('options');

  // compute
  const icon = useMemo(() => {
    if (shiftName === ShiftNameEnum.Morning) return <SunOutlined />;
    if (shiftName === ShiftNameEnum.Afternoon) return <CloudOutlined />;
    if (shiftName === ShiftNameEnum.Night) return <MoonOutlined />;
    return <LoadingOutlined />;
  }, [shiftName]);
  const color = useMemo(() => {
    if (shiftName === ShiftNameEnum.Morning) return 'processing';
    if (shiftName === ShiftNameEnum.Afternoon) return 'warning';
    if (shiftName === ShiftNameEnum.Night) return 'cyan';
    return 'default';
  }, [shiftName]);
  const label = useMemo(() => {
    if (shiftName === ShiftNameEnum.Morning) return t('morningShiftName');
    if (shiftName === ShiftNameEnum.Afternoon) return t('afternoonShiftName');
    if (shiftName === ShiftNameEnum.Night) return t('nightShiftName');
    return t('undefined');
  }, [shiftName, t]);

  // handlers
  const handleClick = useCallback(() => {
    if (onClick) onClick(shiftName);
  }, [onClick, shiftName]);

  return (
    <TagAlpha
      className='flex w-full cursor-pointer'
      onClick={handleClick}
      color={color}
      {...tagProps}
    >
      <div
        className=''
        style={{ width: '20%' }}
      >
        {icon}
      </div>

      <Tooltip title={tooltip}>
        <div
          className='text-center'
          style={{ width: '200%' }}
        >
          {label}
        </div>
      </Tooltip>
    </TagAlpha>
  );
};

export default TagShiftName;
export type { ITagShiftNameProps };
