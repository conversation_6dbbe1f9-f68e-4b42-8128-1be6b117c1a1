import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { TagProps } from 'antd';
import TagAlpha from './TagAlpha';

interface ITagActivation extends TagProps {
  activated?: boolean;
  children?: ReactNode;
}

const TagActivation = (props: ITagActivation) => {
  const { activated = false, children, ...tagProps } = props || {};

  const { t } = useTranslation('tagActivation');

  return activated ? (
    <TagAlpha
      color='success'
      icon={<CheckCircleOutlined />}
      {...tagProps}
    >
      {children || t('activated')}
    </TagAlpha>
  ) : (
    <TagAlpha
      color='error'
      icon={<CloseCircleOutlined />}
      {...tagProps}
    >
      {children || t('deactivated')}
    </TagAlpha>
  );
};

export default TagActivation;
