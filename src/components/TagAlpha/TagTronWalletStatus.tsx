// libs
import { useCallback, useMemo } from 'react';
// prettier-ignore
import { Tooltip } from 'antd';
import { FileProtectOutlined, HourglassOutlined, LikeOutlined, LoadingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// utils
import { TronWalletStatusEnum, tronWalletStatusObj } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagTronWalletStatusProps extends Omit<ITagAlphaProps, 'onClick'> {
  status: TronWalletStatusEnum | undefined | null;
  onClick?: (status: ITagTronWalletStatusProps['status']) => void;
  tooltip?: string;
}

const TagTronWalletStatus: React.FunctionComponent<ITagTronWalletStatusProps> = (props) => {
  // props
  const { status, onClick, tooltip, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('options');

  // compute
  const icon = useMemo(() => {
    if (status === TronWalletStatusEnum.Available) return <LikeOutlined />;
    if (status === TronWalletStatusEnum.Transaction) return <FileProtectOutlined />;
    if (status === TronWalletStatusEnum.Cooldown) return <HourglassOutlined />;
    return <LoadingOutlined />;
  }, [status]);
  const color = useMemo(() => {
    if (status === TronWalletStatusEnum.Available) return 'processing';
    if (status === TronWalletStatusEnum.Transaction) return 'warning';
    if (status === TronWalletStatusEnum.Cooldown) return 'cyan';
    return 'default';
  }, [status]);
  const label = useMemo(() => {
    if (status === TronWalletStatusEnum.Available) return t(tronWalletStatusObj.Available.label);
    if (status === TronWalletStatusEnum.Transaction) return t(tronWalletStatusObj.Transaction.label);
    if (status === TronWalletStatusEnum.Cooldown) return t(tronWalletStatusObj.Cooldown.label);
    return t('undefined');
  }, [status, t]);

  // handlers
  const handleClick = useCallback(() => {
    if (onClick) onClick(status);
  }, [onClick, status]);

  return (
    <TagAlpha
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagTronWalletStatus;
export type { ITagTronWalletStatusProps };
