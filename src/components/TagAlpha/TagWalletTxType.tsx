// libs
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';
import { Txt } from '../TypographyMaster';

interface ITagWalletTxTypeProps extends ITagAlphaProps {
  type: number;
}

const TagWalletTxType: React.FunctionComponent<ITagWalletTxTypeProps> = (props) => {
  // props
  const { type, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('tagWalletTxType');

  // compute
  const label = useMemo(() => {
    if (type === 0) return t('transferIn');
    if (type === 1) return t('transferOut');
    if (type === 2) return t('concentration');
    if (type === 3) return t('merchantTransfer');
    if (type === 4) return t('merchantTransferOut');
    if (type === 5) return t('instoreTransfer');
    if (type === 6) return t('anomalyTransfer');

    return t('undefined');
  }, [type, t]);
  const color = useMemo(() => {
    if (type === 0) return 'geekblue-inverse';
    if (type === 1) return 'purple-inverse';
    if (type === 2) return 'cyan-inverse';
    if (type === 3) return 'green-inverse';
    if (type === 4) return 'pink-inverse';
    if (type === 5) return 'lime-inverse';
    if (type === 6) return 'error';
    return 'default';
  }, [type]);

  return (
    <TagAlpha
      className={`${className}`}
      {...{ color }}
      {...tagProps}
    >
      <Txt>{label}</Txt>
    </TagAlpha>
  );
};

export default TagWalletTxType;
export type { ITagWalletTxTypeProps };
