// libs
import { HtmlHTMLAttributes, Key } from 'react';

// utils
import { OptionBase, ShiftNameEnum } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

type SelectTagOption = OptionBase;

interface ITagSelectsProps extends HtmlHTMLAttributes<HTMLDivElement> {
  options: Array<SelectTagOption>;
  activeKeys: Array<Key>;
  setActiveKeys: ReactSet<ITagSelectsProps['activeKeys']>;
  tagsProps?: Omit<ITagAlphaProps, 'onClick'> & {
    onClick: (key: Key) => void;
  };
  isDark?: boolean;
  shiftOrders?: ShiftNameEnum[];
}

const TagSelects: React.FunctionComponent<ITagSelectsProps> = (props) => {
  // props
  const { options, activeKeys, isDark, setActiveKeys, tagsProps, shiftOrders = [], ...containerProps } = props || {};
  const { onClick, className, ...otherTagsProps } = tagsProps || {};

  return (
    <div {...containerProps}>
      {options
        .sort((a, b) => {
          const aIndex = shiftOrders.findIndex((shift) => shift === a.value);
          const bIndex = shiftOrders.findIndex((shift) => shift === b.value);

          return aIndex - bIndex;
        })
        .map((item) => {
          const { value, label } = item;
          const isActive = activeKeys.includes(value);
          const key = (value ?? label) as Key;
          const bgColor = (() => {
            if (!isActive) return '';
            if (isDark) return 'bg-green-800';
            return 'bg-green-300';
          })();

          return (
            <TagAlpha
              key={key}
              className={`m-0 w-full cursor-pointer ${bgColor} text-center ${className}`}
              onClick={() => {
                if (isActive) setActiveKeys((pre) => pre.filter((filterPre) => filterPre !== value));
                else setActiveKeys((pre) => [...pre, value]);
                if (onClick) onClick(key);
              }}
              {...otherTagsProps}
            >
              {label}
            </TagAlpha>
          );
        })}
    </div>
  );
};

export default TagSelects;
export type { SelectTagOption, ITagSelectsProps };
