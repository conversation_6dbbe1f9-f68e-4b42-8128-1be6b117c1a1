// libs
import { useCallback, useMemo } from 'react';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  CloudServerOutlined,
  FileExcelOutlined,
  FrownOutlined,
  LoadingOutlined,
  RedoOutlined,
  RetweetOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

// hooks
import { TxStatusNum } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagTxStatusProps extends Omit<ITagAlphaProps, 'onClick'> {
  status: TxStatusNum | undefined | null;
  onClick?: (status: ITagTxStatusProps['status']) => void;
  tooltip?: string;
}

const TagTxStatus: React.FunctionComponent<ITagTxStatusProps> = (props) => {
  // props
  const { status, onClick, tooltip, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('tagTxStatus');

  // compute
  const icon = useMemo(() => {
    if (status === TxStatusNum.WaitingForApproval) return <UserOutlined />;
    if (status === TxStatusNum.Created) return <LoadingOutlined />;
    if (status === TxStatusNum.Broadcasted) return <RetweetOutlined />;
    if (status === TxStatusNum.Confirmed) return <CloudServerOutlined />;
    if (status === TxStatusNum.Completed) return <CheckCircleOutlined />;
    if (status === TxStatusNum.Canceled) return <FileExcelOutlined />;
    if (status === TxStatusNum.Timeout) return <ClockCircleOutlined />;
    if (status === TxStatusNum.Retry) return <RedoOutlined />;
    if (status === TxStatusNum.MerchantCallbackFailed) return <FrownOutlined />;
    if (status === TxStatusNum.BlockchainTransactionFailed) return <CloseCircleOutlined />;
    return <LoadingOutlined />;
  }, [status]);
  const color = useMemo(() => {
    if (status === TxStatusNum.WaitingForApproval) return 'gold';
    if (status === TxStatusNum.Created) return 'processing';
    if (status === TxStatusNum.Broadcasted) return 'warning';
    if (status === TxStatusNum.Confirmed) return 'cyan';
    if (status === TxStatusNum.Completed) return 'success';
    if (status === TxStatusNum.Canceled) return 'volcano';
    if (status === TxStatusNum.Timeout) return 'error';
    if (status === TxStatusNum.Retry) return 'magenta';
    if (status === TxStatusNum.MerchantCallbackFailed) return 'purple';
    if (status === TxStatusNum.BlockchainTransactionFailed) return 'geekblue';
    return 'default';
  }, [status]);
  const label = useMemo(() => {
    if (status === TxStatusNum.WaitingForApproval) return t('waitingForApproval'); // Label for WaitingForApproval
    if (status === TxStatusNum.Created) return t('created');
    if (status === TxStatusNum.Broadcasted) return t('broadcasted');
    if (status === TxStatusNum.Confirmed) return t('confirmed');
    if (status === TxStatusNum.Completed) return t('completed');
    if (status === TxStatusNum.Canceled) return t('canceled');
    if (status === TxStatusNum.Timeout) return t('timeout');
    if (status === TxStatusNum.Retry) return t('retry');
    if (status === TxStatusNum.MerchantCallbackFailed) return t('merchantCallbackFailed');
    if (status === TxStatusNum.BlockchainTransactionFailed) return t('blockchainTransactionFailed');
    return t('undefined');
  }, [status, t]);

  // handlers
  const handleClick = useCallback(() => {
    if (onClick) onClick(status);
  }, [onClick, status]);

  return (
    <TagAlpha
      className={`cursor-pointer ${className}`}
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagTxStatus;
export type { ITagTxStatusProps };
