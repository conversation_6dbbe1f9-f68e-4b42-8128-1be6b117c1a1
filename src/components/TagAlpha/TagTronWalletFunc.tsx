// libs
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// utils
import { TronWalletFuncEnum } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagTronWalletFuncProps extends ITagAlphaProps {
  walletFunc: TronWalletFuncEnum;
}
const TagTronWalletFunc: React.FunctionComponent<ITagTronWalletFuncProps> = (props) => {
  // props
  const { walletFunc, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('options');

  // compute
  const label = useMemo(() => {
    if (walletFunc === TronWalletFuncEnum.Collection) return t('collectionTronWalletFunc');
    if (walletFunc === TronWalletFuncEnum.Stake) return t('stakeTronWalletFunc');
    if (walletFunc === TronWalletFuncEnum.MerchantPaymentWallet) return t('merchantPaymentWalletTronWalletFunc');
    if (walletFunc === TronWalletFuncEnum.SupervisorWallet) return t('supervisorWalletTronWalletFunc');
    if (walletFunc === TronWalletFuncEnum.EnergyLease) return t('energyLeaseWalletTronWalletFunc');
    return t('undefined');
  }, [walletFunc, t]);
  const color = useMemo(() => {
    if (walletFunc === TronWalletFuncEnum.Collection) return '#1677ff';
    if (walletFunc === TronWalletFuncEnum.Stake) return '#13c2c2';
    if (walletFunc === TronWalletFuncEnum.MerchantPaymentWallet) return '#a0d911';
    if (walletFunc === TronWalletFuncEnum.SupervisorWallet) return '#722ed1';
    if (walletFunc === TronWalletFuncEnum.EnergyLease) return '#52c41a';
    return 'default';
  }, [walletFunc]);

  return (
    <TagAlpha
      className={` ${className}`}
      {...{ color }}
      {...tagProps}
    >
      {label}
    </TagAlpha>
  );
};

export default TagTronWalletFunc;
export type { ITagTronWalletFuncProps };
