import { useCallback, useMemo } from 'react';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  InboxOutlined,
  LoadingOutlined,
  RedoOutlined,
  RollbackOutlined,
  StopOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { LeaseOutOrderStatusEnum, leaseOutOrderStatusOptions } from '@/utils';
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagLeaseOutStatus extends Omit<ITagAlphaProps, 'onClick'> {
  status: LeaseOutOrderStatusEnum | undefined | null;
  onClick?: (status: ITagLeaseOutStatus['status']) => void;
  tooltip?: string;
}

const TagLeaseOutStatus: React.FunctionComponent<ITagLeaseOutStatus> = (props) => {
  const { status, onClick, tooltip, className, ...tagProps } = props || {};

  const { t } = useTranslation('options');

  const icon = useMemo(() => {
    if (status === LeaseOutOrderStatusEnum.Received) return <InboxOutlined />;
    if (status === LeaseOutOrderStatusEnum.Delegating) return <LoadingOutlined />;
    if (status === LeaseOutOrderStatusEnum.DelegateSuccess) return <CheckCircleOutlined />;
    if (status === LeaseOutOrderStatusEnum.DelegateFail) return <CloseCircleOutlined />;
    if (status === LeaseOutOrderStatusEnum.Reclaiming) return <RedoOutlined />;
    if (status === LeaseOutOrderStatusEnum.ReclaimSuccess) return <RollbackOutlined />;
    if (status === LeaseOutOrderStatusEnum.ReclaimFail) return <StopOutlined />;
    if (status === LeaseOutOrderStatusEnum.InvalidTrxAmount) return <ExclamationCircleOutlined />;
    if (status === LeaseOutOrderStatusEnum.InsufficientEnergy) return <ThunderboltOutlined />;

    return <ExclamationCircleOutlined />;
  }, [status]);
  const color = useMemo(() => {
    if (status === LeaseOutOrderStatusEnum.Received) return 'geekblue';
    if (status === LeaseOutOrderStatusEnum.Delegating) return 'procressing';
    if (status === LeaseOutOrderStatusEnum.DelegateSuccess) return 'success';
    if (status === LeaseOutOrderStatusEnum.DelegateFail) return 'error';
    if (status === LeaseOutOrderStatusEnum.Reclaiming) return 'warning';
    if (status === LeaseOutOrderStatusEnum.ReclaimSuccess) return 'success';
    if (status === LeaseOutOrderStatusEnum.ReclaimFail) return 'error';
    if (status === LeaseOutOrderStatusEnum.InvalidTrxAmount) return 'error';
    if (status === LeaseOutOrderStatusEnum.InsufficientEnergy) return 'warning';

    return 'default';
  }, [status]);
  const label = useMemo(
    () => t(leaseOutOrderStatusOptions.find((option) => option.value === status)?.label || ''),
    [t, status],
  );

  const handleClick = useCallback(() => {
    if (onClick) onClick(status);
  }, [onClick, status]);

  return (
    <TagAlpha
      className={className}
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagLeaseOutStatus;
export type { ITagLeaseOutStatus };
