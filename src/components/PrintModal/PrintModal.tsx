// libs
import { MutableRefObject } from 'react';
import { useReactToPrint } from 'react-to-print';
import { useTranslation } from 'react-i18next';

// components
import ModalAlpha from '../ModalAlpha';
import BtnFuncs from '../BtnFuncs';
import { Title } from '../TypographyMaster';

const DEFAULT_PRINT_MARGIN = 1.2;

// used for print columns selection
interface Option {
  label: string;
  value: string;
}

interface IPrintModalProps {
  open: boolean;
  setOpen: ReactSet<IPrintModalProps['open']>;
  documentTitle?: string; // Title for printing when save as a file.
  contentRef: MutableRefObject<null>; // `ref` of an element will be printed.
  margin?: number; // Margin for printing; the default is 1.2 cm.
  children: React.ReactNode;
}

const PrintModal: React.FunctionComponent<IPrintModalProps> = (props) => {
  // props
  const { open, setOpen, documentTitle, contentRef, margin, children } = props || {};
  const { t } = useTranslation('printModal');

  // hooks
  const reactToPrintFn = useReactToPrint({
    content: () => contentRef.current,
    documentTitle,
    pageStyle: `@page { margin: ${margin || DEFAULT_PRINT_MARGIN}cm`,
  });

  // handlers
  const handlePrint = () => {
    reactToPrintFn();
  };

  return (
    <ModalAlpha
      maximize
      title={<Title level={3}>{t('title')}</Title>}
      footer={false}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
    >
      <BtnFuncs
        type='primary'
        shape='round'
        onClick={handlePrint}
        className='mb-4 ml-auto block'
      >
        {t('print')}
      </BtnFuncs>
      <section>{children}</section>
    </ModalAlpha>
  );
};

export default PrintModal;
export type { IPrintModalProps, Option };
