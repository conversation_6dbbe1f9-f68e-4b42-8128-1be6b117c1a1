// libs
import { MutableRefObject, useCallback, useMemo, useState } from 'react';
import { Checkbox, TableColumnsType } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { DepositAnomalyItemInterface } from '@/api/order';

// components
import TableAlpha from '../TableAlpha';
import { Txt } from '../TypographyMaster';
import { Option } from './PrintModal';

interface IPrintAbDepModal {
  columns: TableColumnsType<DepositAnomalyItemInterface>;
  dataSource: DepositAnomalyItemInterface[];
  summary: (pageData: readonly any[]) => JSX.Element | undefined;
  contentRef: MutableRefObject<null>; // `ref` of an element will be printed.
}

const PrintAbnormalDeposit: React.FunctionComponent<IPrintAbDepModal> = (props) => {
  // props
  const { columns: originalColumns, dataSource, summary, contentRef } = props || {};
  const { t } = useTranslation('printModal');

  // states
  const [columns, setColumns] = useState(originalColumns); // Used to render only the selected columns.

  // compute
  const printOptions = useMemo(
    () => originalColumns.map((column) => ({ label: column.title, value: column.key })) as Option[],
    [originalColumns],
  );
  const defaultPrintOptions = useMemo(() => printOptions.map((printOption) => printOption.value), [printOptions]); // Used to set all columns as selected.

  // handlers
  const handleSelectPrintOptions = useCallback(
    (checked: string[]) => {
      setColumns(originalColumns.filter((column) => checked.includes(column.key!.toString())));
    },
    [originalColumns],
  );

  return (
    <>
      <div className='mb-4'>
        <Txt className='mr-2 font-semibold'>{t('label')}</Txt>
        <Checkbox.Group
          options={printOptions}
          defaultValue={defaultPrintOptions}
          onChange={handleSelectPrintOptions}
        />
      </div>

      <div
        ref={contentRef} // The element that holds this `ref` will be printed.
        className='border'
      >
        <TableAlpha
          {...{ columns, dataSource, summary }}
          size='small'
          rowKey='hash'
          pagination={false}
        />
      </div>
    </>
  );
};

export default PrintAbnormalDeposit;
export type { IPrintAbDepModal };
