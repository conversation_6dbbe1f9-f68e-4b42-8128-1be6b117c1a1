// libs
import { MutableRefObject, useCallback, useMemo, useState } from 'react';
import { Checkbox, TableColumnsType } from 'antd';
import { useTranslation } from 'react-i18next';

// hooks
import { DepositRowInterface } from '@/pages/deposit/useDepositColumns';

// components
import TableAlpha from '../TableAlpha';
import { Txt } from '../TypographyMaster';
import { Option } from './PrintModal';

interface IPrintDepositProps {
  columns: TableColumnsType<DepositRowInterface>;
  dataSource: DepositRowInterface[];
  summary: (pageData: readonly any[]) => JSX.Element | undefined;
  contentRef: MutableRefObject<null>; // `ref` of an element will be printed.
}

const PrintDeposit: React.FunctionComponent<IPrintDepositProps> = (props) => {
  // props
  const { columns: originalColumns, dataSource, summary, contentRef } = props || {};

  // states
  const [columns, setColumns] = useState(originalColumns); // Used to render only the selected columns.

  // hooks
  const { t } = useTranslation('printModal');

  // compute
  const printOptions = useMemo(
    () => originalColumns.map((column) => ({ label: column.title, value: column.key })) as Option[],
    [originalColumns],
  );
  const defaultPrintOptions = useMemo(() => printOptions.map((printOption) => printOption.value), [printOptions]); // Used to set all columns as selected.

  // handlers
  const handleSelectPrintOptions = useCallback(
    (checked: string[]) => {
      setColumns(originalColumns.filter((column) => checked.includes(column.key!.toString())));
    },
    [originalColumns],
  );

  return (
    <>
      <div className='mb-4'>
        <Txt className='mr-2 font-semibold'>{t('label')}:</Txt>
        <Checkbox.Group
          options={printOptions}
          defaultValue={defaultPrintOptions}
          onChange={handleSelectPrintOptions}
        />
      </div>

      <div
        ref={contentRef} // The element that holds this `ref` will be printed.
        className='border'
      >
        <TableAlpha
          {...{ columns, dataSource, summary }}
          size='small'
          rowKey='key'
          pagination={false}
        />
      </div>
    </>
  );
};

export default PrintDeposit;
export type { IPrintDepositProps };
