// libs
import { MutableRefObject, useCallback, useMemo, useState } from 'react';
import { Checkbox, TableColumnsType } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { IWalletRecordItem } from '@/api';

// hooks
import { IWhRow } from '@/pages/wallet/WalletRecord/useWhColumns';
import useWhSummary from '@/pages/wallet/WalletRecord/useWhSummary';

// components
import TableAlpha from '../TableAlpha';
import { Txt } from '../TypographyMaster';
import { Option } from './PrintModal';

interface IPrintWalletHistoryProps {
  columns: TableColumnsType<IWalletRecordItem>;
  dataSource: IWalletRecordItem[];
  selectedRows: IWhRow[];
  contentRef: MutableRefObject<null>; // `ref` of an element will be printed.
}

const PrintWalletHistory: React.FunctionComponent<IPrintWalletHistoryProps> = (props) => {
  // props
  const { columns: originalColumns, dataSource, selectedRows, contentRef } = props || {};

  // states
  const [columns, setColumns] = useState(originalColumns); // Used to render only the selected columns.

  // hooks
  const { t } = useTranslation('printModal');

  // compute
  const printOptions = useMemo(
    () => originalColumns.map((column) => ({ label: column.title, value: column.key })) as Option[],
    [originalColumns],
  );
  const summary = useWhSummary({ dataSource, columns, selectedRows });
  const defaultPrintOptions = useMemo(() => printOptions.map((printOption) => printOption.value), [printOptions]); // Used to set all columns as selected.

  // handlers
  const handleSelectPrintOptions = useCallback(
    (checked: string[]) => {
      setColumns(originalColumns.filter((column) => checked.includes(column.key!.toString())));
    },
    [originalColumns],
  );

  return (
    <>
      <div className='mb-4'>
        <Txt className='mr-2 font-semibold'>{t('label')}</Txt>
        <Checkbox.Group
          options={printOptions}
          defaultValue={defaultPrintOptions}
          onChange={handleSelectPrintOptions}
        />
      </div>

      <div
        ref={contentRef} // The element that holds this `ref` will be printed.
        className='border'
      >
        <TableAlpha
          {...{ dataSource, columns, summary }}
          size='small'
          pagination={false}
        />
      </div>
    </>
  );
};

export default PrintWalletHistory;
export type { IPrintWalletHistoryProps };
