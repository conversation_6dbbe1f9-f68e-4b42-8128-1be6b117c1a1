import { Space } from 'antd';
import { SpaceProps } from 'antd/lib';
import dayjs from 'dayjs';
import { dateFormator } from '@/hooks';
import { useUserStore } from '@/store';
import { Txt } from '../TypographyMaster';

type LogOptions = {
  createdAt: string | null;
  log: string;
};

interface ILogListProps extends SpaceProps {
  logs: Array<LogOptions> | undefined;
}

const LogList: React.FunctionComponent<ILogListProps> = (props) => {
  // props
  const { logs, ...spaceProps } = props || {};

  // hooks
  const { isDark } = useUserStore();

  return (
    <Space
      direction='vertical'
      size='small'
      {...spaceProps}
    >
      {logs?.length ? (
        logs?.map((logInfo) => {
          const { createdAt, log } = logInfo;
          const createFormat = createdAt ? dayjs(createdAt).format(dateFormator.accurate) : 'Time';
          return (
            <Space
              key={log}
              className={`flex-wrap items-start gap-y-0 transition-colors duration-200
              ${isDark ? 'border-stone-200' : 'border-stone-800'}
              ${isDark ? 'hover:border-cyan-300' : 'hover:border-cyan-700'}
            `}
            >
              <Txt
                type='secondary'
                className='text-nowrap'
              >
                {createFormat}:
              </Txt>
              {log.split(' ').map((text, index) => {
                const key = `${text}${index}`;
                return (
                  <Txt
                    className={`text-nowrap select-none transition-colors duration-200
                      ${isDark ? 'text-stone-200' : 'text-stone-800'}
                      ${isDark ? 'hover:text-orange-300' : 'hover:text-orange-700'}
                    `}
                    key={key}
                  >
                    {text}
                  </Txt>
                );
              })}
            </Space>
          );
        })
      ) : (
        <Space
          className={`items-start border-b transition-colors duration-200
            ${isDark ? 'border-stone-200' : 'border-stone-800'}
            ${isDark ? 'hover:border-cyan-300' : 'hover:border-cyan-700'}
          `}
        >
          {['查', '無', 'L', 'o', 'g'].map((text, index) => {
            const key = `${text}-${index}`;
            return (
              <Txt
                className={`text-nowrap select-none transition-colors duration-200
                  ${isDark ? 'text-stone-200' : 'text-stone-800'}
                  ${isDark ? 'hover:text-orange-300' : 'hover:text-orange-700'}
                `}
                key={key}
              >
                {text}
              </Txt>
            );
          })}
        </Space>
      )}
    </Space>
  );
};

export default LogList;
export type { LogOptions, ILogListProps };
