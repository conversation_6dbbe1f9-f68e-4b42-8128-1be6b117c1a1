// libs
import { useCallback, useEffect, useState } from 'react';
import { Flex, Input, Space } from 'antd';
import { FlexProps, InputProps } from 'antd/lib';
import { useTranslation } from 'react-i18next';

// components
import { Txt } from './TypographyMaster';
import BtnFuncs from './BtnFuncs';

interface ISearchMasterProps extends Omit<InputProps, 'onSearch'> {
  titles: Array<{ label: React.ReactNode; key: string }>;
  onSearch?: (values: { [key in ISearchMasterProps['titles'][number]['key']]: string }) => void;
  containerProps?: FlexProps;
  defaultValues?: { [key: string]: string };
}

const SearchMaster: React.FunctionComponent<ISearchMasterProps> = (props) => {
  // props
  const { titles, onSearch, defaultValues, containerProps, ...searchProps } = props || {};

  // states
  const [values, setValues] = useState<{ [key in ISearchMasterProps['titles'][number]['key']]: string }>(
    titles.reduce(
      (preValues, current) => ({ ...preValues, [current.key]: defaultValues ? defaultValues[current.key] : '' }),
      {},
    ),
  );

  // hooks
  const { t } = useTranslation('searchMaster');

  // side effects
  useEffect(() => {
    setValues(
      titles.reduce(
        (preValues, current) => ({ ...preValues, [current.key]: defaultValues ? defaultValues[current.key] : '' }),
        {},
      ),
    );
  }, [defaultValues]);
  const handleSearch = useCallback(() => {
    if (onSearch) onSearch(values);
  }, [onSearch, values]);

  return (
    <Flex
      gap={8}
      className={`items-end ${containerProps?.className} flex-wrap`}
      {...containerProps}
    >
      {titles.map((mapT, index) => {
        const key = `${mapT.key}-${index}`;
        return (
          <Space
            key={key}
            size='small'
            className='flex flex-col items-start gap-y-1'
          >
            <Txt
              strong
              type='secondary'
            >
              {mapT.label}:
            </Txt>
            <Input
              placeholder={t('placeholder')}
              allowClear
              variant='filled'
              onKeyUp={(e) => {
                if (e.key === 'Enter') handleSearch();
              }}
              value={values[mapT.key]}
              onChange={(e) => {
                setValues((pre) => ({ ...pre, [mapT.key]: e.target.value }));
              }}
              {...searchProps}
            />
          </Space>
        );
      })}
      <BtnFuncs
        onClick={() => {
          if (onSearch) onSearch(values);
        }}
        iconType='search'
        type='primary'
        shape='circle'
      />
    </Flex>
  );
};

export default SearchMaster;
