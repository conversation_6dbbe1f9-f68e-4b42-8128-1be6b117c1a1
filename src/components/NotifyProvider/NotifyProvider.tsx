// libs
import { useEffect, useRef } from 'react';
import { MessageArgsProps, Typography } from 'antd';
import { CheckOutlined, CloseOutlined, WarningOutlined } from '@ant-design/icons';
import useMessage from 'antd/es/message/useMessage';

const { Text, Title } = Typography;

type NotifyBaseOptions = {
  title: string;
  des: string | undefined;
  useT?: boolean;
};

interface INotifyProviderProps {
  basicQue: Array<NotifyBaseOptions>;
  shiftBsicQue: (count?: number) => void;
  keepTime?: number;
  type: MessageArgsProps['type'];
}

const NotifyProvider: React.FunctionComponent<INotifyProviderProps> = (props) => {
  // props
  const { basicQue, shiftBsicQue, keepTime = 5 * 1000, type } = props || {};

  // hooks
  const [api, context] = useMessage();

  // 設定 error 的 que;
  const isSendingError = useRef(false);
  useEffect(() => {
    // 發送並阻擋
    if (basicQue.length < 1 || isSendingError.current) return;
    isSendingError.current = true;

    //
    const currentSnd = basicQue.at(0);
    if (type === 'error') {
      api.error({
        duration: keepTime / 1000,
        icon: <span />,
        content: (
          <div style={{ textAlign: 'left' }}>
            <Title
              className='tight'
              level={3}
            >
              <CloseOutlined style={{ color: 'red', marginRight: 5 }} />
              {currentSnd?.title}
            </Title>
            <hr />
            <Text>{currentSnd?.des}</Text>
          </div>
        ),
      });
    } else if (type === 'success') {
      api.success({
        duration: keepTime / 1000,
        icon: <span />,
        content: (
          <div style={{ textAlign: 'left' }}>
            <Title
              className='tight'
              level={3}
            >
              <CheckOutlined style={{ color: 'green', marginRight: 5 }} />
              {currentSnd?.title}
            </Title>
            <hr />
            <Text>{currentSnd?.des}</Text>
          </div>
        ),
      });
    } else {
      api.info({
        duration: keepTime / 1000,
        icon: <span />,
        content: (
          <div style={{ textAlign: 'left' }}>
            <Title
              className='tight'
              level={3}
            >
              <WarningOutlined style={{ color: 'orange', marginRight: 5 }} />
              {currentSnd?.title}
            </Title>
            <hr />
            <Text>{currentSnd?.des}</Text>
          </div>
        ),
      });
    }

    // 特定時間後 取消阻擋 更新successQue
    setTimeout(() => {
      isSendingError.current = false;
      shiftBsicQue(1);
    }, keepTime);
  }, [api, basicQue, keepTime, shiftBsicQue, type]);

  return <div>{context}</div>;
};

export default NotifyProvider;
export type { NotifyBaseOptions, INotifyProviderProps };
