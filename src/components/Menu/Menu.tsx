// libs
import { HtmlHTMLAttributes, memo, useEffect, useMemo, useRef, useState } from 'react';
import { Tabs } from 'antd';
import { TabsProps } from 'antd/lib';
import { Link, useLocation } from 'react-router-dom';
import { debounce } from 'lodash';

// store
import { useUserStore } from '@/store';

// components
import { Txt } from '../TypographyMaster';

type MenuOptions = {
  label: React.ReactNode;
  to: string;
  key: string;
};

interface IMenuProps extends Omit<TabsProps, 'items'> {
  prefix: string;
  items: Array<MenuOptions>;
  containerProps?: HtmlHTMLAttributes<HTMLDivElement>;
  vertical?: boolean;
}

const Menu: React.FunctionComponent<IMenuProps> = (props) => {
  // props
  const { prefix, items, containerProps, vertical, ...tabProps } = props || {};

  // states
  const [containerMinWidth, setContainerMinWidth] = useState<number>();
  const [activeKey, setActiveKey] = useState<string>();
  const [hoverKey, setHoverKey] = useState<string>();

  // refs
  const tabsRef = useRef<HTMLDivElement>(null);

  // hooks
  const location = useLocation();
  const { isDark } = useUserStore();

  // compute
  const vaildPath = useMemo(() => location.pathname.replace(prefix, ''), [location.pathname, prefix]);
  const tabItems = useMemo(() => {
    return items.reduce(
      (preItems, item) => {
        const { label, to, key } = item;
        const keyValid = key === '' ? 'index' : key;
        return [
          ...preItems,
          {
            key: keyValid,
            label: (
              <Link {...{ to }}>
                <Txt>{label}</Txt>
              </Link>
            ),
          },
        ];
      },
      [] as Array<{ key: string; label: React.ReactNode }>,
    );
  }, [items]);

  // === init === //
  // valid path to activeKey
  useEffect(() => {
    const dHandler = debounce(() => {
      const newKey = vaildPath === '' ? 'index' : vaildPath.split('/').at(1);
      setActiveKey(newKey);
    }, 100);
    dHandler();

    return () => {
      dHandler.cancel();
    };
  }, [setActiveKey, vaildPath]);

  // Keep container base width
  useEffect(() => {
    if (!tabsRef.current) return;
    setContainerMinWidth(tabsRef.current.scrollWidth + 5);
  }, [setContainerMinWidth]);

  if (vertical)
    return (
      <div {...containerProps}>
        <div
          ref={tabsRef}
          className='flex flex-col space-y-2'
        >
          {items.map((mapI) => {
            const { label, to, key } = mapI;
            const keyValid = key === '' ? 'index' : key;

            const isActive = keyValid === activeKey;
            const isHover = keyValid === hoverKey;
            return (
              <div
                key={keyValid}
                className='relative overflow-hidden rounded-se-lg px-4 py-2 shadow-md'
              >
                <Link
                  {...{ to }}
                  onMouseEnter={() => {
                    setHoverKey(key || 'index');
                  }}
                  onMouseLeave={() => {
                    setHoverKey(undefined);
                  }}
                >
                  <div>{label}</div>

                  <div
                    className={`
                    absolute left-0 top-0 h-full w-full bg-gradient-to-r
                    ${isDark ? 'to-green-1000 from-green-800' : 'from-green-300 to-green-100'}
                  `}
                  />
                  <div
                    className={`
                     absolute transition-all duration-300
                    ${isHover ? 'top-[10%]' : 'top-0'}
                    ${isHover ? 'left-[5%]' : 'left-0'}
                    ${isHover ? 'w-[90%]' : 'w-full'}
                    ${isHover ? 'h-[80%]' : 'h-[90%]'}
                    ${isHover ? 'rounded-lg' : 'rounded-[0px]'}
                    ${isDark ? 'bg-[#1f2326]' : 'bg-white'}
                    ${!isHover && isActive ? 'opacity-0' : 'opacity-100'}
                  `}
                  />
                  <div
                    className={`
                    absolute left-0 top-0 h-full w-full px-4 py-2
                    ${isDark ? 'text-white' : 'text-blakc'}
                  `}
                  >
                    {label}
                  </div>
                </Link>
              </div>
            );
          })}
        </div>
      </div>
    );

  return (
    <div
      style={{ minWidth: containerMinWidth }}
      {...containerProps}
    >
      <div ref={tabsRef}>
        <Tabs
          tabBarStyle={{ marginBottom: 0 }}
          items={tabItems}
          {...{ activeKey, ...tabProps }}
        />
      </div>
    </div>
  );
};

export default memo(Menu);
export type { MenuOptions, IMenuProps };
