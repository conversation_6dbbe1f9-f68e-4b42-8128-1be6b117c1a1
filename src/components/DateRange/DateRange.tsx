import { HtmlHTMLAttributes, useEffect, useMemo, useRef, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Button, DatePicker, Select, Tooltip } from 'antd';
import { CalendarOutlined, RightSquareOutlined, ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import isoWeek from 'dayjs/plugin/isoWeek';
import { useUserStore } from '@/store';
import { dateFormator } from '@/hooks';
import ModalAlpha from '../ModalAlpha/ModalAlpha';
import { Txt } from '../TypographyMaster';
import BtnFuncs from '../BtnFuncs';

dayjs.extend(isoWeek);

type DateRangeOptions = { from: Dayjs; to: Dayjs };

interface IDateRangeProps extends HtmlHTMLAttributes<HTMLButtonElement> {
  values?: DateRangeOptions;
  defaultValues?: DateRangeOptions;
  onValuesChange?: (newValues: DateRangeOptions) => void;
  onDateSubmit?: (values: DateRangeOptions) => void;
  inSubmit?: boolean;
  wrapProps?: HtmlHTMLAttributes<HTMLDivElement>;
  loading?: boolean;
}

const DateRange: React.FunctionComponent<IDateRangeProps> = (props) => {
  // props
  const { loading, wrapProps, inSubmit, defaultValues, values, onValuesChange, onDateSubmit, className, ...btnProps } =
    props || {};

  // states
  const [range, setRange] = useState<DateRangeOptions>(
    defaultValues || { from: dayjs().startOf('day'), to: dayjs().endOf('day') },
  );
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [inSubmitLoading, setInSubmitLoading] = useState(false);
  const [backgroundPosition, setBackgroundPosition] = useState(0);
  const [mode, setMode] = useState<'day' | 'week' | 'month'>('day');

  // hooks
  const { isDark } = useUserStore();
  const { t } = useTranslation('dateRange');

  // compute
  const rangeResult = useMemo(() => values || range, [range, values]);
  const { from, to } = rangeResult;
  const isLoadingMask = useMemo(() => loading || inSubmit || inSubmitLoading, [inSubmit, inSubmitLoading, loading]);

  // init
  useEffect(() => {
    if (onValuesChange) onValuesChange(range);
  }, [onValuesChange, range]);

  useEffect(() => {
    if (!isLoadingMask) return () => {};
    const changePositionInterval = setInterval(() => {
      setBackgroundPosition((pre) => {
        if (pre > 100) return 0;

        return pre + 1;
      });
    }, 20);

    return () => {
      clearInterval(changePositionInterval);
    };
  }, [isLoadingMask]);

  // refs
  const operateHistory = useRef<Array<typeof range>>([]);
  const tempRange = useRef(range);
  useEffect(() => {
    const lastRecord = operateHistory.current.at(-1);
    if (lastRecord?.from.isSame(range.from) && lastRecord?.to.isSame(range.to)) return;
    const currentIndex = operateHistory.current.findIndex(
      (findR) => findR.from.isSame(tempRange.current.from) && findR.to.isSame(tempRange.current.to),
    );
    operateHistory.current = operateHistory.current.slice(0, currentIndex + 1).concat([range]);
    tempRange.current = range;
  }, [range]);

  // ctrl + z
  const downKeys = useRef<Array<string>>([]);
  useEffect(() => {
    if (!isPanelOpen) return () => {};
    const handleKeyDown = (e: KeyboardEvent) => {
      const { key } = e;
      downKeys.current = Array.from(new Map([...downKeys.current, key].map((mapK) => [mapK, mapK])).values());
      if (downKeys.current.length === 2 && downKeys.current.includes('z') && downKeys.current.includes('Control')) {
        e.preventDefault();
        e.stopPropagation();
        const currentIndex = operateHistory.current.findIndex(
          (findR) => findR.from.isSame(range.from) && findR.to.isSame(range.to),
        );
        const returnTarget = currentIndex > 0 ? operateHistory.current.at(currentIndex - 1) : undefined;
        if (returnTarget) setRange(returnTarget);
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      const { key } = e;
      downKeys.current = downKeys.current.filter((filterK) => filterK !== key);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [isPanelOpen, range.from, range.to]);

  const setToday = () => {
    setRange({
      from: dayjs().startOf('day'),
      to: dayjs().endOf('day'),
    });
    setMode('day');
  };
  const setThisWeek = () => {
    setRange({
      from: dayjs().startOf('isoWeek'),
      to: dayjs().endOf('isoWeek'),
    });
    setMode('week');
  };
  const setThisMonth = () => {
    setRange({
      from: dayjs().startOf('month'),
      to: dayjs().endOf('month'),
    });
    setMode('month');
  };
  const shiftRange = (isDecreasing: boolean = false) => {
    if (mode === 'day') {
      setRange({
        from: range.from.add(isDecreasing ? -1 : 1, 'day'),
        to: range.to.add(isDecreasing ? -1 : 1, 'day'),
      });
    } else if (mode === 'week') {
      setRange({
        from: range.from.add(isDecreasing ? -1 : 1, 'week'),
        to: range.to.add(isDecreasing ? -1 : 1, 'week'),
      });
    } else if (mode === 'month') {
      setRange({
        from: range.from.add(isDecreasing ? -1 : 1, 'month').startOf('month'),
        to: range.from.add(isDecreasing ? -1 : 1, 'month').endOf('month'),
      });
    }
  };
  const getRangeTooltip = (isDecreasing: boolean = false) => {
    if (isDecreasing)
      switch (mode) {
        case 'day':
          return t('prevDay');
        case 'week':
          return t('prevWeek');
        case 'month':
          return t('prevMonth');
        default:
          return t('prevDay');
      }
    else
      switch (mode) {
        case 'day':
          return t('nextDay');
        case 'week':
          return t('nextWeek');
        case 'month':
          return t('nextMonth');
        default:
          return t('nextDay');
      }
  };

  return (
    <div {...{ ...wrapProps, style: { ...wrapProps?.style, position: 'relative' } }}>
      <button
        onClick={() => setIsPanelOpen(true)}
        type='button'
        className={`
          flex items-center space-x-2 rounded-lg border  p-2 transition hover:border-[#389e0d] focus:outline-[#389e0d] ${
            isDark ? 'border-[#424242] bg-[#141414]' : 'border-[#d9d9d9] bg-white'
          }
          ${className}
        `}
        {...btnProps}
      >
        <section>{dayjs(from).format(dateFormator.accurate)}</section>

        <RightSquareOutlined />

        <section>{dayjs(to).format(dateFormator.accurate)}</section>

        <section>
          <CalendarOutlined />
        </section>
      </button>

      <div
        style={{
          background:
            'linear-gradient(to right, #cfd1d0 47.4%, #cfd1d0 47.5%, #919191 50%, #cfd1d0 52.5%, #cfd1d0 100%)',
          backgroundSize: '220% 100%',
          backgroundPosition: `${backgroundPosition}%`,
          pointerEvents: 'auto',
          cursor: 'wait',
          zIndex: 1,
          visibility: isLoadingMask ? 'visible' : 'hidden',
        }}
        className={`
          absolute left-0 top-0 h-full w-full bg-green-300 opacity-50
        `}
      />
      <ModalAlpha
        open={isPanelOpen}
        styles={{ content: { transform: 'translateY(13vh)' } }}
        footer={null}
        onCancel={() => setIsPanelOpen(false)}
        width={600}
        afterOpenChange={(isOpen) => {
          if (!isOpen) operateHistory.current = [];
        }}
      >
        <div className='flex items-center justify-center gap-x-2'>
          <BtnFuncs
            type='link'
            className='border border-current'
            onClick={setToday}
          >
            {t('today')}
          </BtnFuncs>
          <BtnFuncs
            type='link'
            className='border border-current'
            onClick={setThisWeek}
          >
            {t('thisWeek')}
          </BtnFuncs>
          <BtnFuncs
            type='link'
            className='border border-current'
            onClick={setThisMonth}
          >
            {t('thisMonth')}
          </BtnFuncs>
        </div>
        <main className='my-4 flex items-center justify-between gap-x-2'>
          {/* left */}
          <Tooltip title={getRangeTooltip(true)}>
            <BtnFuncs
              type='link'
              className='border border-current'
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                shiftRange(true);
              }}
            />
          </Tooltip>
          <section className='flex flex-col space-y-1'>
            <Txt>{t('fromLabel')}</Txt>
            <DatePicker
              value={from}
              onChange={(newDate) => setRange((pre) => ({ ...pre, from: newDate }))}
              allowClear={false}
            />
            {/* Pick time */}
            <section className='flex w-full items-center justify-between space-x-6'>
              <section>
                <Select
                  options={Array(24)
                    .fill(null)
                    .map((_, index) => {
                      const hour = index;
                      return { value: hour, label: dayjs(from).set('hour', hour).format('HH') };
                    })}
                  value={dayjs(from).get('hour')}
                  onChange={(newHour) => setRange((pre) => ({ ...pre, from: pre.from.set('hour', newHour) }))}
                />
                <Txt className='ml-1'>{t('hour')}</Txt>
              </section>
              <Txt>:</Txt>

              <section>
                <Select
                  options={Array(60)
                    .fill(null)
                    .map((_, index) => {
                      const minute = index;
                      return { value: minute, label: dayjs(from).set('minute', minute).format('mm') };
                    })}
                  value={dayjs(from).get('minute')}
                  onChange={(newMinute) => setRange((pre) => ({ ...pre, from: pre.from.set('minute', newMinute) }))}
                />
                <Txt className='ml-1'>{t('minute')}</Txt>
              </section>
            </section>
          </section>

          <RightSquareOutlined />

          {/* right */}
          <section className='flex flex-col space-y-1'>
            <Txt>{t('toLabel')}</Txt>
            <DatePicker
              value={to}
              onChange={(newDate) => setRange((pre) => ({ ...pre, to: newDate }))}
              allowClear={false}
            />
            {/* Pick time */}
            <section className='flex w-full items-center justify-between  space-x-6'>
              <section>
                <Select
                  options={Array(24)
                    .fill(null)
                    .map((_, index) => {
                      const hour = index;
                      return { value: hour, label: dayjs(to).set('hour', hour).format('HH') };
                    })}
                  value={dayjs(to).get('hour')}
                  onChange={(newHour) => setRange((pre) => ({ ...pre, to: pre.to.set('hour', newHour) }))}
                />
                <Txt className='ml-1'>{t('hour')}</Txt>
              </section>
              <Txt>:</Txt>

              <section>
                <Select
                  options={Array(60)
                    .fill(null)
                    .map((_, index) => {
                      const minute = index;
                      return { value: minute, label: dayjs(to).set('minute', minute).format('mm') };
                    })}
                  value={dayjs(to).get('minute')}
                  onChange={(newMinute) => setRange((pre) => ({ ...pre, to: pre.to.set('minute', newMinute) }))}
                />
                <Txt className='ml-1'>{t('minute')}</Txt>
              </section>
            </section>
          </section>
          <Tooltip title={getRangeTooltip()}>
            <BtnFuncs
              type='link'
              className='border border-current'
              icon={<ArrowRightOutlined />}
              onClick={() => {
                shiftRange();
              }}
            />
          </Tooltip>
        </main>

        <Button
          loading={inSubmitLoading || inSubmit}
          className='mx-auto block w-1/2'
          type='primary'
          onClick={() => {
            setInSubmitLoading(true);
            if (onDateSubmit) onDateSubmit(rangeResult);
            setTimeout(() => {
              setIsPanelOpen(false);
            }, 500);
            setTimeout(() => {
              setInSubmitLoading(false);
            }, 1000);
          }}
        >
          {t('submit')}
        </Button>
      </ModalAlpha>
    </div>
  );
};

export default DateRange;
export type { DateRangeOptions, IDateRangeProps };
