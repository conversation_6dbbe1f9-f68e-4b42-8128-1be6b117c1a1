// libs
import { HtmlHTMLAttributes, useCallback, useMemo, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { useTranslation } from 'react-i18next';
import { RightSquareOutlined } from '@ant-design/icons';

// components
import { Txt } from '../TypographyMaster';
import DateFields from './DateFields';
import type { DateRangeOptions } from './DateRange';

interface IDateRangeFieldsProps {
  value?: DateRangeOptions;
  onChange?: (newRange: DateRangeOptions) => void;
  containerProps?: HtmlHTMLAttributes<HTMLDivElement>;
  defaultRange?: DateRangeOptions;
}
const DateRangeFields: React.FunctionComponent<IDateRangeFieldsProps> = (props) => {
  const { value, onChange, defaultRange, containerProps } = props || {};
  const [dateRange, setDateRange] = useState(defaultRange || { from: dayjs(), to: dayjs() });
  const { t } = useTranslation('dateRangeFields');
  // compute
  const rangeSelf = useMemo(() => value || dateRange, [dateRange, value]);
  // handlers
  const handleFromChange = useCallback(
    (newDate: Dayjs) => {
      if (value && onChange) onChange({ ...value, from: newDate });
      else setDateRange((pre) => ({ ...pre, from: newDate }));
    },
    [onChange, value],
  );

  const handleToChange = useCallback(
    (newDate: Dayjs) => {
      if (value && onChange) onChange({ ...value, to: newDate });
      else setDateRange((pre) => ({ ...pre, to: newDate }));
    },
    [onChange, value],
  );

  return (
    <main
      {...containerProps}
      className={`flex items-center justify-between space-x-2 ${containerProps?.className}`}
    >
      {/* left */}
      <section className='flex flex-col space-y-1'>
        <Txt>{t('from')}</Txt>
        <DateFields
          value={rangeSelf.from}
          onChange={handleFromChange}
        />
      </section>

      <RightSquareOutlined />

      {/* right */}
      <section className='flex flex-col space-y-1'>
        <Txt>{t('to')}</Txt>
        <DateFields
          value={rangeSelf.to}
          onChange={handleToChange}
        />
      </section>
    </main>
  );
};

export default DateRangeFields;
