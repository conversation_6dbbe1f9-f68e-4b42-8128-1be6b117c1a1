// libs
import { HtmlHTMLAttributes, useCallback, useMemo, useState } from 'react';
import { DatePicker, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useTranslation } from 'react-i18next';

// components
import { Txt } from '../TypographyMaster';

interface IDateFieldsProps {
  containerProps?: HtmlHTMLAttributes<HTMLDivElement>;
  value?: Dayjs;
  onChange?: (newValue: Dayjs) => void;
  defaultDate?: Dayjs;
}

const DateFields: React.FunctionComponent<IDateFieldsProps> = (props) => {
  // props
  const { containerProps, value, defaultDate, onChange } = props || {};

  // states
  const [date, setDate] = useState(defaultDate || dayjs());

  // hooks
  const { t } = useTranslation('dateFields');

  // compute
  const dateSelf = useMemo(() => value || date, [date, value]);

  // handlers
  const handleChangeDate = useCallback(
    (newDate: Dayjs) => {
      if (onChange && value) onChange(newDate);
      else setDate(newDate);
    },
    [onChange, value],
  );
  const handleSelectHour = useCallback(
    (newHour: number) => {
      if (onChange && value) onChange(value.set('hour', newHour));
      else setDate((pre) => pre.set('hour', newHour));
    },
    [onChange, value],
  );
  const handleSelectMinute = useCallback(
    (newMinute: number) => {
      if (onChange && value) onChange(value.set('hour', newMinute));
      else setDate((pre) => pre.set('minute', newMinute));
    },
    [onChange, value],
  );

  return (
    <main {...containerProps}>
      <DatePicker
        value={dateSelf}
        onChange={handleChangeDate}
        allowClear={false}
        className='mb-1 w-full'
      />
      {/* Pick time */}
      <section className='flex w-full items-center justify-between space-x-6'>
        <section className='flex items-center'>
          <Select
            options={Array(24)
              .fill(null)
              .map((_, index) => {
                const hour = index;
                return { value: hour, label: (hour % 24).toString().padStart(2, '0') };
              })}
            value={dateSelf.get('hour')}
            onChange={handleSelectHour}
          />
          <Txt className='ml-1'>{t('hour')}</Txt>
        </section>
        <Txt>:</Txt>

        <section className='flex items-center'>
          <Select
            options={Array(60)
              .fill(null)
              .map((_, index) => {
                const minute = index;
                return { value: minute, label: (minute % 60).toString().padStart(2, '0') };
              })}
            value={dateSelf.get('minute')}
            onChange={handleSelectMinute}
          />
          <Txt className='ml-1'>{t('minute')}</Txt>
        </section>
      </section>
    </main>
  );
};

export default DateFields;
