import { useMemo, useRef } from 'react';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { GlobalOutlined } from '@ant-design/icons';
import { useUserStore } from '@/store/useUserStore';
import { useIsDemo } from '@/hooks';
import { useLocation } from 'react-router-dom';
import DropdownAlpha, { DropdownAlphaRef, IDropdownAlphaProps } from './DropdownAlpha';

type AllLocaleDefinition = Record<LocaleTypes, { locale: LocaleTypes; label: React.ReactNode }>;
interface IDropdownLocaleProps extends Omit<IDropdownAlphaProps, 'items'> {}

const DropdownLocale: React.FunctionComponent<IDropdownLocaleProps> = (props) => {
  // props
  const { ...dropdownProps } = props || {};

  // refs
  const dropRef = useRef<DropdownAlphaRef>(null);

  // hooks
  const { i18n } = useTranslation();
  const { language, changeLanguage } = i18n;
  const { isDark } = useUserStore();
  const isDemo = useIsDemo();
  const location = useLocation();
  // compute
  const allLocaleDefinition: AllLocaleDefinition = useMemo(() => {
    return {
      'zh-TW': {
        locale: 'zh-TW',
        label: '中文(繁體)',
      },
      'zh-CN': {
        locale: 'zh-CN',
        label: '中文(简体)',
      },
      'en-US': {
        locale: 'en-US',
        label: 'English',
      },
      'vi-VN': {
        locale: 'vi-VN',
        label: 'Tiếng Việt',
      },
    };
  }, []);
  const allLocaleItem = useMemo(() => {
    return Object.values(allLocaleDefinition)
      .filter((item) => item.locale !== 'zh-CN' && item.locale !== 'vi-VN') // Temporarily hide zh-CN and vi-VN until the translations are available
      .map((mapD) => {
        const { label, locale } = mapD;
        const isActive = language === locale;

        return {
          item: (
            <Button
              className={`${isDark ? 'text-white' : 'text-black'} w-full`}
              type={isActive ? 'primary' : 'link'}
              onClick={() => {
                changeLanguage(locale);
                dropRef.current?.setIsOpen(false);
              }}
            >
              {label}
            </Button>
          ),
          key: locale,
          locale,
        };
      });
  }, [allLocaleDefinition, changeLanguage, isDark, language]);

  const isLogin = useMemo(() => location.pathname.includes('private'), [location]);

  const colorStyle = useMemo(() => {
    if (isDemo && !isDark && !isLogin) return '';
    if (isDemo && isDark) return '#fff';
    if (isDemo && !isDark) return '#fff';
    if (isDark) return '';
    if (!isDark) return '';
    return '';
  }, [isDark, isDemo, isLogin]);

  return (
    <DropdownAlpha
      buttonProps={{ type: 'text', style: { paddingInline: 8, color: colorStyle } }}
      ref={dropRef}
      icon={<GlobalOutlined />}
      items={allLocaleItem}
      noUnderLink
      gap={8}
      itemHeight='fit-content'
      pannelMaxHeight={400}
      {...dropdownProps}
    />
  );
};

export default DropdownLocale;
