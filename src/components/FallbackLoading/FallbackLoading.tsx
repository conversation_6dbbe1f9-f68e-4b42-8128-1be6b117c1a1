// store
import { useUserStore } from '@/store';

// components
import SpinMaster from '../SpinMaster';

interface IFallbackLoadingProps {}

const FallbackLoading: React.FunctionComponent<IFallbackLoadingProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { isDark } = useUserStore();

  return (
    <div
      className={`
        flex h-screen items-center justify-center
        ${isDark ? 'bg-neutral-800' : 'bg-green-200'}
      `}
    >
      <SpinMaster />
    </div>
  );
};

export default FallbackLoading;
