/* eslint-disable react/require-default-props */
// libs
import { forwardRef, memo, useMemo } from 'react';
import { Table, TableProps } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { isEqual } from 'lodash';

// hooks
import useTbApColumns from './useTbApColumns';

// styles
import styles from './styles.module.scss';

interface ITableAlphaProps<T = any> extends Omit<TableProps<T>, 'columns' | 'expandable'> {
  columns?: ColumnsType<T>;
  expandable?: TableProps<T>['expandable'];
  titleRender?: React.ReactNode;
  setPageSize?: React.Dispatch<React.SetStateAction<number>>;
  setCurrentPage?: React.Dispatch<React.SetStateAction<number>>;
  totalDataLength?: number;
  pageSize?: number;
  currentPage?: number;
  onFilterChange?: (filters: { [key: string]: Array<string> }) => void;
  isDark?: boolean;
}

const TableAlpha = forwardRef<HTMLDivElement, ITableAlphaProps>((props, ref) => {
  // props
  const {
    isDark,
    columns: originColumns,
    setPageSize: syncPageSize,
    setCurrentPage: syncPage,
    currentPage,
    pageSize,
    totalDataLength,
    onFilterChange,
    scroll,
    titleRender,
    title,
    ...tableProps
  } = props;

  // hooks
  const columns = useTbApColumns({
    isDark,
    originColumns,
    onFilterChange,
  });

  // compute
  const pageSizeOptions = useMemo(() => {
    const psDefailtOptions = [10, 20, Math.min(50, totalDataLength || 10)].filter((filterPs) =>
      totalDataLength ? totalDataLength >= filterPs : true,
    );
    const psOptionsMap = new Map(psDefailtOptions.map((mapN) => [mapN, mapN]));
    const psArr = Array.from(psOptionsMap.values()).sort((a, b) => a - b);

    return psArr;
  }, [totalDataLength]);

  return (
    <div ref={ref}>
      <Table
        title={
          !!title || !!titleRender
            ? (tableData) => {
                if (title) return title(tableData);
                return titleRender;
              }
            : undefined
        }
        pagination={{
          total: totalDataLength,
          current: currentPage,
          pageSize,
          onChange: (newPage, newSize) => {
            if (newPage !== undefined && syncPage) syncPage(newPage);
            if (newSize !== undefined && syncPageSize) syncPageSize(newSize);
          },
          hideOnSinglePage: false,
          showSizeChanger: true,
          pageSizeOptions,
        }}
        rowClassName={() => styles.test}
        scroll={{
          x: 900,
          ...scroll,
        }}
        {...{
          ...tableProps,
          columns,
          style: {
            ...tableProps.style,
            scrollbarWidth: 'thin',
          },
        }}
      />
    </div>
  );
});

export default memo(TableAlpha, (preProps, nextProps) => {
  const {
    totalDataLength: preTotalDataLength,
    pageSize: prePageSize,
    currentPage: preCurrentPage,
    isDark: preIsDark,
    setPageSize: _preSetPageSize, // Do not compare set function
    setCurrentPage: _preSetCurrentPage,

    /**
     * columns, expandable, titleRender ar depth judgments and are easier to change
     * than othter functions to judge in advance and use And to avoid redundant operations.
     */
    columns: preColumns,
    expandable: preExpandable,
    titleRender: preTitleRender,
    ...otherPreProps
  } = preProps;
  const {
    totalDataLength: nextTotalDataLength,
    pageSize: nextPageSize,
    currentPage: nextCurrentPage,
    isDark: nextIsDark,
    setPageSize: _nextSetPageSize, // Do not compare set function
    setCurrentPage: _nextetCurrentPage,

    /**
     * columns, expandable, titleRender ar depth judgments and are easier to change
     * than othter functions to judge in advance and use And to avoid redundant operations.
     */
    columns: nextColumns,
    expandable: nextExpandable,
    titleRender: nextTitleRender,
    ...otherNextProps
  } = nextProps;
  const isShallowDiff =
    preTotalDataLength !== nextTotalDataLength ||
    preIsDark !== nextIsDark ||
    prePageSize !== nextPageSize ||
    preCurrentPage !== nextCurrentPage;
  if (isShallowDiff) return false;

  const result =
    isEqual(preColumns, nextColumns) &&
    isEqual(preExpandable, nextExpandable) &&
    isEqual(preTitleRender, nextTitleRender) &&
    isEqual(otherPreProps, otherNextProps);

  return result;
});
