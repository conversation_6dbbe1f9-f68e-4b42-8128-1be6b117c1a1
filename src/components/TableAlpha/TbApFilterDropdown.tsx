// libs
import { Key, useMemo } from 'react';
import { List, Checkbox, Button, Tooltip } from 'antd';
import { ClearOutlined, SearchOutlined } from '@ant-design/icons';
import { FilterDropdownProps } from 'antd/es/table/interface';
import { useTranslation } from 'react-i18next';

interface ITbApFilterDropdownProps extends FilterDropdownProps {
  columnKey: Key | undefined;
  filteredValue: { [key: string]: Array<string> };
  setFilteredValue: ReactSet<ITbApFilterDropdownProps['filteredValue']>;
  setConfirmFilterValue: ReactSet<ITbApFilterDropdownProps['filteredValue']>;
}

const TbApFilterDropdown: React.FunctionComponent<ITbApFilterDropdownProps> = (props) => {
  // props
  const { filteredValue, setFilteredValue, setConfirmFilterValue, columnKey, filters, confirm } = props || {};

  // hooks
  const { t } = useTranslation('tbApFilterDropdown');

  // compute
  const columnFiltered = useMemo(() => {
    if (typeof columnKey === 'undefined' || typeof columnKey === 'bigint' || !(columnKey in filteredValue)) return [];
    return filteredValue[columnKey];
  }, [columnKey, filteredValue]);

  return (
    <>
      <List
        dataSource={filters}
        renderItem={(option) => {
          const checked = (() => {
            if (typeof columnKey === 'bigint' || typeof columnKey === 'undefined' || !(columnKey in filteredValue))
              return false;
            if (!filteredValue[columnKey].includes(option.value.toString())) return false;
            return true;
          })();
          return (
            <List.Item>
              <Checkbox
                {...{ checked }}
                className='ml-2'
                key={option.value.toString()}
                onChange={(e) => {
                  if (typeof columnKey !== 'string') return;

                  if (e.target.checked) {
                    if (!(columnKey in filteredValue)) {
                      const newValues = [option.value.toString()];
                      setFilteredValue((pre) => ({ ...pre, [columnKey]: newValues }));
                    } else {
                      const newValues = filteredValue[columnKey].concat([option.value.toString()]);
                      const setValues = Array.from(new Set(newValues));
                      setFilteredValue((pre) => ({ ...pre, [columnKey]: setValues }));
                    }
                  } else if (!(columnKey in filteredValue)) {
                    setFilteredValue((pre) => ({ ...pre, [columnKey]: [] }));
                  } else {
                    const newValues = filteredValue[columnKey].filter(
                      (filterFilterValue) => filterFilterValue !== option.value.toString(),
                    );
                    setFilteredValue((pre) => ({ ...pre, [columnKey]: newValues }));
                  }
                }}
              >
                {option.text}
              </Checkbox>
            </List.Item>
          );
        }}
      />
      <div className='flex w-full justify-between px-2 pb-2 '>
        <Tooltip title={t('clear')}>
          <Button
            size='small'
            shape='circle'
            icon={<ClearOutlined />}
            disabled={!columnFiltered.length}
            onClick={() => {
              setFilteredValue((pre) => {
                if (typeof columnKey === 'undefined' || typeof columnKey === 'bigint') return pre;
                return { ...pre, [columnKey]: [] };
              });
              setConfirmFilterValue((pre) => {
                if (typeof columnKey === 'undefined' || typeof columnKey === 'bigint') return pre;
                return { ...pre, [columnKey]: [] };
              });
              confirm();
            }}
          />
        </Tooltip>

        <Button
          size='small'
          type='primary'
          icon={<SearchOutlined />}
          onClick={() => {
            setConfirmFilterValue((pre) => {
              if (typeof columnKey === 'undefined' || typeof columnKey === 'bigint') return pre;
              const pureConfirmed = { ...pre, [columnKey]: columnFiltered };
              return pureConfirmed;
            });
            confirm();
          }}
        >
          {t('submit')}
        </Button>
      </div>
    </>
  );
};

export default TbApFilterDropdown;
