@import "../../color.module.scss";

.test {
  border: 2px solid blue;
}
.filter {
  padding-inline: 2px;
  transition: color 0.2s;
  color: $accessories;
  &:hover {
    color: $accessories_hovered;
  }
  &.active {
    color: $accessories_active;
    &:hover {
      color: $accessories_active_hovered;
    }
  }

  &.dark {
    color: $accessories_dark;
    &:hover {
      color: $accessories_dark_hovered;
    }
    &.active {
      color: $accessories_dark_active;
      &:hover {
        color: $accessories_dark_active_hovered;
      }
    }
  }
}

.box-sorter {
  padding-inline: 2px;
  position: relative;
  display: flex;
  flex-direction: column;
  .sorter {
    font-size: 9px;
    transition: color 0.2s;
    color: $accessories;
    &:hover {
      color: $accessories_active_hovered;
    }
    &.active {
      color: $sorter_active;
      &:hover {
        color: $accessories_active_hovered;
      }
    }

    &.dark {
      color: $accessories_dark;
      &:hover {
        color: $accessories_dark_hovered;
      }
      &.active {
        color: $sorter_dark_active;
        &:hover {
          color: $accessories_dark_active_hovered;
        }
      }
    }
  }
}
