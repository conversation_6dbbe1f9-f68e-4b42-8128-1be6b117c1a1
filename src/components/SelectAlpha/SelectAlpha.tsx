// libs
import { forwardRef, useImperativeHandle, useState } from 'react';
import { Select, SelectProps } from 'antd';

type SelectAlphaRef = {
  value: any;
  setValue: ReactSet<any>;
};

interface ISelectAlphaProps extends SelectProps {}

const SelectAlpha = forwardRef<SelectAlphaRef, ISelectAlphaProps>((props, ref) => {
  // props
  const { value, onChange, ...selectProps } = props || {};

  // states
  const [valueSelf, setValueSelf] = useState(value);

  // init
  useImperativeHandle(
    ref,
    () => {
      return {
        value: valueSelf,
        setValue: setValueSelf,
      };
    },
    [valueSelf],
  );

  return (
    <Select
      value={valueSelf}
      onChange={(newValue, option) => {
        setValueSelf(newValue);
        if (onChange) onChange(newValue, option);
      }}
      {...selectProps}
    />
  );
});

export default SelectAlpha;
export type { SelectAlphaRef };
