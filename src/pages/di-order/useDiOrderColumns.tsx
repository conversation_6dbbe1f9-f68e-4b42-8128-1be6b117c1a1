import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { TableColumnsType, Tooltip, Flex } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { DiOrderItemInterface, DiOrderListRes } from '@/api/order';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import { TagTxStatus, TagCryptoType } from '@/components/TagAlpha';
import { dateFormator } from '@/hooks';
import { useUserStore } from '@/store';
import { CryptoEnum, TxStatusNum, nTot } from '@/utils';

interface DiOrderRowInterface {
  key: React.Key;
  merchantNumber: string;
  orderUid: string;
  merchantOrderId: string;
  memberId: string;
  payerBankAccountName: string;
  entryCode: string;
  transactionType: number;
  cryptoType: CryptoEnum;
  cryptoAmount: number;
  fiatType: string;
  fiatAmount: number;
  status: TxStatusNum;
  createdAt: string;
  isMerchantNotified: boolean;
}

type DiOrderColumnsProps = {
  dataSource: Array<DiOrderRowInterface>;
  txInfo: DiOrderListRes | undefined;
  setDiOrderMatchFrom: ReactSet<DiOrderItemInterface | undefined>;
  search: string | undefined;
  setSearch: ReactSet<DiOrderColumnsProps['search']>;
  searchPlaceHolder: string | undefined;
  setSearchPlaceHolder: ReactSet<DiOrderColumnsProps['searchPlaceHolder']>;
};

const useDiOrderColumns = (useProps: DiOrderColumnsProps) => {
  // props
  const { search, setSearch, setSearchPlaceHolder, setDiOrderMatchFrom, dataSource, txInfo } = useProps;

  // states
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  // hooks
  const { isDark } = useUserStore();
  const { t } = useTranslation('diOrderColumns');

  // handlers
  const onClickStatus = useCallback(
    (row: DiOrderRowInterface) => {
      setDiOrderMatchFrom(txInfo?.items.find((findI) => findI.orderUid === row.key));
    },
    [setDiOrderMatchFrom, txInfo?.items],
  );

  // === init ===
  // When input search
  const preSearch = useRef(search);
  useEffect(() => {
    if (preSearch.current === search) return;
    preSearch.current = search;
    if (!search) {
      setHoveredKeys([]);
    } else {
      const pureSearch = search.trim().toLowerCase();
      const newHoveredKeys = dataSource.reduce(
        (preKeys, currentD) => {
          const newItemKeys = [];
          if (currentD.orderUid.toLowerCase().includes(pureSearch)) newItemKeys.push(currentD.orderUid);
          if (currentD.merchantOrderId.toLowerCase().includes(pureSearch)) newItemKeys.push(currentD.merchantOrderId);
          if (currentD.entryCode.toLowerCase().includes(pureSearch)) newItemKeys.push(currentD.entryCode);

          return [...preKeys, ...newItemKeys];
        },
        [] as typeof hoveredKeys,
      );
      setHoveredKeys(newHoveredKeys);
    }
  }, [dataSource, search]);

  // When hovered text
  const lastHovered = useMemo(() => hoveredKeys.at(-1), [hoveredKeys]);
  const tempLastHovered = useRef(lastHovered);
  useEffect(() => {
    if (lastHovered === tempLastHovered.current) return;
    tempLastHovered.current = lastHovered;
    if (!lastHovered) {
      setSearchPlaceHolder(undefined);
    } else setSearchPlaceHolder(lastHovered);
  }, [lastHovered, setSearch, setSearchPlaceHolder]);

  // compute
  const columns = useMemo(() => {
    const renderAllDiOrderStatus = () => {
      const keys = Object.keys(TxStatusNum)
        .map((item) => {
          if (!Number.isNaN(Number(item))) {
            return Number(item);
          }

          return undefined;
        })
        .filter((item) => item !== undefined) as Array<TxStatusNum>;

      return keys.map((key) => {
        return {
          value: key,
          text: (
            <TagTxStatus
              className='w-32 border text-center'
              status={key}
            />
          ),
          diabled: true,
        };
      });
    };

    const result: TableColumnsType<DiOrderRowInterface> = [
      {
        title: <Txt>{t('merchantColumn')}</Txt>,
        key: 'merchant',
        render: (_, { merchantNumber }) => {
          return (
            <Tooltip title={t('merchantNumber')}>
              <Txt code>{merchantNumber}</Txt>
            </Tooltip>
          );
        },
      },
      {
        title: <Txt>{t('memberColumn')}</Txt>,
        key: 'member',
        render: (_, { memberId, payerBankAccountName }) => {
          return (
            <Flex vertical>
              <Tooltip title={t('memberId')}>
                <Txt code>{memberId}</Txt>
              </Tooltip>
              <Tooltip title={t('payerBankAccountName')}>
                <Txt>{payerBankAccountName}</Txt>
              </Tooltip>
            </Flex>
          );
        },
      },
      {
        title: <Txt>{t('timeColumn')}</Txt>,
        key: 'time',
        render: (_, { createdAt }) => {
          const createFormat = createdAt ? dayjs(createdAt).format(dateFormator.accurate) : '-';

          return (
            <Flex gap={5}>
              <Flex vertical>
                <Txt type='secondary'>{t('createTime')}:</Txt>
              </Flex>
              <Flex vertical>
                <Txt>{createFormat}</Txt>
              </Flex>
            </Flex>
          );
        },
      },
      {
        title: <Txt>{t('merchantOrderNumber')}</Txt>,
        key: 'merchantOrderNumber',
        render: (_, { merchantOrderId }) => {
          const isExpanded = expandedKeys.includes(merchantOrderId.toString() || '');
          const isHovered = hoveredKeys.includes(merchantOrderId.toString() || '');
          return (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={merchantOrderId.toString() || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: <Txt>UXM {t('orderUidColumn')}</Txt>,
        key: 'orderUid',
        render: (_, { orderUid }) => {
          const isExpanded = expandedKeys.includes(orderUid.toString() || '');
          const isHovered = hoveredKeys.includes(orderUid.toString() || '');
          return (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={orderUid.toString() || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: <Txt>{t('entryCodeColumn')}</Txt>,
        key: 'entryCode',
        render: (_, { entryCode }) => {
          const isExpanded = expandedKeys.includes(entryCode.toString() || '');
          const isHovered = hoveredKeys.includes(entryCode.toString() || '');
          return (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={entryCode.toString() || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: <Txt>{t('cryptoColumn')}</Txt>,
        key: 'crypto',
        align: 'center',
        render: (_, { cryptoType }) => {
          return <TagCryptoType {...{ cryptoType }} />;
        },
      },
      {
        title: <Txt>{t('cryptoAmount')}</Txt>,
        key: 'cryptoAmount',
        align: 'right',
        sorter: (a, b) => a.cryptoAmount - b.cryptoAmount,
        render: (_, { cryptoAmount }) => {
          return <Txt>{nTot({ value: cryptoAmount, digitsType: 'USD' })}</Txt>;
        },
      },
      {
        title: <Txt>{t('fiatAmount')}</Txt>,
        key: 'fiatAmount',
        align: 'right',
        sorter: (a, b) => a.fiatAmount - b.fiatAmount,
        render: (_, { fiatAmount, fiatType }) => {
          return (
            <Txt
              strong
              className='text-[#389e0d]'
            >
              {fiatAmount ? `${nTot({ value: fiatAmount, digitsType: 'USD' })} ${fiatType}` : '-'}
            </Txt>
          );
        },
      },
      {
        title: <Txt>{t('notificationColumn')}</Txt>,
        key: 'notification',
        align: 'center',
        render: (_, { isMerchantNotified }) => {
          return (
            <Txt className={isMerchantNotified ? 'text-green-600' : 'text-red-600'}>
              {isMerchantNotified ? t('notified') : t('notNotified')}
            </Txt>
          );
        },
      },
      {
        title: <Txt>{t('stateColumn')}</Txt>,
        key: 'state',
        filters: renderAllDiOrderStatus(),
        align: 'center',
        render: (_, record) => {
          const { status } = record;

          return (
            <TagTxStatus
              {...{ status }}
              tooltip={t('detailed')}
              onClick={() => onClickStatus(record)}
            />
          );
        },
      },
    ];

    return result;
  }, [expandedKeys, hoveredKeys, isDark, onClickStatus, t]);

  return { columns };
};

export default useDiOrderColumns;
export type { DiOrderRowInterface, DiOrderColumnsProps };
