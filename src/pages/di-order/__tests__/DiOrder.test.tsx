import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import DiOrder from '../DiOrder';

// Mock the hooks and components
jest.mock('@/api/order', () => ({
  useDiOrderList: () => ({
    data: {
      currentPage: 1,
      totalPages: 1,
      pageSize: 10,
      totalCount: 1,
      items: [
        {
          orderUid: 'P-B-**************-5CUBV2',
          merchantOrderId: 'd01ssa37-8da5-3caa-a237-6esa33e31e82',
          memberId: 'testMember',
          payerBankAccountName: 'Test Bank Account',
          entryCode: 'UvWfALsFmhcNqd5HCKLoWeVuBhTiGt7W',
          transactionType: 1,
          cryptoType: 1,
          cryptoAmount: 1502.05,
          fiatType: 'CNY',
          fiatAmount: 10965,
          status: 3,
          createdAt: '2025-05-27T10:57:05.88964Z',
          isMerchantNotified: false,
        },
      ],
    },
    isPending: false,
    isRefetching: false,
    isError: false,
    refetch: jest.fn(),
  }),
}));

jest.mock('@/hooks/useDataSource', () => ({
  __esModule: true,
  default: () => ({
    dataSource: [
      {
        key: 'P-B-**************-5CUBV2',
        orderUid: 'P-B-**************-5CUBV2',
        merchantOrderId: 'd01ssa37-8da5-3caa-a237-6esa33e31e82',
        memberId: 'testMember',
        payerBankAccountName: 'Test Bank Account',
        entryCode: 'UvWfALsFmhcNqd5HCKLoWeVuBhTiGt7W',
        transactionType: 1,
        cryptoType: 1,
        cryptoAmount: 1502.05,
        fiatType: 'CNY',
        fiatAmount: 10965,
        status: 3,
        createdAt: '2025-05-27T10:57:05.88964Z',
        isMerchantNotified: false,
      },
    ],
    updateItem: jest.fn(),
  }),
}));

jest.mock('../useDiOrderColumns', () => ({
  __esModule: true,
  default: () => ({
    columns: [
      {
        title: 'Member',
        key: 'member',
        render: () => <div>Test Member</div>,
      },
    ],
  }),
}));

jest.mock('../useDiOrderSummary', () => ({
  __esModule: true,
  default: () => () => <tr><td>Summary</td></tr>,
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('DiOrder', () => {
  it('renders DI Order page correctly', () => {
    render(
      <TestWrapper>
        <DiOrder />
      </TestWrapper>
    );

    // Check if the breadcrumb is rendered
    expect(screen.getByText('DI Orders')).toBeInTheDocument();
    
    // Check if the search functionality is present
    expect(screen.getByPlaceholderText(/Find order information/i)).toBeInTheDocument();
  });

  it('displays the correct page title', () => {
    render(
      <TestWrapper>
        <DiOrder />
      </TestWrapper>
    );

    expect(screen.getByText('DI Orders')).toBeInTheDocument();
  });
});
