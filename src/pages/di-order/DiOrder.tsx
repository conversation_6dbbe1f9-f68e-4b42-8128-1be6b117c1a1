import { Breadcrumb, Input, Select } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { DiOrderItemInterface, DiOrderListRes, useDiOrderList } from '@/api/order';
import { useMerchantList } from '@/api/merchant';
import SearchMaster from '@/components/SearchMaster';
import { useCallback, useMemo, useRef, useState } from 'react';
import TableAlpha from '@/components/TableAlpha';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import dayjs from 'dayjs';
import BtnTableFuncs from '@/components/BtnFuncs/BtnTableFuncs';
import PrintModal, { PrintDeposit } from '@/components/PrintModal';
import { Txt } from '@/components/TypographyMaster';
import { dateFormator, useTableStates, useTestRenderTimes } from '@/hooks';
import useDataSource from '@/hooks/useDataSource';
import useExcelSummary from '@/hooks/useExcelSummary';
import useTranslateExcelTitleRow from '@/hooks/useTranslateExcelTitleRow';
import { useNotifyStore, useUserStore } from '@/store';
// prettier-ignore
import {  cryptoEnumOptions, exportSheetByArray, storageHelper, txStatusOptions, } from '@/utils';
import useDiOrderColumns, { DiOrderRowInterface } from './useDiOrderColumns';
import useDiOrderSummary from './useDiOrderSummary';

const storageRange = storageHelper<{ from: string; to: string }>('diOrderRange').getItem();
const defaultDateRange = storageRange
  ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
  : { from: dayjs().startOf('d'), to: dayjs().startOf('d').add(1, 'd') };

type DiOrderSearchValues = {
  [key in 'MerchantOrderId' | 'OrderUid']?: string;
};

interface IDiOrderProps {}

const DiOrder: React.FunctionComponent<IDiOrderProps> = (props) => {
  const location = useLocation();
  const printDiOrderRef = useRef(null); // `ref` of an element will be printed.
  // props
  const {} = props || {};
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange); // page filters
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({}); // page filters
  const [OrderUid, setOrderUid] = useState<string | undefined>(location.state?.OrderUid); // page filters
  const [MerchantOrderId, setMerchantOrderId] = useState<string>(); // page filters
  const [MerchantNumber, setMerchantNumber] = useState<number>(); // page filters
  const [OrderByDescending, setOrderByDescending] = useState<boolean>(); // page filters
  const [search, setSearch] = useState<string>();
  const [searchPlaceHolder, setSearchPlaceHolder] = useState<string>();
  const [, setDiOrderMatchFrom] = useState<DiOrderItemInterface>();
  const [openPrint, setOpenPrint] = useState<boolean>(false); // Controls the open/close state of the `PrintModal`.

  // hooks
  const { isDark } = useUserStore();
  const { pushBEQ } = useNotifyStore();
  const { t } = useTranslation('diOrder');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({ translator: 'diOrder' });

  const { data, isPending, isRefetching, isError, refetch } = useDiOrderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      CreatedAtStart: dateRange.from.format(),
      CreatedAtEnd: dateRange.to.format(),
      MerchantOrderId,
      MerchantNumber,
      OrderUid,
      OrderByDescending,
    },
  });
  const { data: merchantsInfo, isError: mxIsError } = useMerchantList({});

  // compute
  const { dataSource } = useDataSource<DiOrderRowInterface, DiOrderListRes>({
    isError,
    txInfo: data,
    mapper: (item: DiOrderItemInterface) => {
      return {
        key: item.orderUid,
        merchantNumber: item.merchantNumber,
        orderUid: item.orderUid,
        merchantOrderId: item.merchantOrderId,
        memberId: item.memberId,
        payerBankAccountName: item.payerBankAccountName,
        entryCode: item.entryCode,
        transactionType: item.transactionType,
        cryptoType: item.cryptoType,
        cryptoAmount: item.cryptoAmount,
        fiatType: item.fiatType,
        fiatAmount: item.fiatAmount,
        status: item.status,
        createdAt: item.createdAt,
        isMerchantNotified: item.isMerchantNotified,
      };
    },
  });
  const { columns } = useDiOrderColumns({
    search,
    setSearch,
    searchPlaceHolder,
    setSearchPlaceHolder,
    dataSource,
    txInfo: data,
    setDiOrderMatchFrom,
  });
  const summary = useDiOrderSummary({ columns });
  const excelSummary = useExcelSummary<DiOrderRowInterface>({
    dataSource,
    offset: 7, // indexOf cryptoAmount = 7 (after adding merchant column)
    keysToSum: ['cryptoAmount', 'fiatAmount'],
  });

  const clientsOptions = useMemo(() => {
    if (!merchantsInfo || mxIsError) return [];
    const multiClientName = merchantsInfo.items.map((mapI) => mapI.customerName);
    const clientNamesMap = new Map(multiClientName.map((mapN) => [mapN, mapN]));
    const clientsName = Array.from(clientNamesMap.values()).sort();
    return clientsName;
  }, [merchantsInfo, mxIsError]);

  const merchantsOptions = useMemo(() => {
    if (!merchantsInfo || mxIsError) return [];
    const { items } = merchantsInfo;
    return clientsOptions.map((mapCn) => {
      const clientMerchants = items.filter((filterI) => filterI.customerName === mapCn);
      return {
        label: (
          <Txt
            strong
            type='secondary'
          >
            {mapCn}
          </Txt>
        ),
        title: mapCn,
        options: clientMerchants.map((mapCm) => {
          const { merchantNumber, merchantName } = mapCm;
          return {
            label: <Txt>{merchantName}</Txt>,
            value: merchantNumber,
          };
        }),
      };
    });
  }, [clientsOptions, merchantsInfo, mxIsError]);

  // handlers
  const handleOnDateSubmit = useCallback((newDate: DateRangeOptions) => {
    setDateRange(newDate);
    storageHelper<DateRangeOptions>('diOrderRange').setItem(newDate);
  }, []);

  const handleSearch = useCallback(
    (values: DiOrderSearchValues) => {
      const isAllKeySame = Object.entries(values).every(([key, value]) => {
        if (key === 'MerchantOrderId') return value === MerchantOrderId;
        if (key === 'OrderUid') return value === OrderUid;
        return false;
      });
      if (isAllKeySame) refetch();
      else {
        setMerchantOrderId(values.MerchantOrderId);
        setOrderUid(values.OrderUid);
      }
    },
    [MerchantOrderId, OrderUid, refetch],
  );

  const handlePrint = (_: React.MouseEvent<HTMLElement, MouseEvent>) => {
    if (dataSource.length) {
      setOpenPrint(true);
    } else {
      pushBEQ([{ title: 'UXM Settlement Corp', des: t('emptyRecordsMessage') }]);
    }
  };

  const handleExportXlsx = useCallback(() => {
    const sheetTitleRow = [
      'merchantNumberTitleRow',
      'memberIdTitleRow',
      'payerBankAccountNameTitleRow',
      'createTimeTitleRow',
      'orderUidTitleRow',
      'merchantOrderIdTitleRow',
      'entryCodeTitleRow',
      'cryptoTitleRow',
      'cryptoAmountTitleRow',
      'fiatAmountTitleRow',
      'notificationTitleRow',
      'stateTitleRow',
    ];
    const sheetDataRows = [
      ...dataSource.map((mapD) => {
        const cryptoOption = cryptoEnumOptions.find((findO) => findO.value === mapD.cryptoType);
        const statusOption = txStatusOptions.find((findO) => findO.value === mapD.status);
        const createTime = mapD.createdAt ? dayjs(mapD.createdAt).format(dateFormator.accurate) : '';
        return [
          mapD.merchantNumber,
          mapD.memberId,
          mapD.payerBankAccountName,
          createTime,
          mapD.orderUid,
          mapD.merchantOrderId,
          mapD.entryCode,
          cryptoOption?.label || '', // Crypto
          mapD.cryptoAmount,
          `${mapD.fiatAmount} ${mapD.fiatType}`,
          mapD.isMerchantNotified ? t('notified') : t('notNotified'),
          optionsT(statusOption?.label || 'undefined'),
        ];
      }),
      excelSummary,
    ];
    exportSheetByArray({
      arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
      sheetName: t('sheetName'),
      fileName: `${t('fileName')} ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
        dateFormator.accurate,
      )} `,
    });
  }, [dataSource, dateRange.from, dateRange.to, excelSummary, translateExcelTitleRow, t, optionsT]);

  // Test render times
  useTestRenderTimes(false, 'DiOrder');

  return (
    <>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('homeBreadcrumb')}</Link>,
          },
          {
            title: t('currentBreadcrumb'),
          },
        ]}
      />

      <header className='my-4 flex flex-col gap-y-2'>
        <section className='flex flex-wrap items-end gap-2'>
          <DateRange
            loading={isRefetching}
            onDateSubmit={handleOnDateSubmit}
            defaultValues={dateRange}
          />
          <Select
            placeholder={t('merchantOptionsPlaceholder')}
            options={merchantsOptions}
            popupMatchSelectWidth={160}
            allowClear
            onChange={(newMerchantNumber) => setMerchantNumber(newMerchantNumber)}
          />
          <Input
            className='w-48'
            placeholder={searchPlaceHolder || t('searchPlaceholder')}
            autoComplete='off'
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            lang='en'
            inputMode='email'
          />
        </section>

        <section className='flex flex-wrap items-end justify-between gap-2'>
          <SearchMaster
            titles={[
              { key: 'MerchantOrderId', label: t('searchMerchantOrderIdLabel') },
              { key: 'OrderUid', label: t('searchOrderUidLabel') },
            ]}
            defaultValues={{
              OrderUid: OrderUid ?? '',
            }}
            onSearch={handleSearch}
          />

          <BtnTableFuncs
            onExcelClick={handleExportXlsx}
            onPrintClick={handlePrint}
          />
        </section>
      </header>

      {/* main */}
      <TableAlpha
        {...{
          isDark,
          dataSource,
          columns,
          summary,
          totalDataLength: data?.totalCount,
          pageSize,
          setPageSize,
          currentPage,
          setCurrentPage,
        }}
        rowClassName={(record) => {
          let className = '';
          if (record.key === OrderUid) {
            if (isDark) className = 'highlight-row-dark';
            else className = 'highlight-row';
          }
          return className;
        }}
        size='small'
        rowKey='key'
        loading={isPending}
        onChange={(_, _filters, sortResults) => {
          const firstSortResults = (() => {
            if (Array.isArray(sortResults)) return sortResults.at(0);
            return sortResults;
          })();
          const { order: orderOrderType } = firstSortResults || {};
          setOrderByDescending(() => {
            if (orderOrderType === undefined) return orderOrderType;
            return orderOrderType === 'descend';
          });
        }}
      />
    </>
  );
};

export default DiOrder;
