import { useMemo } from 'react';
import { TableColumnsType } from 'antd';
import { useTranslation } from 'react-i18next';
import { Txt } from '@/components/TypographyMaster';
import { nTot } from '@/utils';
import { DiOrderRowInterface } from './useDiOrderColumns';

interface DiOrderSummaryProps {
  columns: TableColumnsType<DiOrderRowInterface>;
}

const useDiOrderSummary = (useProps: DiOrderSummaryProps) => {
  // props
  const { columns } = useProps;

  // hooks
  const { t } = useTranslation('diOrderSummary');

  // compute
  const summary = useMemo(() => {
    return (pageData: readonly DiOrderRowInterface[]) => {
      let totalCryptoAmount = 0;
      let totalFiatAmount = 0;
      let totalNotified = 0;

      pageData.forEach(({ cryptoAmount, fiatAmount, isMerchantNotified }) => {
        totalCryptoAmount += cryptoAmount;
        totalFiatAmount += fiatAmount;
        if (isMerchantNotified) totalNotified += 1;
      });

      return (
        <tr>
          <td colSpan={columns.length - 4}>
            <Txt strong>{t('total')}</Txt>
          </td>
          <td className='text-right'>
            <Txt strong>{nTot({ value: totalCryptoAmount, digitsType: 'USD' })}</Txt>
          </td>
          <td className='text-right'>
            <Txt strong>{nTot({ value: totalFiatAmount, digitsType: 'USD' })}</Txt>
          </td>
          <td className='text-center'>
            <Txt strong>{`${totalNotified}/${pageData.length}`}</Txt>
          </td>
          <td></td>
        </tr>
      );
    };
  }, [columns.length, t]);

  return summary;
};

export default useDiOrderSummary;
