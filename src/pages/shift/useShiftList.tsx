// libs
import { useCallback } from 'react';
import { Table, TableProps } from 'antd';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

// api
import { ListShiftOption } from '@/api';

// components
import { Txt } from '@/components/TypographyMaster';
import TagShiftName from '@/components/TagAlpha/TagShiftName';

// utils
import { ShiftNameEnum, nTot } from '@/utils';

const useShiftList = () => {
  // hooks
  const { t } = useTranslation('shiftListColumns');

  // compute
  const columns = (date: string): TableProps<ListShiftOption>['columns'] => [
    {
      title: () => <Txt className='text-nowrap'>{date}</Txt>,
      key: 'shiftName',
      align: 'center',
      render: (_, { shiftName }) => {
        return <TagShiftName {...{ shiftName }} />;
      },
    },
    {
      title: () => <Txt className='text-nowrap'>{t('trxColumn')}</Txt>,
      key: 'trx',
      align: 'center',
      render: (_, { trx }) => <Txt>{nTot({ value: trx, digitsType: 'BandWidth' })}</Txt>,
    },
    {
      title: () => <Txt className='text-nowrap'>{t('usdtColumn')}</Txt>,
      key: 'usdt',
      align: 'center',
      render: (_, { usdt }) => <Txt>{nTot({ value: usdt, digitsType: 'USDT' })}</Txt>,
    },
    {
      title: () => <Txt className='text-nowrap'>{t('totalFeeColumn')}</Txt>,
      key: 'totalFees',
      align: 'center',
      render: (_, { totalFees }) => <Txt>{nTot({ value: totalFees, digitsType: 'BandWidth' })}</Txt>,
    },
    {
      title: () => <Txt className='text-nowrap'>{t('totalGasPayColumn')}</Txt>,
      key: 'totalGasPay',
      align: 'center',
      render: (_, { totalGasPay }) => <Txt>{nTot({ value: totalGasPay, digitsType: 'BandWidth' })}</Txt>,
    },
  ];

  const summary = useCallback(
    (datas: readonly any[]) => {
      const summaryDatas: Array<ListShiftOption> = [...datas];
      const totalFee = summaryDatas.reduce((preValue, current) => preValue + current.totalFees, 0);
      const totalGasPay = summaryDatas.reduce((preValue, current) => preValue + current.totalGasPay, 0);

      return (
        <Table.Summary fixed>
          <Table.Summary.Row className='font-semibold'>
            <Table.Summary.Cell
              index={0}
              colSpan={3}
            />
            <Table.Summary.Cell
              index={1}
              align='end'
            >
              {t('pageTotal')}
            </Table.Summary.Cell>
            <Table.Summary.Cell
              index={2}
              align='center'
            >
              {nTot({ value: totalFee, digitsType: 'BandWidth' })}
            </Table.Summary.Cell>
            <Table.Summary.Cell
              index={3}
              align='center'
            >
              {nTot({ value: totalGasPay, digitsType: 'BandWidth' })}
            </Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
    [t],
  );

  const defaultDateRange = {
    from: dayjs().startOf('day'),
    to: dayjs().endOf('day'),
  };
  const SHIFT_ORDER = [ShiftNameEnum.Night, ShiftNameEnum.Morning, ShiftNameEnum.Afternoon];

  return { columns, summary, defaultDateRange, SHIFT_ORDER };
};

export default useShiftList;
