// libs
import { Breadcrumb } from 'antd';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// pages
import ShiftList from './components/ShiftList/ShiftList';

const Shift: React.FC = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { t } = useTranslation('shift');

  return (
    <>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('homeBreadcrumb')}</Link>,
          },
          {
            title: t('currentBreadcrumb'),
          },
        ]}
      />
      <ShiftList />
    </>
  );
};

export default Shift;
