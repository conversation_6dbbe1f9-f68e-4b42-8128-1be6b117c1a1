// libs
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

// components
import { Txt } from '@/components/TypographyMaster';

// utils
import { nTot } from '@/utils';

interface IShiftSummaryRow {
  key: string;
  label: string;
  value: number;
}

interface ISummaryValues {
  depositCount: number;
  depositAmount: number;
  depositFee: number;
  depositGas: number;
  withdrawalCount: number;
  withdrawalAmount: number;
  withdrawalFee: number;
  withdrawalGas: number;
}

const useMerchantSummary = () => {
  // hooks
  const { t } = useTranslation('merchantSummary');

  // compute
  const columns = useCallback(
    (title: string) => [
      {
        key: title,
        title: <Txt>{t(title)}</Txt>,
        render: (_: any, record: IShiftSummaryRow) => (
          <span className='flex justify-between'>
            <span>{t(record.label)}</span>
            {record.value !== 0 ? (
              <span>
                {record.label.toLowerCase().includes('count')
                  ? record.value
                  : nTot({ value: record.value, digitsType: 'BandWidth' })}
              </span>
            ) : (
              '--'
            )}
          </span>
        ),
      },
    ],
    [t],
  );

  const createDataSource = (summary: ISummaryValues) =>
    Object.entries(summary).map(([key, value]) => ({
      key,
      label: key,
      value,
    }));

  /**
   * Maps a summary object to a structured format using a specified prefix.
   *
   * @param {string} prefix - The prefix used to extract keys from the summary object.
   *                          It can be either 'merchant' or 'member'. For example, if
   *                          the prefix is 'merchant', it will look for keys like
   *                          "merchantDepositCount" and "merchantWithdrawalAmount".
   * @param {any} summary - The source object containing the summary data, typically with
   *                        keys in a prefixed format.
   * @returns {ISummaryValues} - A structured object containing the deposit and withdrawal
   *                             counts, amounts, fees, and gas values extracted from the
   *                             summary object.
   *
   * This function is useful for transforming raw summary data into a more readable and
   * manageable format. It assumes that the summary object has keys named in the pattern
   * `${prefix}DepositCount`, `${prefix}DepositAmount`, etc., where `prefix` is either
   * 'merchant' or 'member'.
   */
  const mapSummary = (prefix: string, summary: any): ISummaryValues => ({
    depositCount: summary[`${prefix}DepositCount`],
    depositAmount: summary[`${prefix}DepositAmount`],
    depositFee: summary[`${prefix}DepositFee`],
    depositGas: summary[`${prefix}DepositGas`],
    withdrawalCount: summary[`${prefix}WithdrawalCount`],
    withdrawalAmount: summary[`${prefix}WithdrawalAmount`],
    withdrawalFee: summary[`${prefix}WithdrawalFee`],
    withdrawalGas: summary[`${prefix}WithdrawalGas`],
  });

  return { columns, createDataSource, mapSummary };
};

export default useMerchantSummary;
export type { IShiftSummaryRow, ISummaryValues };
