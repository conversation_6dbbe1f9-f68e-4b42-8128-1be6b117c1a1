// libs
import { useState, useCallback, useEffect, useRef } from 'react';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { Card, Form, Input, Button, Radio } from 'antd';
import { useTranslation } from 'react-i18next';
import { useLogin } from '@/api';
import { useFormInit } from '@/hooks';

type LoginSubmitValues = {
  userID: string;
  code: string;
};

interface IStepLoginProps {
  setReq2FA: ReactSet<boolean>;
  active?: boolean;
}
const StepLogin: React.FunctionComponent<IStepLoginProps> = (props) => {
  // props
  const { setReq2FA } = props || {};

  // states
  const [loginDevType, setLoginDevType] = useState<string>('admin');
  const initialValues = useFormInit({ type: 'login', isTest: true });

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('stepLogin');

  // mutate
  const { mutate: login, isPending: inLogin } = useLogin({
    onSuccess: (res) => {
      if (res.requiredTwoFactor) setReq2FA(true);
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: LoginSubmitValues) => {
      const { userID, code } = values;
      login({
        username: userID,
        password: code,
      });
    },
    [login],
  );

  // init
  useEffect(() => {
    const isTestLogin = true;
    if (!isTestLogin || !import.meta.env.DEV) return;
    if (loginDevType.toLowerCase().includes('admin')) {
      form.setFieldValue('userID', loginDevType);
      form.setFieldValue('code', 'Aa123456');
    } else {
      form.setFieldValue('userID', loginDevType);
      form.setFieldValue('code', '123456Aa');
    }
  }, [form, loginDevType]);

  const downKeys = useRef<Array<string>>([]);
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const newKeys = [...downKeys.current, e.key];
      const newKeysMap = new Map(newKeys.map((mapK) => [mapK, mapK]));
      downKeys.current = Array.from(newKeysMap.values());

      // Key combination settings, only for development
      if (!import.meta.env.DEV) return;
      const keysCurrent = downKeys.current;
      if (keysCurrent.includes('Enter') && keysCurrent.includes('Shift') && keysCurrent.includes('Control')) {
        if (keysCurrent.at(0) !== 'Shift' || keysCurrent.at(1) !== 'Control') return;
        form.setFieldValue('userID', 'benAdmin2');
        form.setFieldValue('code', 'Aa123456');
        form.submit();
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      const newKeys = downKeys.current.filter((filterK) => filterK !== e.key);
      const newKeysMap = new Map(newKeys.map((mapK) => [mapK, mapK]));
      downKeys.current = Array.from(newKeysMap.values());
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  });

  return (
    <Card title={t('title')}>
      <Form
        {...{ form, initialValues }}
        variant='filled'
        layout='vertical'
        onFinish={handleSubmit}
      >
        <Form.Item
          name='userID'
          label={t('userIdLabel')}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder={t('userIdPlaceholder')}
            inputMode='text'
          />
        </Form.Item>

        <Form.Item
          name='code'
          label={t('passwordLabel')}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder={t('passwordPlaceholder')}
            autoComplete='current-password'
            inputMode='text'
          />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inLogin}
          >
            {t('login')}
          </Button>
        </Form.Item>
      </Form>

      {import.meta.env.DEV && (
        <div className='mt-4 flex flex-col space-y-4'>
          <Radio.Group
            optionType='button'
            options={['benAdmin', 'shuuAdmin', 'liamAdmin', 'soAdmin']}
            value={loginDevType}
            onChange={(e) => setLoginDevType(e.target.value)}
          />

          <Radio.Group
            optionType='button'
            options={['cs1', 'cs2', 'cs3', 'cs4', 'cs5']}
            value={loginDevType}
            onChange={(e) => setLoginDevType(e.target.value)}
          />

          <Radio.Group
            optionType='button'
            options={['supervisor1', 'supervisor2', 'supervisor3', 'supervisor4', 'supervisor5']}
            value={loginDevType}
            onChange={(e) => setLoginDevType(e.target.value)}
          />
        </div>
      )}
    </Card>
  );
};

export default StepLogin;
export type { IStepLoginProps, LoginSubmitValues };
