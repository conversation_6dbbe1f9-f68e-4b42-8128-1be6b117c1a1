// libs
import { useState } from 'react';

// components
import { ICinemaProps } from '@/components/CardGallery';

// pages
import StepLogin, { IStepLoginProps } from './StepLogin';
import StepGa, { IStepGaProps } from './StepGa';

type LoginCiemaProps = {
  loginProps: IStepLoginProps;
  gaProps: IStepGaProps;
};

const useLoginCinema = (useProps: LoginCiemaProps) => {
  // props
  const { loginProps, gaProps } = useProps;

  // states
  const [currentMv, setCurrentMv] = useState<'login' | 'ga'>();

  const screens: ICinemaProps<typeof currentMv>['screens'] = {
    login: (
      <StepLogin
        {...loginProps}
        active={currentMv === 'login'}
      />
    ),
    ga: (
      <StepGa
        {...gaProps}
        active={currentMv === 'ga'}
      />
    ),
  };

  return { currentMv, setCurrentMv, screens };
};

export default useLoginCinema;
export type { LoginCiemaProps };
