import { useEffect, useState } from 'react';
import { Col, Row, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { Cinema } from '@/components/CardGallery';
import useLoginCinema from './useLoginCinema';

interface ILoginProps {}
const Login: React.FunctionComponent<ILoginProps> = (props) => {
  // props
  const {} = props || {};

  // states
  const [req2FA, setReq2FA] = useState(false);

  // hooks
  const { t } = useTranslation('login');

  // compute
  const { currentMv, setCurrentMv, screens } = useLoginCinema({
    loginProps: { setReq2FA },
    gaProps: { reqPreStep: () => setCurrentMv('login') },
  });

  // init
  useEffect(() => {
    const isTestScreen = false;
    if (isTestScreen && import.meta.env.DEV) setCurrentMv('ga');
    else if (req2FA) setCurrentMv('ga');
    else setCurrentMv('login');
  }, [req2FA, setCurrentMv]);

  return (
    <Row
      wrap
      gutter={[30, 10]}
    >
      <Col
        flex='auto'
        className='ml-2'
      >
        <Typography.Title level={3}>
          {t('welcome')}
          <br />
          UXM Settlement Corp！
        </Typography.Title>
        <Typography.Text type='secondary'>{t('slogan')}</Typography.Text>
      </Col>

      <Col flex='auto'>
        <Cinema {...{ currentMv, screens }} />
      </Col>
    </Row>
  );
};

export default Login;
