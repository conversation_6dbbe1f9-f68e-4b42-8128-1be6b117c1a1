// libs
import { useCallback } from 'react';
import { UserOutlined } from '@ant-design/icons';
import { Card, Form, Input, Button } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useVfy2Fa } from '@/api';

type SubmitVfyValues = { TwoFactorCode: string };

interface IStepGaProps {
  active?: boolean;
  reqPreStep: () => void;
}

const StepGa: React.FunctionComponent<IStepGaProps> = (props) => {
  // props
  const { reqPreStep } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('stepGa');

  // mutation
  const { mutate: vfy, isPending: inVfy } = useVfy2Fa({
    onError: () => {
      form.resetFields();
      reqPreStep();
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: SubmitVfyValues) => {
      vfy(values);
    },
    [vfy],
  );

  return (
    <Card title={t('title')}>
      <Form
        {...{ form }}
        variant='filled'
        layout='vertical'
        onFinish={handleSubmit}
      >
        <Form.Item
          name='TwoFactorCode'
          label={t('_2FACodeLabel')}
          rules={[{ required: true, message: t('_2FAErrorMessage') }]}
        >
          <Input.Password
            prefix={<UserOutlined />}
            placeholder={t('_2FAPlaceholder')}
            inputMode='text'
          />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inVfy}
          >
            {t('verify')}
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default StepGa;
export type { SubmitVfyValues, IStepGaProps };
