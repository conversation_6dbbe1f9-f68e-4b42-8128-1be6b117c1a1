import { Disable2FAProps, InfoRes } from '@/api';
import BtnFuncs from '@/components/BtnFuncs';
import LogList from '@/components/LogList';
import { Txt } from '@/components/TypographyMaster';
import { EditOutlined } from '@ant-design/icons';
import { DescriptionsProps, Button, Space } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

type UserDescribeItemsProps = {
  info: InfoRes | undefined | null;
  setOpenChPw: ReactSet<boolean>;
  setOpenSetOpw: ReactSet<boolean>;
  setOpenChangeOPW: ReactSet<boolean>;
  setOpenBindingGA: ReactSet<boolean>;
  setOpenOpwVfy: ReactSet<boolean>;
  disable2FA: (props: Disable2FAProps) => void;
  inDisable: boolean;
};

const useUserDescribeItems = (useProps: UserDescribeItemsProps) => {
  const { info, setOpenChPw, setOpenSetOpw, setOpenChangeOPW, setOpenBindingGA, setOpenOpwVfy, disable2FA, inDisable } =
    useProps;
  const { t } = useTranslation('user');

  const items: DescriptionsProps['items'] = useMemo(() => {
    const { nickName, userId, roles } = info || {};
    const permission = (() => {
      if (roles?.includes('SystemAdmin')) return t('administrator');
      if (roles?.includes('SystemCS')) return 'CS';
      return 'CS';
    })();

    return [
      {
        key: '1',
        label: <Txt>{t('nickNameLabel')}</Txt>,
        children: <Txt className='text-lg font-bold'>{nickName}</Txt>,
      },
      {
        key: '3',
        label: <Txt>{t('userIdLabel')}</Txt>,
        children: userId,
      },
      {
        key: '4',
        label: <Txt>{t('createTimeLabel')}</Txt>,
        children: (
          <section className='flex items-center justify-around'>
            <Txt>-- {t('missingField')}</Txt>
            <Button
              type='link'
              icon={<EditOutlined />}
              onClick={() => setOpenChPw(true)}
            >
              {t('changePasswordButtonText')}
            </Button>
          </section>
        ),
      },
      {
        key: '5',
        label: <Txt>{t('permissionLabel')}</Txt>,
        children: permission,
      },
      {
        key: '8',
        label: <Txt>{t('operationPasswordLabel')}</Txt>,
        children: (
          <Space>
            <Button
              type='link'
              icon={<EditOutlined />}
              onClick={() => setOpenSetOpw(true)}
              disabled={info?.operationPasswordEnabled}
            >
              {t('setOperationPasswordButtonText')}
            </Button>

            <Button
              type='link'
              icon={<EditOutlined />}
              onClick={() => setOpenChangeOPW(true)}
              disabled={!info?.operationPasswordEnabled}
            >
              {t('changeOperationPasswordButtonText')}
            </Button>
          </Space>
        ),
      },
      {
        key: '9',
        label: <Txt>{t('_2FALabel')}</Txt>,
        children: (
          <Space>
            <Button
              type='link'
              icon={<EditOutlined />}
              onClick={() => setOpenBindingGA(true)}
              disabled={info?.twoFactorEnabled}
            >
              {t('enable2FAButtonText')}
            </Button>

            <BtnFuncs
              type='link'
              iconType='disabled'
              onClick={() => {
                if (!info) return;
                if (info.operationPasswordEnabled) setOpenOpwVfy(true);
                else
                  disable2FA({
                    userId: info.userId,
                  });
              }}
              danger
              disabled={!info?.twoFactorEnabled}
              loading={inDisable}
            >
              {t('disable2FAButtonText')}
            </BtnFuncs>
          </Space>
        ),
      },
      {
        key: '13',
        label: <Txt>{t('logLabel')}</Txt>,
        children: <LogList logs={[{ createdAt: dayjs().format(), log: t('noLog') }]} />,
      },
    ];
  }, [disable2FA, inDisable, info, setOpenBindingGA, setOpenChPw, setOpenChangeOPW, setOpenOpwVfy, setOpenSetOpw, t]);

  return items;
};

export default useUserDescribeItems;
