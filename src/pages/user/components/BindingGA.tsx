// libs
import { useCallback, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Button, Divider, Form, Input, QRCode, Skeleton, Space } from 'antd';
import { AndroidOutlined, AppleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// api
import { useEnable2FA } from '@/api';
import { use2FQrcode } from '@/api/auth/use2FQrcode';

// components
import BtnFuncs from '@/components/BtnFuncs';
import ModalAlpha from '@/components/ModalAlpha';
import { Title, Txt } from '@/components/TypographyMaster';

// store
import { useNotifyStore, useUserStore } from '@/store';

// utils
import queryKeys from '@/utils/queryKeys';

// pages
import QRCodeComponent from './QRCodeComponent';

type SubmitEnable2FAValues = {
  FirstCode: string;
  SecondCode: string;
};

interface IBindingGAProps {
  open: boolean;
  onCancel: () => void;
}

const BindingGA: React.FunctionComponent<IBindingGAProps> = (props) => {
  // props
  const { open, onCancel } = props || {};

  // states
  const [hoverOn, setHoverOn] = useState<'android' | 'ios'>();
  const [isHoverGoogleQrCode, setIsHoverGoogleQrCode] = useState(false);

  // hooks
  const queryClient = useQueryClient();
  const { isDark, loginRes } = useUserStore();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('bindingGA');

  const platformsData = [
    {
      title: t('goToGoogleStore'),
      qrValue: 'https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=zh_TW',
      storeLink: 'https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=zh_TW',
      icon: <AndroidOutlined />,
    },
    {
      title: t('goToAppStore'),
      qrValue: 'https://apps.apple.com/tw/app/google-authenticator/id388497605',
      storeLink: 'https://apps.apple.com/tw/app/google-authenticator/id388497605',
      icon: <AppleOutlined />,
    },
  ];

  // mutation
  const {
    handleSearch,
    isPending: gettingQrCode,
    data,
  } = use2FQrcode({ isActive: open, onSettled: () => setIsHoverGoogleQrCode(false) });
  const { mutate: enable2FA, isPending: inEnable } = useEnable2FA({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Settlement Corp', des: t('successNotificationDescription') }]);
      onCancel();
      queryClient.refetchQueries({ queryKey: queryKeys.query.info(loginRes?.token) });
    },
  });
  // handlers
  const handleSubmitEnable = useCallback(
    (values: SubmitEnable2FAValues) => {
      enable2FA(values);
    },
    [enable2FA],
  );

  return (
    <ModalAlpha
      width={720}
      title={
        <header>
          <Title
            tight
            level={3}
          >
            {t('title')}
          </Title>
        </header>
      }
      footer={false}
      open={open}
      onCancel={onCancel}
    >
      <Form
        variant='filled'
        onFinish={handleSubmitEnable}
      >
        <Title
          color='geekblue'
          level={4}
          style={{ textWrap: 'wrap' }}
        >
          {t('step1Title')}
        </Title>
        <div className='grid grid-cols-2 gap-4'>
          {platformsData.map((item) => (
            <div
              key={item.qrValue}
              className='flex flex-col items-center text-center'
            >
              <div className='flex items-center space-x-2'>
                <span className='text-lg font-medium'>{item.title}</span>
                {item.icon}
              </div>
              <QRCodeComponent
                title={item.title}
                qrValue={item.qrValue}
                storeLink={item.storeLink}
                isDark={isDark}
                hoverOn={hoverOn}
                setHoverOn={setHoverOn}
              />
            </div>
          ))}
        </div>

        <Title
          color='geekblue'
          level={4}
          className='mt-8'
          style={{ textWrap: 'wrap' }}
        >
          {t('step2Title')}
        </Title>

        <Form.Item
          label={t('remarkLabel')}
          name='remark'
        >
          <Input
            className='w-36'
            placeholder={t('remarkPlaceholder')}
          />
        </Form.Item>

        <main className='flex justify-between'>
          {!gettingQrCode ? (
            <div
              onMouseEnter={() => setIsHoverGoogleQrCode(true)}
              onMouseLeave={() => setIsHoverGoogleQrCode(false)}
              className='relative'
            >
              {data?.qrCodeUrl ? (
                <QRCode
                  bordered
                  value={data.qrCodeUrl}
                />
              ) : (
                <div className='flex h-32 w-32 items-center justify-center bg-gray-200 text-gray-500'>
                  QR Code not available
                </div>
              )}
              <div
                className={`absolute left-0
                        top-0 flex h-full w-full items-center justify-center transition-[opacity] duration-300
                        ${isDark ? 'bg-neutral-800' : 'bg-neutral-200'}
                        ${isHoverGoogleQrCode ? 'opacity-90' : 'opacity-0'}
                      `}
              >
                <BtnFuncs
                  iconType='redo'
                  type='link'
                  onClick={handleSearch}
                />
              </div>
            </div>
          ) : (
            <Skeleton.Node
              active
              style={{ width: 140, height: 140 }}
            />
          )}

          <Space
            direction='vertical'
            className='text-right'
          >
            <Txt strong>{t('scanGoogleAuthenticatorMessage')}</Txt>
            <Txt copyable>
              {t('myKey')}:
              {!gettingQrCode ? (
                data?.key
              ) : (
                <Skeleton.Input
                  size='small'
                  active
                />
              )}
            </Txt>
          </Space>
        </main>
        <Divider />

        <Title
          color='geekblue'
          level={4}
          className='mt-8'
          style={{ textWrap: 'wrap' }}
        >
          {t('step3Title')}
        </Title>

        <Space>
          <Form.Item
            name='FirstCode'
            label={t('firstCodeLabel')}
            rules={[{ required: true, message: t('firstCodeErrorMessage') }]}
          >
            <Input
              disabled={inEnable}
              placeholder={t('firstCodePlaceholder')}
              autoComplete='off'
            />
          </Form.Item>

          <Form.Item
            name='SecondCode'
            label={t('sendCodeLabel')}
            rules={[{ required: true, message: t('sendCodeErrorMessage') }]}
          >
            <Input
              disabled={inEnable}
              placeholder={t('sendCodePlaceholder')}
              autoComplete='off'
            />
          </Form.Item>
        </Space>

        <section
          className={`
            mb-4 flex justify-end
          `}
        >
          <Txt
            className={`rounded border-2 p-2
              ${isDark ? 'border-blue-800' : 'border-blue-600'}
            `}
            type='secondary'
            wrap
          >
            <ExclamationCircleOutlined /> {t('warningMessage')}
          </Txt>
        </section>

        <Form.Item className='mb-0'>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inEnable}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </ModalAlpha>
  );
};

export default BindingGA;
