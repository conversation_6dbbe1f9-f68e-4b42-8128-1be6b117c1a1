// libs
import { useCallback } from 'react';
import { Button, Divider, Form, Input, Modal } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useChOpw } from '@/api';

// components
import { Title } from '@/components/TypographyMaster';

// store
import { useNotifyStore } from '@/store';

type ChOpwSubmitValues = {
  oldOperationPassword: string;
  newOperationPassword: string;
  readonlyewOperationPassword: string;
};

interface IChangeOPWProps {
  open: boolean;
  onCancel: () => void;
}

const ChangeOPW: React.FunctionComponent<IChangeOPWProps> = (props) => {
  // props
  const { open, onCancel } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('changeOpw');

  // mutation
  const { mutate: chOpw, isPending: inCh } = useChOpw({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Settlement Corp', des: t('successNotificationDescription') }]);
      onCancel();
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: ChOpwSubmitValues) => {
      const { oldOperationPassword, newOperationPassword } = values;
      chOpw({
        oldOperationPassword,
        newOperationPassword,
      });
    },
    [chOpw],
  );

  return (
    <Modal
      title={
        <Title
          level={3}
          tight
        >
          {t('title')}
        </Title>
      }
      footer={false}
      open={open}
      onCancel={onCancel}
    >
      <Form
        {...{ form }}
        variant='filled'
        layout='vertical'
        onFinish={handleSubmit}
        className='mt-6'
      >
        <Form.Item
          name='oldOperationPassword'
          label={t('oldOperationPasswordLabel')}
          rules={[{ required: true, message: t('oldOperationPasswordErrorMessage') }]}
        >
          <Input.Password
            disabled={inCh}
            placeholder={t('oldOperationPasswordPlaceholder')}
          />
        </Form.Item>
        <Divider />

        <Form.Item
          name='newOperationPassword'
          label={t('newOperationPasswordLabel')}
          rules={[{ required: true, message: t('newOperationPasswordErrorMessage') }]}
        >
          <Input.Password
            disabled={inCh}
            placeholder={t('newOperationPasswordPlaceholder')}
          />
        </Form.Item>

        <Form.Item
          name='readonlyewOperationPassword'
          label={t('confirmOperationPasswordLabel')}
          rules={[
            {
              required: true,
              message: t('confirmOperationPasswordErrorMessage'),
            },
            {
              validator: (_, value) => {
                const newOperationPassword = form.getFieldValue('newOperationPassword');
                if (newOperationPassword !== value) return Promise.reject(Error(t('operationPasswordNotMatchMessage')));
                return Promise.resolve();
              },
            },
          ]}
          dependencies={['newOperationPassword']}
          className='mb-10'
        >
          <Input.Password
            disabled={inCh}
            placeholder={t('confirmOperationPasswordPlaceholder')}
          />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inCh}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ChangeOPW;
