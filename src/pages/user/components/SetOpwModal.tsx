// libs
import { useCallback } from 'react';
import { Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useSetOpw } from '@/api/auth';

// components
import BtnFuncs from '@/components/BtnFuncs';
import ModalAlpha from '@/components/ModalAlpha';
import { Title } from '@/components/TypographyMaster';

// store
import { useNotifyStore } from '@/store';

type SetOprPwValues = {
  loginPassword: string;
  operationPassword: string;
};

interface ISetOpwModalProps {
  open: boolean;
  setOpen: ReactSet<ISetOpwModalProps['open']>;
}

const SetOpwModal: React.FunctionComponent<ISetOpwModalProps> = (props) => {
  // props
  const { open, setOpen } = props || {};

  // hooks
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('setOpwModal');

  // mutation
  const { mutate: setOpw, isPending: inSet } = useSetOpw({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Settlement Corp', des: t('successNotificationDescription') }]);
      setOpen(false);
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: SetOprPwValues) => {
      setOpw(values);
    },
    [setOpw],
  );

  return (
    <ModalAlpha
      {...{ open }}
      onCancel={() => setOpen(false)}
      footer={null}
      title={
        <header>
          <Title
            level={3}
            tight
          >
            {t('title')}
          </Title>
        </header>
      }
      className='translate-y-[5vh]'
    >
      <Form
        onFinish={handleSubmit}
        layout='vertical'
      >
        <Form.Item
          name='loginPassword'
          label={t('loginPasswordLabel')}
          rules={[{ required: true, message: t('loginPasswordErrorMessage') }]}
        >
          <Input.Password disabled={inSet} />
        </Form.Item>

        <Form.Item
          name='operationPassword'
          label={t('operationPasswordLabel')}
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password disabled={inSet} />
        </Form.Item>

        <Form.Item className='mb-0 flex justify-end'>
          <BtnFuncs
            htmlType='submit'
            type='primary'
            loading={inSet}
          >
            {t('submit')}
          </BtnFuncs>
        </Form.Item>
      </Form>
    </ModalAlpha>
  );
};

export default SetOpwModal;
export type { SetOprPwValues, ISetOpwModalProps };
