// libs
import { useCallback } from 'react';
import { But<PERSON>, Divider, Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useChPw } from '@/api';

// components
import ModalAlpha from '@/components/ModalAlpha';
import { Title } from '@/components/TypographyMaster';

// store
import { useNotifyStore } from '@/store';

type ChpwSubmitValues = {
  currentPassword: string;
  newPassword: string;
  readonlyewOperationPassword: string;
};

interface IChangePwProps {
  open: boolean;
  setOpen: ReactSet<IChangePwProps['open']>;
}

const ChangePw: React.FunctionComponent<IChangePwProps> = (props) => {
  // props
  const { open, setOpen } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('changePw');

  // mutation
  const { mutate: chPw, isPending: inCh } = useChPw({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Settlement Corp', des: t('successNotificationDescription') }]);
      setOpen(false);
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: ChpwSubmitValues) => {
      chPw({
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
      });
    },
    [chPw],
  );

  return (
    <ModalAlpha
      title={
        <Title
          level={3}
          tight
        >
          {t('title')}
        </Title>
      }
      footer={false}
      open={open}
      onCancel={() => setOpen(false)}
    >
      <Form
        {...{ form }}
        variant='filled'
        onFinish={handleSubmit}
        layout='vertical'
        className='mt-6'
      >
        <Form.Item
          name='currentPassword'
          label={t('currentPasswordLabel')}
          rules={[{ required: true, message: t('currentPasswordErrorMessage') }]}
        >
          <Input.Password
            disabled={inCh}
            placeholder={t('currentPasswordPlaceholder')}
          />
        </Form.Item>

        <Divider />

        <Form.Item
          name='newPassword'
          label={t('newPasswordLabel')}
          rules={[{ required: true, message: t('newPasswordErrorMessage') }]}
        >
          <Input.Password
            disabled={inCh}
            placeholder={t('newPasswordPlaceholder')}
          />
        </Form.Item>

        <Form.Item
          name='readonlyewOperationPassword'
          label={t('confirmPasswordLabel')}
          rules={[
            {
              required: true,
              message: t('confirmPasswordErrorMessage'),
            },
            {
              validator: (_, value) => {
                const newPassword = form.getFieldValue('newPassword');
                if (newPassword !== value) return Promise.reject(Error(t('passwordNotMatchMessage')));
                return Promise.resolve();
              },
            },
          ]}
          dependencies={['newPassword']}
          className='mb-10'
        >
          <Input.Password
            disabled={inCh}
            placeholder={t('confirmPasswordPlaceholder')}
          />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inCh}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </ModalAlpha>
  );
};

export default ChangePw;
