// libs
import { QRCode } from 'antd';

interface IQRCodeComponent {
  title: string;
  qrValue: string;
  storeLink: string;
  isDark: boolean;
  hoverOn: string | undefined;
  setHoverOn: ReactSet<'ios' | 'android' | undefined>;
}

const QRCodeComponent = ({ title, qrValue, storeLink, isDark, hoverOn, setHoverOn }: IQRCodeComponent) => {
  const isHovered = hoverOn === title;

  return (
    <div
      onMouseEnter={() => setHoverOn(title as 'ios' | 'android' | undefined)}
      onMouseLeave={() => setHoverOn(undefined)}
      className='relative flex justify-center'
    >
      <QRCode
        bordered={false}
        value={qrValue}
      />
      <div
        className={`absolute left-0 top-0 flex h-full w-full items-center justify-center transition-opacity duration-300
        ${isDark ? 'bg-neutral-800' : 'bg-neutral-200'} 
        ${isHovered ? 'opacity-90' : 'opacity-0'}
        `}
      >
        <a
          href={storeLink}
          target='_blank'
          rel='noreferrer'
          className='text-white underline'
        >
          {title}
        </a>
      </div>
    </div>
  );
};

export default QRCodeComponent;
export type { IQRCodeComponent };
