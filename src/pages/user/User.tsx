import { useCallback, useState } from 'react';
import { Breadcrumb, Descriptions } from 'antd';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useDisable2FA } from '@/api';
import OpwVfyModal from '@/components/ModalAlpha/OpwVfyModal';
import { useUserStore } from '@/store';
import BindingGA from './components/BindingGA';
import ChangeOPW from './components/ChangeOPW';
import SetOpwModal from './components/SetOpwModal';
import ChangePw from './components/ChangePW';
import useUserDescribeItems from './useUserDescribeItems';

interface IUserProps {}
const User: React.FunctionComponent<IUserProps> = (props) => {
  // props
  const {} = props || {};

  // states
  const [openChPw, setOpenChPw] = useState(false);
  const [openSetOpw, setOpenSetOpw] = useState(false);
  const [openChangeOPW, setOpenChangeOPW] = useState(false);
  const [openBindingGA, setOpenBindingGA] = useState(false);
  const [openOpwVfy, setOpenOpwVfy] = useState(false);

  // hooks
  const { info } = useUserStore();
  const { t } = useTranslation('user');

  // mutate
  const { mutate: disable2FA, isPending: inDisable } = useDisable2FA({
    onSuccess: () => setOpenOpwVfy(false),
  });

  // handlers
  const handlePass = useCallback(
    (values: { code: string }) => {
      if (!info) return;
      disable2FA({
        userId: info.userId,
        OperationPassword: values.code,
      });
    },
    [disable2FA, info],
  );

  // compute
  const items = useUserDescribeItems({
    info,
    setOpenChPw,
    setOpenSetOpw,
    setOpenChangeOPW,
    setOpenBindingGA,
    setOpenOpwVfy,
    disable2FA,
    inDisable,
  });
  return (
    <>
      <div>
        <Breadcrumb
          items={[
            {
              title: <Link to='private'>{t('homeBreadcrumb')}</Link>,
            },
            {
              title: t('currentBreadcrumb'),
            },
          ]}
        />
        <Descriptions
          size='small'
          bordered
          items={items}
          column={1}
          labelStyle={{ width: 360 }}
          className='my-5'
        />
      </div>

      {/* Dialogues */}
      <ChangePw
        open={openChPw}
        setOpen={setOpenChPw}
      />
      <SetOpwModal
        open={openSetOpw}
        setOpen={setOpenSetOpw}
      />

      <ChangeOPW
        open={openChangeOPW}
        onCancel={() => setOpenChangeOPW(false)}
      />

      <BindingGA
        open={openBindingGA}
        onCancel={() => setOpenBindingGA(false)}
      />

      <OpwVfyModal
        open={openOpwVfy}
        setOpen={setOpenOpwVfy}
        vfyProps={{ onFinish: handlePass, loading: inDisable }}
      />
    </>
  );
};

export default User;
