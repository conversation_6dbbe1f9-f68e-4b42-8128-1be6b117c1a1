// libs
import React, { useCallback } from 'react';
import { Modal, Form, DatePicker, Select } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

// components
import BtnFuncs from '@/components/BtnFuncs';

// utils
import { CryptoEnum, cryptoEnumOptions } from '@/utils';

interface CustomerSummarySubmitValues {
  dateRange: [dayjs.Dayjs | null, dayjs.Dayjs | null];
  selectedCoins: CryptoEnum[];
}

interface CustomerSummaryModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSubmit: (values: CustomerSummarySubmitValues) => void;
  initialValues?: CustomerSummarySubmitValues;
}

const CustomerSummaryModal: React.FC<CustomerSummaryModalProps> = (props) => {
  // props
  const { open, setOpen, onSubmit, initialValues } = props || {};

  // hooks
  const { t } = useTranslation('customerSummaryModal');

  // handlers
  const handleSubmit = useCallback(
    (values: CustomerSummarySubmitValues) => {
      onSubmit({
        dateRange: values.dateRange,
        selectedCoins: values.selectedCoins,
      });
      setOpen(false);
    },
    [onSubmit, setOpen],
  );

  return (
    <Modal
      title={t('title')}
      open={open}
      onCancel={() => setOpen(false)}
      footer={null}
      width='40%'
    >
      <Form
        layout='vertical'
        initialValues={{
          dateRange: initialValues?.dateRange || [dayjs().startOf('M'), dayjs().endOf('M')],
          selectedCoins: initialValues?.selectedCoins || [CryptoEnum.TRC20_USDT],
        }}
        onFinish={handleSubmit}
      >
        <Form.Item
          label={t('dateRangeLabel')}
          name='dateRange'
        >
          <DatePicker.RangePicker
            allowClear={false}
            format='YYYY-MM-DD'
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          label={t('selectedCoinsLabel')}
          name='selectedCoins'
          rules={[
            {
              validator: (_, value: CryptoEnum[]) => {
                if (value.length) return Promise.resolve();
                return Promise.reject(Error(t('selectedCoinsErrorMessage')));
              },
            },
          ]}
        >
          <Select
            mode='multiple'
            placeholder={t('selectCurrencyPlaceholder')}
            options={cryptoEnumOptions}
            style={{ width: '100%' }}
          />
        </Form.Item>

        <div className='flex justify-end '>
          <Form.Item className='mb-0'>
            <BtnFuncs
              type='primary'
              htmlType='submit'
            >
              {t('submit')}
            </BtnFuncs>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
};

export default CustomerSummaryModal;
export type { CustomerSummarySubmitValues };
