import { Txt } from '@/components/TypographyMaster';
import { Flex } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface CustomerDataRowInterface {
  key: React.Key;
  customerId?: number;
  customerName?: string;
  merchantCount?: number;
  balance?: number;
  lockedBalance?: number;
  createdAt?: string;
  merchantId?: number;
  merchantName?: string;
  merchantNumber?: string;
  memberDepositCount: number;
  memberDepositAmount: number;
  memberDepositFee: number;
  memberDepositGas: number;
  memberWithdrawalCount: number;
  memberWithdrawalAmount: number;
  memberWithdrawalFee: number;
  memberWithdrawalGas: number;
  merchantDepositCount: number;
  merchantDepositAmount: number;
  merchantDepositFee: number;
  merchantDepositGas: number;
  merchantWithdrawalCount: number;
  merchantWithdrawalAmount: number;
  merchantWithdrawalFee: number;
  merchantWithdrawalGas: number;
  children?: Array<CustomerDataRowInterface>;
}

interface ICustomersColumnsProps {}

const useCustomersColumns = (props: ICustomersColumnsProps) => {
  const {} = props || {};

  const { t } = useTranslation('customersTable');

  const columns: ColumnsType<CustomerDataRowInterface> = [
    {
      title: <Txt className='block w-full text-center'>{t('customerColumn')}</Txt>,
      dataIndex: 'customerName',
      key: 'customerName',
      render: (_, { customerName }) => {
        return <Txt>{customerName}</Txt>;
      },
    },
    {
      title: <Txt className='block text-center'>{t('merchantColumn')}</Txt>,
      dataIndex: 'merchant',
      key: 'merchant',
      width: 200,
      render: (_, { merchantCount, merchantName, merchantNumber }) => {
        return merchantCount !== undefined ? (
          <Txt type='secondary'>
            {t('merchantCount')}: {merchantCount}
          </Txt>
        ) : (
          <Flex
            gap={5}
            justify='space-between'
          >
            <Txt>{merchantName}</Txt>
            <Txt code>{merchantNumber}</Txt>
          </Flex>
        );
      },
    },
    {
      title: <Txt>{t('memberDepositColumn')}</Txt>,
      children: [
        {
          title: (
            <Txt>
              {t('qty')}/{t('amt')}
            </Txt>
          ),
          key: 'memberDepositAmount',
          dataIndex: 'memberDepositAmount',
          align: 'right',
          render: (_, { memberDepositAmount, memberDepositCount }) => {
            return (
              <Txt>
                {memberDepositCount} / {memberDepositAmount}
              </Txt>
            );
          },
        },
        {
          title: <Txt>{t('fee')}</Txt>,
          key: 'memberDepositFee',
          dataIndex: 'memberDepositFee',
          align: 'right',
          render: (_, { memberDepositFee }) => {
            return (
              <Txt
                strong
                className='text-[#389e0d]'
              >
                {memberDepositFee}
              </Txt>
            );
          },
        },
      ],
    },
    {
      title: <Txt>{t('memberWithdrawColumn')}</Txt>,
      children: [
        {
          title: (
            <Txt>
              {t('qty')}/{t('amt')}
            </Txt>
          ),
          key: 'memberWithdrawalAmount',
          dataIndex: 'memberWithdrawalalAmount',
          align: 'right',
          render: (_, { memberWithdrawalAmount, memberWithdrawalCount }) => {
            return (
              <div>
                {memberWithdrawalCount} / {memberWithdrawalAmount}
              </div>
            );
          },
        },
        {
          title: <Txt>{t('fee')}</Txt>,
          key: 'memberWithdrawalFee',
          dataIndex: 'memberWithdrawalFee',
          align: 'right',
          render: (_, { memberWithdrawalFee }) => {
            return (
              <Txt
                strong
                className='text-[#389e0d]'
              >
                {memberWithdrawalFee}
              </Txt>
            );
          },
        },
      ],
    },
    {
      title: <Txt>{t('merchantTransferInColumn')}</Txt>,
      children: [
        {
          title: (
            <Txt>
              {t('qty')}/{t('amt')}
            </Txt>
          ),
          key: 'merchantDepositAmount',
          dataIndex: 'merchantDepositAmount',
          align: 'right',
          render: (_, { merchantDepositAmount, merchantDepositCount }) => {
            return (
              <div>
                {merchantDepositCount} / {merchantDepositAmount}
              </div>
            );
          },
        },
        {
          title: <Txt>{t('fee')}</Txt>,
          key: 'merchantDepositFee',
          dataIndex: 'merchantDepositFee',
          align: 'right',
          render: (_, { merchantDepositFee }) => {
            return (
              <Txt
                strong
                className='text-[#389e0d]'
              >
                {merchantDepositFee}
              </Txt>
            );
          },
        },
      ],
    },
    {
      title: <Txt>{t('merchantTransferOutColumn')}</Txt>,
      children: [
        {
          title: (
            <Txt>
              {t('qty')}/{t('amt')}
            </Txt>
          ),
          key: 'merchantWithdrawalAmount',
          dataIndex: 'merchantWithdrawalAmount',
          align: 'right',
          render: (_, { merchantWithdrawalAmount, merchantWithdrawalCount }) => {
            return (
              <div>
                {merchantWithdrawalCount} / {merchantWithdrawalAmount}
              </div>
            );
          },
        },
        {
          title: <Txt>{t('fee')}</Txt>,
          key: 'merchantWithdrawalFee',
          dataIndex: 'merchantWithdrawalFee',
          align: 'right',
          render: (_, { merchantWithdrawalFee }) => {
            return (
              <Txt
                strong
                className='text-[#389e0d]'
              >
                {merchantWithdrawalFee}
              </Txt>
            );
          },
        },
      ],
    },
    {
      title: (
        <Flex vertical>
          <Txt>{t('merchantWithdrawGasColumn')}</Txt>
          <Txt
            type='secondary'
            className='text-xs'
          >
            ({t('withdraw')}+{t('transferOut')})
          </Txt>
        </Flex>
      ),
      key: 'memberWithdrawalGas',
      dataIndex: 'memberWithdrawalGas',
      align: 'right',
      render: (_, { memberWithdrawalGas, merchantWithdrawalGas }) => {
        return (
          <Txt
            strong
            type='danger'
          >
            {memberWithdrawalGas + merchantWithdrawalGas}
          </Txt>
        );
      },
    },
    { title: <Txt>{t('balanceColumn')}</Txt>, dataIndex: 'balance', align: 'right', key: 'balance' },
  ];

  return { columns };
};

export default useCustomersColumns;
export type { CustomerDataRowInterface };
