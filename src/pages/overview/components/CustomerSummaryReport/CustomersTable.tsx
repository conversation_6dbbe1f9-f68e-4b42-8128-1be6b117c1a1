/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { useCallback, useMemo, useState } from 'react';
import { Space, Spin, Card, Tag, Empty, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { useSummaryCustomer, CustomerData } from '@/api/customer/useSummaryCustomer';
import BtnFuncs from '@/components/BtnFuncs';
import { Title, Txt } from '@/components/TypographyMaster';
import TableAlpha from '@/components/TableAlpha';
import { getEnumKeyByEnumValue } from '@/utils/getEnumKeyByValue';
import { CryptoEnum } from '@/utils';
import { SwapOutlined } from '@ant-design/icons';
import useDataSourceWithoutPagination from '@/hooks/useDataSourceWithoutPagination';
import { ClientMerchantDetailsResult, useClientMerchantDetails } from '@/api/customer/useClientMerchantDetails';
import ButtonAlpha from '@/components/ButtonAlpha';
import CustomerSummaryModal, { CustomerSummarySubmitValues } from '../../CustomerSummaryModal/CustomerSummaryModal';
import useCustomersColumns, { CustomerDataRowInterface } from './useCustomersColumns';

interface ICustomersTableProps {}

const CustomersTable: React.FunctionComponent<ICustomersTableProps> = (props) => {
  // props
  const {} = props || {};

  // states
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [calReport, setCalReport] = useState(false);
  const [calReportValues, setCalReportValues] = useState<CustomerSummarySubmitValues>();

  // hooks
  const { t } = useTranslation('customersTable');

  // Queries
  const { data: customers, isLoading: customersLoading } = useSummaryCustomer({
    params: {
      startDate: calReportValues?.dateRange ? calReportValues.dateRange[0]?.format('YYYY-MM-DD') : undefined,
      endDate: calReportValues?.dateRange ? calReportValues.dateRange[1]?.format('YYYY-MM-DD') : undefined,
      currency: calReportValues?.selectedCoins?.join(','),
    },
  });
  const customerIds = useMemo(() => (customers ? customers.map((customer) => customer.customerId) : []), [customers]);
  const clientMerchantDetails = useClientMerchantDetails({ customerIds }) as Array<ClientMerchantDetailsResult>;

  const { dataSource } = useDataSourceWithoutPagination<CustomerDataRowInterface, CustomerData>({
    txInfo: customers,
    mapper: (item) => {
      const { customerId, customerName, balance } = item;
      const { data: detail } =
        clientMerchantDetails.find((merchantDetails) => merchantDetails.data?.clientId === customerId) || {};

      const children = detail?.merchantDetails.length
        ? detail.merchantDetails.map((merchantDetail) => {
            const {
              merchantId,
              merchantName,
              merchantNumber,
              memberDepositCount,
              memberDepositAmount,
              memberDepositFee,
              memberDepositGas,
              memberWithdrawalCount,
              memberWithdrawalAmount,
              memberWithdrawalFee,
              memberWithdrawalGas,
              merchantDepositCount,
              merchantDepositAmount,
              merchantDepositFee,
              merchantDepositGas,
              merchantWithdrawalCount,
              merchantWithdrawalAmount,
              merchantWithdrawalFee,
              merchantWithdrawalGas,
            } = merchantDetail;

            const column: CustomerDataRowInterface = {
              key: `${customerId}-${merchantId}-${merchantName}`,
              merchantName,
              merchantNumber,
              memberDepositCount,
              memberDepositAmount,
              memberDepositFee,
              memberDepositGas,
              memberWithdrawalCount,
              memberWithdrawalAmount,
              memberWithdrawalFee,
              memberWithdrawalGas,
              merchantDepositCount,
              merchantDepositAmount,
              merchantDepositFee,
              merchantDepositGas,
              merchantWithdrawalCount,
              merchantWithdrawalAmount,
              merchantWithdrawalFee,
              merchantWithdrawalGas,
            };

            return column;
          })
        : undefined;

      const column: CustomerDataRowInterface = {
        key: customerId,
        customerName,
        memberDepositCount: item.memberDepositCount,
        memberDepositAmount: item.memberDepositAmount,
        memberDepositFee: item.memberDepositFee,
        memberDepositGas: item.memberDepositGas,
        memberWithdrawalCount: item.memberWithdrawalCount,
        memberWithdrawalAmount: item.memberWithdrawalAmount,
        memberWithdrawalFee: item.memberWithdrawalFee,
        memberWithdrawalGas: item.memberWithdrawalGas,
        merchantDepositCount: item.merchantDepositCount,
        merchantDepositAmount: item.merchantDepositAmount,
        merchantDepositFee: item.merchantDepositFee,
        merchantDepositGas: item.merchantDepositGas,
        merchantWithdrawalCount: item.merchantWithdrawalCount,
        merchantWithdrawalAmount: item.merchantWithdrawalAmount,
        merchantWithdrawalFee: item.merchantWithdrawalFee,
        merchantWithdrawalGas: item.merchantWithdrawalGas,
        balance,
        children,
      };

      return column;
    },
  });

  const { columns } = useCustomersColumns({});

  const renderNoReportGenerated = useCallback(() => {
    return (
      <div>
        <Empty
          description={t('noReportDescription')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <BtnFuncs
            size='large'
            type='primary'
            shape='round'
            onClick={() => setCalReport(true)}
            iconType='calc'
          >
            {t('reportButtonText')}
          </BtnFuncs>
        </Empty>
      </div>
    );
  }, [t]);

  // Component Render
  if (customersLoading) {
    return (
      <div style={{ textAlign: 'center', marginTop: '20px' }}>
        <Spin size='large' />
      </div>
    );
  }

  return (
    <>
      <Card
        title={
          <Title
            level={3}
            tight
          >
            {t('title')}
          </Title>
        }
      >
        {calReportValues?.dateRange && (
          <Space className='mb-4'>
            <ButtonAlpha
              type='primary'
              icon={<SwapOutlined />}
              onClick={() => setCalReport(true)}
            />
            <Divider type='vertical' />
            <div>
              {t('dateRange')}：<Txt strong> {calReportValues.dateRange[0]?.format('YYYY-MM-DD')}</Txt> {t('to')}{' '}
              <Txt strong>{calReportValues.dateRange[1]?.format('YYYY-MM-DD')}</Txt>
            </div>
            <Divider type='vertical' />
            {calReportValues?.selectedCoins && (
              <div>
                <span>{t('currency')}：</span>{' '}
                {calReportValues.selectedCoins.map((coin) => (
                  <Tag
                    color='blue'
                    key={coin}
                  >
                    {getEnumKeyByEnumValue(CryptoEnum, coin).replace('_', ' ')}
                  </Tag>
                ))}
              </div>
            )}
          </Space>
        )}

        {!customers && !calReportValues ? (
          renderNoReportGenerated()
        ) : (
          <TableAlpha
            columns={columns}
            dataSource={dataSource}
            size='small'
            bordered
            expandable={{
              expandedRowKeys,
              onExpand: (expanded, record: CustomerData) => {
                setExpandedRowKeys(expanded ? [record.customerId] : []);
              },
            }}
          />
        )}
      </Card>
      <CustomerSummaryModal
        open={calReport}
        setOpen={setCalReport}
        onSubmit={(values) => {
          setCalReportValues(values);
          setCalReport(false);
        }}
        initialValues={calReportValues}
      />
    </>
  );
};

export default CustomersTable;
