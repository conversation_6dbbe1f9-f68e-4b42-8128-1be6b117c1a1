// libs
import { useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { Breadcrumb, Tabs } from 'antd';
import { useTranslation } from 'react-i18next';

// components
import LeaseInOrders from './components/LeaseInOrders';
import LeasePlatform from './components/LeasePlatform';
import LeaseOutOrders from './components/LeaseOutOrders';

const LeaseTabKey = {
  leaseInOrders: 'lease-orders',
  leaseOutOrders: 'lease-out-orders',
  leasePlatforms: 'lease-platforms',
};

interface ILeaseProps {}

const Lease: React.FC = (props: ILeaseProps) => {
  // props
  const {} = props || {};

  // states
  const [activeKey, setActiveKey] = useState(LeaseTabKey.leaseInOrders);

  // hooks
  const { t } = useTranslation('lease');

  // compute
  const items = useMemo(
    () => [
      {
        key: LeaseTabKey.leaseInOrders,
        label: t('leaseInOrdersLabel'),
        children: <LeaseInOrders />,
      },
      {
        key: LeaseTabKey.leaseOutOrders,
        label: t('leaseOutOrdersLabel'),
        children: <LeaseOutOrders />,
      },
      {
        key: LeaseTabKey.leasePlatforms,
        label: t('leasePlatformsLabel'),
        children: <LeasePlatform />,
      },
    ],
    [t],
  );

  return (
    <>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('homeBreadcrumb')}</Link>,
          },
          {
            title: t('currentBreadcrumb'),
          },
        ]}
      />
      <Tabs
        activeKey={activeKey.toString()}
        items={items}
        onChange={(value) => {
          setActiveKey(value);
        }}
      />
    </>
  );
};

export default Lease;
