import { useCallback, useState } from 'react';
import { Button, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { LeasePlatformListProps, useLeasePlatformList, LeasePlatformListRes, useActivePlatform } from '@/api';
import SearchMaster from '@/components/SearchMaster';
import TableAlpha from '@/components/TableAlpha';
import { Txt } from '@/components/TypographyMaster';
import SelectAlpha from '@/components/SelectAlpha';
import OpwVfyModal from '@/components/ModalAlpha/OpwVfyModal';
import { useTableStates } from '@/hooks';
import useDataSource from '@/hooks/useDataSource';
import { useNotifyStore } from '@/store';
import useLeasePlatformColumns, { LeasePlatformRowInterface } from './useLeasePlatformColumns';
import EnergyTiersModal from './EnergyTiersModal';
import CreatePlatformModal from './CreatePlatformModal';

type LeasePlatformSearchValues = {
  [key in 'name' | 'address']?: string;
};

interface ILeasePlatformProps {}

const LeasePlatform = (props: ILeasePlatformProps) => {
  // props
  const {} = props || {};

  // states
  const [isActive, setIsActive] = useState<1 | 0 | undefined>();
  const [name, setName] = useState<string>();
  const [address, setAddress] = useState<string>();
  const [orderByDescending, setOrderByDescending] = useState<boolean>(); // page filters
  const [orderBy, setOrderBy] = useState<LeasePlatformListProps['OrderBy']>('id'); // page filters
  const [openCreateModal, setOpenCreateModal] = useState<boolean>(false);
  const [openDetailModal, setOpenDetailModal] = useState<boolean>(false);
  const [openVerifyModal, setOpenVerifyModal] = useState<boolean>(false);
  const [currentPlatform, setCurrentPlatform] = useState<LeasePlatformRowInterface>();

  // hooks
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('leasePlatform');
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});

  const { data, refetch, isPending } = useLeasePlatformList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      // eslint-disable-next-line
      IsActive: isActive !== undefined ? (isActive === 1 ? true : false) : undefined,
      Name: name,
      Address: address,
      OrderByDescending: orderByDescending,
      OrderBy: orderBy,
    },
  });
  const { dataSource } = useDataSource<LeasePlatformRowInterface, LeasePlatformListRes>({
    txInfo: data,
    mapper: (item) => ({ ...item }) as LeasePlatformRowInterface,
  });
  const { columns } = useLeasePlatformColumns({ setCurrentPlatform, setOpenDetailModal, setOpenVerifyModal });
  const { mutate: active, isPending: isPendingActive } = useActivePlatform({
    onSuccess: () => {
      setCurrentPlatform(undefined);
      setOpenVerifyModal(false);
      refetch();
      pushBSQ([{ title: 'UXM Settlement Corp', des: t('activateSuccessDescription') }]);
    },
  });

  // handlers
  const handleSearch = useCallback(
    (values: LeasePlatformSearchValues) => {
      const isAllKeySame = Object.entries(values).every(([key, value]) => {
        if (key === 'name') return value === name;
        if (key === 'address') return value === address;
        return false;
      });
      if (isAllKeySame) refetch();
      else {
        setName(values.name);
        setAddress(values.address);
      }
    },
    [name, address, refetch],
  );
  const handleActive = (values: { code: string }) => {
    if (currentPlatform?.id) active({ platformId: currentPlatform?.id, operationPassword: values.code });
  };

  return (
    <>
      <header className='mb-4 flex flex-wrap items-end justify-between gap-2'>
        <section className='flex flex-wrap gap-x-2 gap-y-1'>
          <Space className='flex flex-col items-start gap-y-1'>
            <Txt
              strong
              type='secondary'
            >
              {t('isActiveLabel')}:
            </Txt>
            <SelectAlpha
              placeholder={t('isActivePlaceholder')}
              value={isActive}
              options={[
                { value: 1, label: t('active') },
                { value: 0, label: t('disable') },
              ]}
              allowClear
              className='w-40'
              onChange={(value) => {
                setIsActive(value);
              }}
            />
          </Space>
          <SearchMaster
            titles={[
              { key: 'name', label: t('searchNameLabel') },
              { key: 'address', label: t('searchAddressLabel') },
            ]}
            onSearch={handleSearch}
          />
        </section>
        <Button
          icon={<PlusOutlined />}
          shape='round'
          type='primary'
          onClick={() => {
            setOpenCreateModal(true);
          }}
        >
          {t('createButtonText')}
        </Button>
      </header>

      <TableAlpha
        {...{
          dataSource,
          columns,
          totalDataLength: data?.totalCount,
          currentPage,
          pageSize,
          setCurrentPage,
          setPageSize,
        }}
        rowKey='id'
        onChange={(_, _filters, sortResults) => {
          const firstSortResults = (() => {
            if (Array.isArray(sortResults)) return sortResults.at(0);
            return sortResults;
          })();
          const { order: orderOrderType, columnKey } = firstSortResults || {};
          setOrderByDescending(() => {
            if (orderOrderType === undefined) return orderOrderType;
            return orderOrderType === 'descend';
          });
          setOrderBy(() => {
            if (columnKey === 'id') return 'id';
            return undefined;
          });
        }}
        loading={isPending}
        size='small'
      />

      <CreatePlatformModal
        open={openCreateModal}
        setOpen={setOpenCreateModal}
        refetch={refetch}
      />

      <OpwVfyModal
        open={openVerifyModal}
        setOpen={setOpenVerifyModal}
        title={t('activeModalTitle')}
        vfyProps={{
          onFinish: handleActive,
          loading: isPendingActive,
        }}
      />

      {currentPlatform && (
        <EnergyTiersModal
          open={openDetailModal}
          setOpen={setOpenDetailModal}
          currentPlatform={currentPlatform}
        />
      )}
    </>
  );
};

export default LeasePlatform;
