import { useCallback, useMemo, useState } from 'react';
import { Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { useLeaseOutOrderList, LeaseOutOrderItemInterface, LeaseOutOrderListRes, LeaseOutOrderListProps } from '@/api';
import TableAlpha from '@/components/TableAlpha';
import SelectAlpha from '@/components/SelectAlpha';
import { Txt } from '@/components/TypographyMaster';
import { useTableStates } from '@/hooks';
import useDataSource from '@/hooks/useDataSource';
import { LeaseOutOrderStatusEnum, leaseOutOrderStatusOptions } from '@/utils';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import useLeaseOutOrdersColumns from './useLeaseOutOrdersColumns';

interface ILeaseOutOrders {}

const LeaseOutOrders = (props: ILeaseOutOrders) => {
  const {} = props || {};

  const [statuses, setStatuses] = useState<Array<LeaseOutOrderStatusEnum>>();
  const [dateRange, setDateRange] = useState<DateRangeOptions>();
  const [orderByDescending, setOrderByDescending] = useState<boolean>(); // page filters
  const [orderBy, setOrderBy] = useState<LeaseOutOrderListProps['OrderBy']>('Id'); // page filters

  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const { data, isPending } = useLeaseOutOrderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      OrderBy: orderBy,
      OrderByDescending: orderByDescending,
      StartTime: dateRange?.from.format(),
      EndTime: dateRange?.to.format(),
      Status: statuses,
    },
  });
  const { columns } = useLeaseOutOrdersColumns({});
  const { dataSource } = useDataSource<LeaseOutOrderItemInterface, LeaseOutOrderListRes>({
    txInfo: data,
    mapper: (item) => ({ ...item }) as LeaseOutOrderItemInterface,
  });
  const { t } = useTranslation('leaseOutOrders');
  const { t: optionsT } = useTranslation('options');

  const translateLeaseOutOrdersStatusOptions = useMemo(
    () => leaseOutOrderStatusOptions.map((option) => ({ ...option, label: optionsT(option.label) })),
    [optionsT],
  );

  const handleChangeDateRange = useCallback((values: DateRangeOptions) => {
    setDateRange(values);
  }, []);

  return (
    <>
      <header className='mb-4 flex flex-col gap-y-2'>
        <DateRange
          loading={isPending}
          defaultValues={dateRange}
          onDateSubmit={handleChangeDateRange}
        />
        <Space className='flex flex-col items-start gap-y-1'>
          <Txt
            strong
            type='secondary'
          >
            {t('statusLabel')}:
          </Txt>
          <SelectAlpha
            placeholder={t('statusPlaceholder')}
            value={statuses}
            mode='multiple'
            options={translateLeaseOutOrdersStatusOptions}
            allowClear
            className='w-40'
            onChange={(value) => {
              setStatuses(value);
            }}
          />
        </Space>
      </header>

      <TableAlpha
        {...{
          dataSource,
          columns,
          totalDataLength: data?.totalCount,
          currentPage,
          pageSize,
          setCurrentPage,
          setPageSize,
        }}
        rowKey='id'
        onChange={(_, _filters, sortResults) => {
          const firstSortResults = (() => {
            if (Array.isArray(sortResults)) return sortResults.at(0);
            return sortResults;
          })();
          const { order: orderOrderType, columnKey } = firstSortResults || {};
          setOrderByDescending(() => {
            if (orderOrderType === undefined) return orderOrderType;
            return orderOrderType === 'descend';
          });
          setOrderBy(() => {
            if (columnKey === 'id') return 'Id';
            return undefined;
          });
        }}
        loading={isPending}
        size='small'
      />
    </>
  );
};

export default LeaseOutOrders;
