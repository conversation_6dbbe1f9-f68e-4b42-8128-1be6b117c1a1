// libs
import { useMemo } from 'react';
import { TableColumnType } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { EnergyTierItem } from '@/api';

// components
import ModalAlpha from '@/components/ModalAlpha';
import TableAlpha from '@/components/TableAlpha';
import { Txt } from '@/components/TypographyMaster';

// utils
import { nTot } from '@/utils';

// pages
import { LeasePlatformRowInterface } from './useLeasePlatformColumns';

interface IEnergyTiersModal {
  open: boolean;
  setOpen: ReactSet<IEnergyTiersModal['open']>;
  currentPlatform: LeasePlatformRowInterface;
}

const EnergyTiersModal = (props: IEnergyTiersModal) => {
  // props
  const { open, setOpen, currentPlatform } = props || {};

  // hooks
  const { t } = useTranslation('energyTiersModal');

  // compute
  const columns: TableColumnType<EnergyTierItem>[] = useMemo(
    () => [
      {
        title: <Txt>{t('leaseCountColumn')}</Txt>,
        key: 'leaseCount',
        align: 'center',
        render: (_, { leaseCount }) => {
          return <span>{leaseCount}</span>;
        },
      },
      {
        title: <Txt>{t('energyConsumptionColumn')}</Txt>,
        key: 'energyConsumption',
        align: 'center',
        render: (_, { energyConsumption }) => {
          return <span>{nTot({ value: energyConsumption, digitsType: 'BandWidth' })}</span>;
        },
      },
      {
        title: <Txt>{t('requiredTrxAmountColumn')}</Txt>,
        key: 'requiredTrxAmount',
        align: 'center',
        render: (_, { requiredTrxAmount }) => {
          return <span>{nTot({ value: requiredTrxAmount, digitsType: 'BandWidth' })}</span>;
        },
      },
    ],
    [t],
  );

  return (
    <ModalAlpha
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
      title={t('title')}
      footer={false}
      maximize
    >
      <TableAlpha
        {...{ columns }}
        dataSource={currentPlatform.energyTiers.map((item, index) => ({ ...item, key: index }))}
        pagination={false}
      />
    </ModalAlpha>
  );
};

export default EnergyTiersModal;
