import { LeaseOutOrderItemInterface } from '@/api';
import { TagLeaseOutStatus } from '@/components/TagAlpha';
import { Txt } from '@/components/TypographyMaster';
import { dateFormator } from '@/hooks';
import { CryptoEnum, nTot } from '@/utils';
import { Flex, TableColumnsType, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface useProps {}

const useLeaseOutOrdersColumns = (props: useProps) => {
  const {} = props || {};

  const { t } = useTranslation('leaseOutOrdersColumns');

  const columns: TableColumnsType<LeaseOutOrderItemInterface> = useMemo(
    () => [
      {
        title: <Txt>{t('id')}</Txt>,
        key: 'id',
        align: 'center',
        sorter: true,
        render: (_, { id }) => {
          return <Txt>{id}</Txt>;
        },
      },
      {
        title: <Txt>{t('addresses')}</Txt>,
        key: 'addresses',
        align: 'center',
        render: (_, { walletAddress, receivingWalletAddress }) => {
          return (
            <Flex gap={4}>
              <Flex
                vertical
                align='start'
              >
                <Txt type='secondary'>{t('clientWallet')}:</Txt>
                <Txt type='secondary'>{t('uxmWallet')}:</Txt>
              </Flex>
              <Flex
                vertical
                align='start'
              >
                <Tooltip title={walletAddress}>
                  <Txt
                    copyable
                    ellipsis
                    className='w-40'
                  >
                    {walletAddress}
                  </Txt>
                </Tooltip>
                <Tooltip title={receivingWalletAddress}>
                  <Txt
                    copyable
                    ellipsis
                    className='w-40'
                  >
                    {receivingWalletAddress}
                  </Txt>
                </Tooltip>
              </Flex>
            </Flex>
          );
        },
      },
      {
        title: <Txt>{t('amount')}</Txt>,
        key: 'amount',
        align: 'center',
        render: (_, { trxAmount, energyAmount }) => {
          return (
            <Flex gap={4}>
              <Flex
                vertical
                align='start'
              >
                <Txt type='secondary'>{t('trx')}:</Txt>
                <Txt type='secondary'>{t('energy')}:</Txt>
              </Flex>
              <Flex
                vertical
                align='start'
              >
                <Txt>{nTot({ value: trxAmount, digitsType: CryptoEnum.TRC20_USDT })}</Txt>
                <Txt>{nTot({ value: energyAmount, digitsType: 'BandWidth' })}</Txt>
              </Flex>
            </Flex>
          );
        },
      },
      {
        title: <Txt>{t('hashes')}</Txt>,
        key: 'hashes',
        align: 'center',
        render: (_, { receivedTxHash, leaseTxHash, reclaimTxHash }) => {
          return (
            <Flex gap={4}>
              <Flex
                vertical
                align='start'
              >
                <Txt type='secondary'>{t('receivedTxHash')}:</Txt>
                <Txt type='secondary'>{t('leaseTxHash')}:</Txt>
                <Txt type='secondary'>{t('reclaimTxHash')}:</Txt>
              </Flex>
              <Flex
                vertical
                align='start'
              >
                <Tooltip title={receivedTxHash}>
                  <Txt
                    copyable
                    ellipsis
                    className='w-40 text-start'
                  >
                    {receivedTxHash}
                  </Txt>
                </Tooltip>
                <Tooltip title={leaseTxHash}>
                  <Txt
                    copyable={!!leaseTxHash}
                    ellipsis
                    className='w-40 text-start'
                  >
                    {leaseTxHash || '--'}
                  </Txt>
                </Tooltip>
                <Tooltip title={reclaimTxHash}>
                  <Txt
                    copyable={!!reclaimTxHash}
                    ellipsis
                    className='w-40 text-start'
                  >
                    {reclaimTxHash || '--'}
                  </Txt>
                </Tooltip>
              </Flex>
            </Flex>
          );
        },
      },
      {
        title: () => <Txt>{t('time')}</Txt>,
        key: 'time',
        align: 'center',
        render: (_, { leaseStartTime, leaseEndTime, createdAt, leaseDurationInMinutes }) => {
          return (
            <Flex gap={4}>
              <Flex
                vertical
                align='start'
              >
                <Txt type='secondary'>{t('createdAt')}:</Txt>
                <Txt type='secondary'>{t('leaseStartTime')}:</Txt>
                <Txt type='secondary'>{t('leaseEndTime')}:</Txt>
                <Txt type='secondary'>{t('leaseDurationInMinutes')}:</Txt>
              </Flex>
              <Flex
                vertical
                align='start'
              >
                <Txt>{dayjs(createdAt).format(dateFormator.accurate)}</Txt>
                <Txt>{dayjs(leaseStartTime).format(dateFormator.accurate)}</Txt>
                {leaseEndTime ? <Txt>{dayjs(leaseEndTime).format(dateFormator.accurate)}</Txt> : '--'}
                {leaseDurationInMinutes ? (
                  <Txt>
                    {leaseDurationInMinutes} {t('minutes')}
                  </Txt>
                ) : (
                  <Txt>--</Txt>
                )}
              </Flex>
            </Flex>
          );
        },
      },
      {
        title: <Txt>{t('status')}</Txt>,
        key: 'status',
        align: 'center',
        render: (_, { status }) => {
          return <TagLeaseOutStatus status={status} />;
        },
      },
    ],
    [t],
  );

  return { columns };
};

export default useLeaseOutOrdersColumns;
