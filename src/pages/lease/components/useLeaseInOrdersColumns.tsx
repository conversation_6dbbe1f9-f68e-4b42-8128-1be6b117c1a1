import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TagLeaseStatus } from '@/components/TagAlpha';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import { nTot } from '@/utils';
import { useUserStore } from '@/store';
import { LeaseInOrderItemInterface } from '@/api';
import { TableColumnsType } from 'antd/lib';

interface useProps {}

const useLeaseInOrdersColumns = (useProps: useProps) => {
  const {} = useProps || {};

  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  const { isDark } = useUserStore();
  const { t } = useTranslation('leaseInOrdersColumns');

  const columns: TableColumnsType<LeaseInOrderItemInterface> = useMemo(
    () => [
      {
        title: <Txt>{t('idColumn')}</Txt>,
        key: 'id',
        align: 'center',
        sorter: true,
        render: (_, { id }) => {
          return <span>{id}</span>;
        },
      },
      {
        title: <Txt>{t('leaseAmountColumn')}</Txt>,
        key: 'leaseAmount',
        align: 'center',
        sorter: true,
        render: (_, { leaseAmount }) => {
          return <span>{nTot({ value: leaseAmount, digitsType: 'BandWidth' })}</span>;
        },
      },
      {
        title: <Txt>{t('paidTrxColumn')}</Txt>,
        key: 'paidTrx',
        align: 'center',
        sorter: true,
        render: (_, { paidTrx }) => {
          return <span>{nTot({ value: paidTrx, digitsType: 'BandWidth' })}</span>;
        },
      },
      {
        title: <Txt>{t('paymentHashColumn')}</Txt>,
        key: 'paymentHash',
        align: 'center',
        render: (_, { paymentHash }) => {
          const isExpanded = expandedKeys.includes(paymentHash);
          const isHovered = hoveredKeys.includes(paymentHash);

          return (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={paymentHash}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: <Txt>{t('statusColumn')}</Txt>,
        key: 'status',
        align: 'center',
        sorter: true,
        render: (_, { status }) => {
          return <TagLeaseStatus status={status} />;
        },
      },
      {
        title: <Txt>{t('platformColumn')}</Txt>,
        key: 'platform',
        align: 'center',
        render: (_, { platform }) => {
          return <span>{platform.name}</span>;
        },
      },
    ],
    [expandedKeys, hoveredKeys, isDark, t],
  );

  return { columns };
};

export default useLeaseInOrdersColumns;
