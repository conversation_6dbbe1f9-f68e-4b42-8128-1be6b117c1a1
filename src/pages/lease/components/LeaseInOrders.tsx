import { useCallback, useMemo, useState } from 'react';
import { Space } from 'antd';
import { useTranslation } from 'react-i18next';
import SearchMaster from '@/components/SearchMaster';
import TableAlpha from '@/components/TableAlpha';
import SelectAlpha from '@/components/SelectAlpha';
import { Txt } from '@/components/TypographyMaster';
import { useTableStates } from '@/hooks';
import useDataSource from '@/hooks/useDataSource';
import { leaseInOrderStatusOptions } from '@/utils';
import { LeaseInOrderItemInterface, LeaseInOrderListProps, LeaseInOrderListRes, useLeaseInOrderList } from '@/api';
import useLeaseInOrdersColumns from './useLeaseInOrdersColumns';

type LeaseInOrdersSearchValues = {
  [key in 'paymentHash' | 'platformId' | 'platformName']?: string;
};

interface ILeaseInOrders {}

const LeaseInOrders = (props: ILeaseInOrders) => {
  const {} = props || {};

  const [paymentHash, setPaymentHash] = useState<string>();
  const [platformId, setPlatformId] = useState<string>();
  const [platformName, setPlatformName] = useState<string>();
  const [status, setStatus] = useState();
  const [orderByDescending, setOrderByDescending] = useState<boolean>(); // page filters
  const [orderBy, setOrderBy] = useState<LeaseInOrderListProps['OrderBy']>('Id'); // page filters

  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const { data, refetch, isPending } = useLeaseInOrderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      Status: status,
      PaymentHash: paymentHash,
      PlatformId: platformId,
      PlatformName: platformName,
      OrderByDescending: orderByDescending,
      OrderBy: orderBy,
    },
  });
  const { columns } = useLeaseInOrdersColumns({});
  const { dataSource } = useDataSource<LeaseInOrderItemInterface, LeaseInOrderListRes>({
    txInfo: data,
    mapper: (item) => ({ ...item }) as LeaseInOrderItemInterface,
  });
  const { t } = useTranslation('leaseInOrders');
  const { t: optionsT } = useTranslation('options');

  const translateLeaseInOrderStatusOptions = useMemo(
    () => leaseInOrderStatusOptions.map((option) => ({ ...option, label: optionsT(option.label) })),
    [optionsT],
  );

  const handleSearch = useCallback(
    (values: LeaseInOrdersSearchValues) => {
      const isAllKeySame = Object.entries(values).every(([key, value]) => {
        if (key === 'paymentHash') return value === paymentHash;
        if (key === 'platformId') return value === platformId;
        if (key === 'platformName') return value === platformName;
        return false;
      });
      if (isAllKeySame) refetch();
      else {
        setPaymentHash(values.paymentHash);
        setPlatformId(values.platformId);
        setPlatformName(values.platformName);
      }
    },
    [paymentHash, platformId, platformName, refetch],
  );

  return (
    <>
      <header className='mb-4 flex flex-wrap items-center gap-2'>
        <Space className='flex flex-col items-start gap-y-1'>
          <Txt
            strong
            type='secondary'
          >
            {t('statusLabel')}:
          </Txt>
          <SelectAlpha
            placeholder={t('statusPlaceholder')}
            value={status}
            options={translateLeaseInOrderStatusOptions}
            allowClear
            className='w-40'
            onChange={(value) => {
              setStatus(value);
            }}
          />
        </Space>
        <SearchMaster
          titles={[
            { key: 'paymentHash', label: t('searchPaymentHashLabel') },
            { key: 'platformId', label: t('searchPlatformIdLabel') },
            { key: 'platformName', label: t('searchPlatformNameLabel') },
          ]}
          onSearch={handleSearch}
        />
      </header>

      <TableAlpha
        {...{
          dataSource,
          columns,
          totalDataLength: data?.totalCount,
          currentPage,
          pageSize,
          setCurrentPage,
          setPageSize,
        }}
        rowKey='id'
        onChange={(_, _filters, sortResults) => {
          const firstSortResults = (() => {
            if (Array.isArray(sortResults)) return sortResults.at(0);
            return sortResults;
          })();
          const { order: orderOrderType, columnKey } = firstSortResults || {};
          setOrderByDescending(() => {
            if (orderOrderType === undefined) return orderOrderType;
            return orderOrderType === 'descend';
          });
          setOrderBy(() => {
            if (columnKey === 'id') return 'Id';
            if (columnKey === 'leaseAmount') return 'LeaseAmount';
            if (columnKey === 'paidTrx') return 'PaidTrx';
            if (columnKey === 'status') return 'Status';
            return undefined;
          });
        }}
        loading={isPending}
        size='small'
      />
    </>
  );
};

export default LeaseInOrders;
