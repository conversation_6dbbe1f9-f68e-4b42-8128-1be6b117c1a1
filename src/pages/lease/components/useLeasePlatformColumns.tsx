import { useMemo, useState } from 'react';
import { TableColumnType } from 'antd';
import { useTranslation } from 'react-i18next';
import { EnergyTierItem } from '@/api';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import BtnFuncs from '@/components/BtnFuncs';
import { useUserStore } from '@/store';
import { TagActivation } from '@/components/TagAlpha';

interface LeasePlatformRowInterface {
  id: number;
  name: string;
  address: string;
  isActive: boolean;
  energyTiers: Array<EnergyTierItem>;
}

interface IUseLeasePlatformColumnsProps {
  currentPlatform?: LeasePlatformRowInterface | undefined;
  setCurrentPlatform: ReactSet<IUseLeasePlatformColumnsProps['currentPlatform']>;
  setOpenDetailModal: ReactSet<boolean>;
  setOpenVerifyModal: ReactSet<boolean>;
}

const useLeasePlatformColumns = (useProps: IUseLeasePlatformColumnsProps) => {
  // props
  const { setCurrentPlatform, setOpenDetailModal, setOpenVerifyModal } = useProps || {};

  // states
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  // hooks
  const { isDark } = useUserStore();
  const { t } = useTranslation('leasePlatformColumns');

  // compute
  const columns: TableColumnType<LeasePlatformRowInterface>[] = useMemo(
    () => [
      {
        title: <Txt>{t('idColumn')}</Txt>,
        key: 'id',
        align: 'center',
        sorter: true,
        render: (_, { id }) => {
          return <span>{id}</span>;
        },
      },
      {
        title: <Txt>{t('nameColumn')}</Txt>,
        key: 'name',
        align: 'center',
        render: (_, { name }) => {
          return <span>{name}</span>;
        },
      },
      {
        title: <Txt>{t('addressColumn')}</Txt>,
        key: 'address',
        align: 'center',
        render: (_, { address }) => {
          const isExpanded = expandedKeys.includes(address);
          const isHovered = hoveredKeys.includes(address);

          return (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={address}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: <Txt>{t('activeColumn')}</Txt>,
        key: 'active',
        align: 'center',
        render: (_, record) => {
          const { isActive } = record;

          return (
            <TagActivation
              activated={isActive}
              className={`${isActive ? '' : 'cursor-pointer'}`}
              onClick={() => {
                if (isActive) return;
                setCurrentPlatform(record);
                setOpenVerifyModal(true);
              }}
            />
          );
        },
      },
      {
        title: () => <Txt className='text-nowrap'>{t('actionColumn')}</Txt>,
        key: 'action',
        align: 'center',
        render: (_, record) => {
          return (
            <BtnFuncs
              className='w-full self-center'
              onClick={() => {
                setCurrentPlatform(record);
                setOpenDetailModal(true);
              }}
              size='small'
              iconType='viewList'
            >
              {t('detailsButtonText')}
            </BtnFuncs>
          );
        },
        width: 100,
      },
    ],
    [expandedKeys, hoveredKeys, isDark, setCurrentPlatform, setOpenDetailModal, setOpenVerifyModal, t],
  );

  return { columns };
};

export default useLeasePlatformColumns;
export type { LeasePlatformRowInterface };
