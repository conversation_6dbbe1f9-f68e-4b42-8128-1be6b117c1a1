// libs
import { Button, Form, Input } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useTranslation } from 'react-i18next';

// api
import { useCreatePlatform } from '@/api';

// components
import ModalAlpha from '@/components/ModalAlpha';

// store
import { useNotifyStore } from '@/store';

interface ICreatePlatformModal {
  open: boolean;
  setOpen: ReactSet<ICreatePlatformModal['open']>;
  refetch: () => void;
}

const CreatePlatformModal = (props: ICreatePlatformModal) => {
  // props
  const { open, setOpen, refetch } = props || {};

  // hooks
  const [form] = useForm();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('createPlatformModal');
  const { mutate: create, isPending: isPendingCreate } = useCreatePlatform({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Settlement Corp', des: t('createSuccessDescription') }]);
      setOpen(false);
      form.resetFields();
      refetch();
    },
  });

  // handlers
  const handleSubmit = ({
    name,
    address,
    operationPassword,
  }: {
    name: string;
    address: string;
    operationPassword: string;
  }) => {
    create({ name, address, operationPassword });
  };

  return (
    <ModalAlpha
      title={t('title')}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
      footer={false}
    >
      <Form
        form={form}
        onFinish={handleSubmit}
        layout='vertical'
      >
        <Form.Item
          label={t('nameLabel')}
          name='name'
          rules={[{ required: true, message: t('nameErrorMessage') }]}
        >
          <Input placeholder={t('namePlaceholder')} />
        </Form.Item>
        <Form.Item
          label={t('addressLabel')}
          name='address'
          rules={[{ required: true, message: t('addressErrorMessage') }]}
        >
          <Input placeholder={t('addressPlaceholder')} />
        </Form.Item>
        <Form.Item
          label={t('operationPasswordLabel')}
          name='operationPassword'
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password placeholder={t('operationPasswordPlaceholder')} />
        </Form.Item>
        <Form.Item className='mb-0'>
          <Button
            htmlType='submit'
            type='primary'
            className='mx-auto flex items-center justify-center rounded-lg'
            loading={isPendingCreate}
            style={{
              backgroundColor: '#2563eb',
              border: 'none',
              color: '#fff',
            }}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </ModalAlpha>
  );
};

export default CreatePlatformModal;
