// libs
import { useState, useMemo, useEffect, useRef } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { TabsProps, Tabs, Breadcrumb } from 'antd';
import { debounce } from 'lodash';
import { useTranslation } from 'react-i18next';

// pages
import StaffAccList from './staff/StaffAccList';
import Client from './client/Client';

interface IAdminProps {}

const Admin: React.FunctionComponent<IAdminProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const navigator = useNavigate();
  const location = useLocation();
  const { t } = useTranslation('admin');

  // states
  const [activeKey, setActiveKey] = useState<'staff' | 'client'>(location.state?.activeKey || 'staff');

  // compute
  const items: TabsProps['items'] = useMemo(() => {
    return [
      {
        key: 'staff',
        label: t('staffTabLabel'),
        children: <StaffAccList />,
      },
      {
        key: 'client',
        label: t('clientTabLabel'),
        children: <Client />,
      },
    ];
  }, [t]);

  // handlers
  const locationStates = useRef(location.state);
  const handleNavDebounce = useMemo(
    () => debounce(() => navigator('.', { state: { ...locationStates.current, activeKey } }), 400),
    [activeKey, navigator],
  );

  // === init ===
  useEffect(() => {
    handleNavDebounce();
    return () => {
      handleNavDebounce.cancel();
    };
  }, [handleNavDebounce]);

  const isTestAutoTab = true;
  useEffect(() => {
    if (import.meta.env.DEV && isTestAutoTab) setActiveKey('client');
  }, [isTestAutoTab]);

  return (
    <>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('homeBreadcrumb')}</Link>,
          },
          {
            title: t('currentBreadcrumb'),
          },
        ]}
      />
      <Tabs
        {...{ activeKey, items }}
        indicator={{ size: (origin) => origin - 20 }}
        onChange={(newActiveKey) => {
          setActiveKey(newActiveKey as typeof activeKey);
        }}
      />
    </>
  );
};

export default Admin;
