// libs
import { Suspense, lazy, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Flex, Segmented } from 'antd';
import { debounce } from 'lodash';
import { useTranslation } from 'react-i18next';

// components
import SpinMaster from '@/components/SpinMaster';
import BtnFuncs from '@/components/BtnFuncs';
import ClientCreateModal from './components/ClientCreateModal';

// pages
import CalReportModal, { CallReportSubmitValues } from './ClientRevenueReport/CalReportModal';
import CalFeesReportModal from './ClientFee/CalFeesReportModal/CalFeesReportModal';

const ClientMerchants = lazy(() => import('./ClientMerchants'));
const ClientWallets = lazy(() => import('./ClientWallets'));
const ClientRevenueReport = lazy(() => import('./ClientRevenueReport'));

type ClientInfoTabTypes = 'MERCHANT_ACCS' | 'MERCHANT_WALLET' | 'BUSINESS_OVERVIEW' | 'ORDER_SETTING' | 'FEE_SETTING';

interface IDepositProps {}

const Client: React.FunctionComponent<IDepositProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const navigator = useNavigate();
  const location = useLocation();
  const { t } = useTranslation('client');

  // states
  const [infoType, setInfoType] = useState<ClientInfoTabTypes>(location.state?.infoType || 'MERCHANT_ACCS');
  const [openClientCreate, setOpenClientCreate] = useState(false);
  const [calReport, setCalReport] = useState(false); // Whether to open a chat window for setting calculation
  const [calReportValues, setCalReportValues] = useState<CallReportSubmitValues>(); // Field results used to calculate bisiness profile
  const [calFeesReport, setCalFeesReport] = useState(false);
  const [, setCalFeesReportValues] = useState<CallReportSubmitValues>();

  // refs
  const originLocationStates = useRef(location.state);

  // compute
  const changeInfoTypeNavD = useMemo(
    () =>
      debounce(
        () =>
          navigator('.', {
            state: originLocationStates.current ? { ...originLocationStates.current, infoType } : { infoType },
          }),
        400,
      ),
    [infoType, navigator],
  );

  // init
  useEffect(() => {
    changeInfoTypeNavD();
    return () => {
      changeInfoTypeNavD.cancel();
    };
  }, [changeInfoTypeNavD]);

  //
  useEffect(() => {
    const isTestInfoType = false;
    if (!import.meta.env.DEV || !isTestInfoType) return;
    setInfoType('BUSINESS_OVERVIEW');
  }, []);

  return (
    <>
      <div>
        <header>
          <Flex
            gap={10}
            className='mb-3 justify-between'
          >
            <Segmented
              options={[
                { label: t('merchantAccountSegmentLabel'), value: 'MERCHANT_ACCS' },
                { label: t('merchantWalletSegmentLabel'), value: 'MERCHANT_WALLET' },
                // { label: t('businessOverviewSegmentLabel'), value: 'BUSINESS_OVERVIEW' },
              ]}
              value={infoType}
              onChange={(newType) => setInfoType(newType as typeof infoType)}
            />

            {infoType === 'MERCHANT_ACCS' && (
              <Button
                icon={<PlusOutlined />}
                shape='round'
                type='primary'
                onClick={() => setOpenClientCreate(true)}
              >
                {t('createButtonText')}
              </Button>
            )}

            {infoType === 'BUSINESS_OVERVIEW' && (
              <BtnFuncs
                shape='round'
                type='primary'
                onClick={() => setCalReport(true)}
                iconType='calc'
              >
                {t('calculateReportButtonText')}
              </BtnFuncs>
            )}
          </Flex>
        </header>

        <main>
          <Suspense
            fallback={
              <div className='my-16 py-16 text-center'>
                <SpinMaster />
              </div>
            }
          >
            {infoType === 'MERCHANT_ACCS' && <ClientMerchants />}
            {infoType === 'MERCHANT_WALLET' && <ClientWallets />}
            {infoType === 'BUSINESS_OVERVIEW' && <ClientRevenueReport {...{ calReportValues }} />}
          </Suspense>
        </main>
      </div>

      {/* Dialogues */}
      <ClientCreateModal
        open={openClientCreate}
        onCancel={() => setOpenClientCreate(false)}
      />

      <CalReportModal
        open={calReport}
        setOpen={setCalReport}
        onSubmit={(values) => {
          setCalReportValues(values);
          setCalReport(false);
        }}
      />
      <CalFeesReportModal
        open={calFeesReport}
        setOpen={setCalFeesReport}
        onSubmit={(values) => {
          setCalFeesReportValues(values);
          setCalFeesReport(false);
        }}
      />
    </>
  );
};

export default Client;
