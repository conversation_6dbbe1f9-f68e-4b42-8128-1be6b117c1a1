// libs
import { useTranslation } from 'react-i18next';

// api
import { MerchantReceiveAddressInfo, MerchantPaymentAddressInfo } from '@/api/merchant';

// components
import { Txt } from '@/components/TypographyMaster';

interface IWalletListProps {
  addressInfos: Array<MerchantReceiveAddressInfo | MerchantPaymentAddressInfo> | undefined;
}

const WalletList: React.FunctionComponent<IWalletListProps> = (props) => {
  // props
  const { addressInfos } = props || {};

  // hooks
  const { t } = useTranslation('clientWalletColumns');

  if (!addressInfos || !addressInfos.length)
    return (
      <div className='w-96'>
        <Txt type='secondary'>{t('emptyLabel')}</Txt>
      </div>
    );

  return addressInfos.map((mapA) => {
    const { addresses } = mapA;

    return (
      <div
        className='w-96'
        key={addresses}
      >
        <Txt>{addresses}</Txt>
      </div>
    );
  });
};

export default WalletList;
