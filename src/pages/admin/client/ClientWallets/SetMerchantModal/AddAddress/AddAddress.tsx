// libs
import { Button, Form, Input, List, Segmented } from 'antd';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

// api
import { useAddPayTronWallet, useAddReceiveTronWallet } from '@/api';
import { MerchantInfo } from '@/api/merchant';

// components
import { Txt } from '@/components/TypographyMaster';

interface IAddAddressProps {
  active?: boolean;
  fromMerchant: MerchantInfo | undefined;
}

const AddAddress: React.FunctionComponent<IAddAddressProps> = (props) => {
  // props
  const { fromMerchant } = props || {};
  const [value, setValue] = useState<'receive' | 'pay'>('receive');
  // hooks
  const [receiveForm] = Form.useForm();
  const [payForm] = Form.useForm();

  // hooks
  const { t } = useTranslation('addMerchantAddress');

  // query
  const { mutate: setReceive, isPending: inSetReceive } = useAddReceiveTronWallet({
    onSettled: () => {
      receiveForm.resetFields();
    },
  });
  const { mutate: setPay, isPending: inSetPay } = useAddPayTronWallet({
    onSettled: () => {
      payForm.resetFields();
    },
  });

  // handlers
  const handleSubmitReceive = useCallback(
    (values: { address: string; code: string }) => {
      if (!fromMerchant) return;
      setReceive({
        merchantId: fromMerchant.id,
        address: values.address,
        operationPassword: values.code,
      });
    },
    [fromMerchant, setReceive],
  );

  const handleSubmitPay = useCallback(
    (values: { code: string }) => {
      if (!fromMerchant) return;
      setPay({
        merchantId: fromMerchant.id,
        operationPassword: values.code,
      });
    },
    [fromMerchant, setPay],
  );

  return (
    <>
      <Segmented
        options={[
          { label: t('title'), value: 'receive' },
          { label: t('paymentWalletTitle'), value: 'pay' },
        ]}
        value={value}
        onChange={setValue}
      />
      {value === 'receive' && (
        <section>
          <Form
            layout='vertical'
            onFinish={handleSubmitReceive}
            form={receiveForm}
          >
            <Form.Item>
              <Txt mark>商戶轉出時，僅允許將資金轉出至指定的目的地錢包</Txt>
            </Form.Item>
            <Form.Item
              name='address'
              label={t('receiveAddressLabel')}
              rules={[{ required: true, message: t('receiveAddressErrorMessage') }]}
            >
              <Input
                autoComplete='off'
                inputMode='text'
                disabled={inSetReceive}
              />
            </Form.Item>

            <Form.Item
              name='code'
              label={t('operationPasswordLabel')}
              rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
            >
              <Input.Password
                autoComplete='off'
                inputMode='text'
                disabled={inSetReceive}
              />
            </Form.Item>

            <Form.Item>
              <Button
                block
                type='primary'
                loading={inSetReceive}
                htmlType='submit'
              >
                {t('submit')}
              </Button>
            </Form.Item>
          </Form>

          <List
            header={<Txt type='secondary'>目前設定：</Txt>}
            dataSource={fromMerchant?.receiveAddress}
            renderItem={(item) => {
              return (
                <div key={item.addresses}>
                  <Txt>{item.addresses}</Txt>
                </div>
              );
            }}
          />
        </section>
      )}
      {value === 'pay' && (
        <section>
          <Form
            layout='vertical'
            onFinish={handleSubmitPay}
            form={payForm}
          >
            <Form.Item>
              <Txt mark>商戶的充值錢包</Txt>
            </Form.Item>
            <Form.Item
              name='code'
              label={t('operationPasswordLabel')}
              rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
            >
              <Input.Password
                autoComplete='off'
                inputMode='text'
                disabled={inSetPay}
              />
            </Form.Item>

            <Form.Item>
              <Button
                block
                type='primary'
                htmlType='submit'
                loading={inSetPay}
              >
                {t('addPaymentWalletButtonText')}
              </Button>
            </Form.Item>
          </Form>

          <List
            header={<Txt type='secondary'>目前設定：</Txt>}
            dataSource={fromMerchant?.paymentAddress}
            renderItem={(item) => {
              return (
                <div
                  key={item.addresses}
                  className='pl-2'
                >
                  <Txt>{item.addresses}</Txt>
                </div>
              );
            }}
          />
        </section>
      )}
    </>
  );
};

export default AddAddress;
