// libs
import { useEffect, useMemo, useRef, useState } from 'react';
import { Tabs } from 'antd';
import { debounce } from 'lodash';
import { useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// api
import { MerchantInfo } from '@/api/merchant';

// components
import ModalAlpha from '@/components/ModalAlpha';
import { Title } from '@/components/TypographyMaster';

// pages
import AddAddress from './AddAddress';
import RemovePayAddress from './RemovePayAddress';

interface ISetMerchantModalProps {
  from: MerchantInfo | undefined;
  setFrom: ReactSet<ISetMerchantModalProps['from']>;
}

const SetMerchantModal: React.FunctionComponent<ISetMerchantModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};
  const { merchantName } = from || {};

  // hooks
  const location = useLocation();
  const navigator = useNavigate();
  const { t } = useTranslation('setMerchantModal');

  // states
  const [activeKey, setActiveKey] = useState<'add' | 'removeReceive' | 'removePay'>(
    location.state?.settingActiveKey || 'add',
  );

  // compute
  const tabsObj: TabObjOptions<typeof activeKey> = useMemo(
    () => ({
      add: {
        key: 'add',
        label: t('addWallet'),
        children: (
          <AddAddress
            fromMerchant={from}
            active={activeKey === 'add'}
          />
        ),
      },

      removeReceive: {
        key: 'removeReceive',
        label: t('removePaymentWallet'),
        children: (
          <RemovePayAddress
            fromMerchant={from}
            active={activeKey === 'removeReceive'}
          />
        ),
      },
      removePay: {
        key: 'removePay',
        label: '移除白名單錢包(fake)',
        children: <div>(fake)</div>,
      },
    }),
    [activeKey, from, t],
  );
  const items = useMemo(() => Object.values(tabsObj), [tabsObj]);

  // handlers
  const originLocationStates = useRef(location.state);
  const changeD = useMemo(
    () =>
      debounce(
        () =>
          navigator('.', {
            state: originLocationStates.current
              ? { ...originLocationStates.current, settingActiveKey: activeKey }
              : { settingActiveKey: activeKey },
          }),
        400,
      ),
    [activeKey, navigator],
  );

  // init
  useEffect(() => {
    changeD();
    return () => {
      changeD.cancel();
    };
  }, [changeD]);

  return (
    <ModalAlpha
      open={!!from}
      onCancel={() => setFrom(undefined)}
      title={
        <Title
          level={3}
          tight
        >
          {t('title')} {merchantName}
        </Title>
      }
      footer={null}
      width={700}
    >
      <Tabs
        {...{ items, activeKey }}
        onChange={(newKey) => {
          setActiveKey(newKey as typeof activeKey);
        }}
      />
    </ModalAlpha>
  );
};

export default SetMerchantModal;
export type { ISetMerchantModalProps };
