// libs
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Checkbox, Empty, Form, Input, Space, TableProps } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { useTranslation } from 'react-i18next';

// api
import { MerchantInfo, useDelPayAddress, useMerchantList } from '@/api/merchant';

// components
import TableAlpha from '@/components/TableAlpha';
import { Title, Txt } from '@/components/TypographyMaster';

// utils
import { nTot } from '@/utils';

interface IRemovePayAddressProps {
  active?: boolean;
  fromMerchant: MerchantInfo | undefined;
}

const RemovePayAddress: React.FunctionComponent<IRemovePayAddressProps> = (props) => {
  // props
  const { active, fromMerchant } = props || {};

  // states
  const [expandedKeys, setExpandedKeys] = useState<Array<number>>([]);
  const [selectedAddress, setSelectedAddress] = useState<Array<string>>([]);

  // hooks
  const { t } = useTranslation('removeMerchantPaymentAddress');

  // query
  const { data: mxInfo } = useMerchantList({
    params: { CustomerName: fromMerchant?.customerName },
    enabled: !!fromMerchant && active,
  });
  const { mutate: del, isPending: inDel } = useDelPayAddress({});

  // handlers
  const handleCheckChange = useCallback((e: CheckboxChangeEvent, address: string) => {
    if (e.target.checked)
      setSelectedAddress((pre) => {
        const newList = [...pre, address];
        return Array.from(new Map(newList.map((mapA) => [mapA, mapA])).values());
      });
    else setSelectedAddress((pre) => pre.filter((filterA) => filterA !== address));
  }, []);

  const handleSubmitDel = useCallback(
    (values: { code: string }) => {
      selectedAddress.forEach((eachA) => {
        del({
          address: eachA,
          operationPassword: values.code,
        });
      });
    },
    [del, selectedAddress],
  );

  // compute
  const dataSource = useMemo(() => {
    if (!mxInfo) return [];
    return mxInfo.items.map((mapI) => {
      const children = mapI.paymentAddress.length
        ? mapI.paymentAddress.map((mapA) => {
            return { ...mapI, key: `${mapI.id}-${mapA}`, address: mapA.addresses };
          })
        : undefined;
      return { ...mapI, key: mapI.id, address: '', children };
    });
  }, [mxInfo]);

  const columns = useMemo(() => {
    const result: TableProps<MerchantInfo & { key: React.Key; address: string }>['columns'] = [
      {
        title: <Txt className='text-nowrap'>{t('merchantColumn')}</Txt>,
        key: 'merchant',
        align: 'center',
        render: (_, { address, merchantName }) => {
          if (!address) return <Txt>{merchantName}</Txt>;
          return <div />;
        },
      },
      {
        title: <Txt className='text-nowrap'>{t('informationColumn')}</Txt>,
        key: 'info',
        align: 'center',
        render: (_, { address, merchantNumber, wallet }) => {
          if (!address)
            return (
              <div className='flex flex-col'>
                <Space size='small'>
                  <Txt type='secondary'>{t('merchantNumber')}:</Txt>
                  <Txt type='secondary'>{merchantNumber}</Txt>
                </Space>

                <Space size='small'>
                  <Txt type='secondary'>{t('balance')}:</Txt>
                  <Txt type='secondary'>{nTot({ value: wallet.balance })}</Txt>
                </Space>
              </div>
            );

          return <div />;
        },
      },
      {
        title: <Txt className='text-nowrap'>{t('walletColumn')}</Txt>,
        key: 'info',
        align: 'center',
        render: (_, { address }) => {
          return <Txt>{address}</Txt>;
        },
      },
      {
        title: <Txt className='text-nowrap'>{t('operateColumn')}</Txt>,
        key: 'info',
        align: 'center',
        render: (_, { address }) => {
          const checked = selectedAddress.includes(address);

          if (address)
            return (
              <Checkbox
                {...{ checked }}
                onChange={(e) => handleCheckChange(e, address)}
              />
            );
          return <div />;
        },
      },
    ];
    return result;
  }, [handleCheckChange, selectedAddress, t]);

  // init
  const isAlreadyDefaultAllExpand = useRef(false);
  useEffect(() => {
    if (isAlreadyDefaultAllExpand.current || !dataSource.length) return;
    setExpandedKeys(dataSource.map((mapD) => mapD.key));
  }, [dataSource]);

  return (
    <>
      <TableAlpha
        {...{ columns, dataSource }}
        size='small'
        rowKey='key'
        expandable={{
          expandedRowKeys: expandedKeys,
          onExpandedRowsChange: (newKeys) => setExpandedKeys(newKeys as typeof expandedKeys),
        }}
        scroll={{ x: 550 }}
      />
      <footer className='mt-4'>
        <header className='flex justify-between'>
          <Title
            level={3}
            tight
          >
            {t('chooseWalletTitle')}
          </Title>

          <Space size='small'>
            <Form onFinish={handleSubmitDel}>
              <Form.Item
                label={<Txt>{t('verificationCodeLabel')}:</Txt>}
                name='code'
                className='mb-0'
                rules={[{ required: true, message: t('verificationCodeErrorMessage') }]}
              >
                <Input.Password disabled={inDel} />
              </Form.Item>
            </Form>

            <Form.Item className='mb-0'>
              <Button
                disabled={!selectedAddress.length}
                type='primary'
                htmlType='submit'
                loading={inDel}
              >
                {t('submit')}
              </Button>
            </Form.Item>
          </Space>
        </header>
        <main>
          {selectedAddress.length ? (
            selectedAddress.map((mapA) => {
              return (
                <div key={mapA}>
                  <Txt>{mapA}</Txt>
                </div>
              );
            })
          ) : (
            <Empty description={t('emptyDescription')} />
          )}
        </main>
      </footer>
    </>
  );
};

export default RemovePayAddress;
