// libs
import { useEffect, useMemo, useRef, useState } from 'react';

// api
import { ClientInterface, ClientMerchantsResult, useClientList, useClientsMerchants } from '@/api/client';
import { MerchantInfo } from '@/api/merchant';

// components
import TableAlpha from '@/components/TableAlpha';

// pages
import useCwxColumns, { ClientWalletRowInterface } from './useCwxColumns';
import SetClientModal from './SetClientModal';
import SetMerchantModal from './SetMerchantModal';

interface IClientWalletProps {}

const ClientWallet: React.FunctionComponent<IClientWalletProps> = (props) => {
  // props
  const {} = props || {};

  // states
  const [expandedClientRow, setExpandedClientRow] = useState<Array<React.Key>>([]);
  const [settingClientFrom, setSettingClientFrom] = useState<ClientInterface>();
  const [settingMerchantFrom, setSettingMerchantFrom] = useState<MerchantInfo>();

  // queries
  const { data: cxInfo, isPending: gettingCxInfo } = useClientList({});
  const clientsNames = useMemo(() => (cxInfo ? cxInfo.map((mapC) => mapC.customerName) : []), [cxInfo]);
  const clientsMxQueries = useClientsMerchants({ customerNames: clientsNames }) as Array<ClientMerchantsResult>;

  // compute
  const dataSource = useMemo(() => {
    if (!cxInfo) return [];
    return cxInfo.map((mapI) => {
      const { customerName, customerId } = mapI;
      const { data: cmxInfo } = clientsMxQueries.find((findQ) => findQ.data?.clientName === customerName) || {};
      const children = cmxInfo?.merchantsInfo.items.length
        ? cmxInfo?.merchantsInfo.items.map((mapM) => {
            const { id, merchantName, receiveAddress, paymentAddress } = mapM;
            const column: ClientWalletRowInterface = {
              key: `${customerId}-${id}`,
              isTestEnv: false,
              merchantName,
              usdtTrc20: {
                receive: receiveAddress,
                payment: paymentAddress,
              },
              customerId,
              merchantId: id,
            };
            return column;
          })
        : undefined;

      const column: ClientWalletRowInterface = {
        key: customerId,
        clientName: customerName,
        isTestEnv: false,
        merchantCount: children?.length,
        customerId,
        children,
      };
      return column;
    });
  }, [clientsMxQueries, cxInfo]);

  const { columns } = useCwxColumns({
    setSettingClientFrom,
    setSettingMerchantFrom,
    cxInfo,
    clientsMxQueries,
    dataSource,
  });

  const firstClientHaveMerchant = useMemo(() => dataSource.find((findD) => findD.children), [dataSource]);

  // init
  const isAlreadyExpandDefault = useRef(false);
  useEffect(() => {
    if (isAlreadyExpandDefault.current || !firstClientHaveMerchant) return;
    isAlreadyExpandDefault.current = true;
    setExpandedClientRow((pre) => [...pre, firstClientHaveMerchant.key]);
  }, [firstClientHaveMerchant]);

  //
  useEffect(() => {
    const isTestSetClient = false;
    if (!isTestSetClient || !import.meta.env.DEV || !cxInfo) return;
    setSettingClientFrom(cxInfo.at(0));
  }, [cxInfo]);

  return (
    <>
      <TableAlpha
        {...{ columns, dataSource }}
        loading={gettingCxInfo}
        bordered
        size='small'
        rowKey='key'
        expandable={{
          expandedRowKeys: expandedClientRow,
          onExpand: (isExpanded, record) => {
            if (isExpanded) setExpandedClientRow(() => [record.key]);
            else setExpandedClientRow((pre) => pre.filter((filterPre) => filterPre !== record.key));
          },
        }}
      />

      {/* Dialogues */}
      <SetClientModal
        from={settingClientFrom}
        setFrom={setSettingClientFrom}
      />

      <SetMerchantModal
        from={settingMerchantFrom}
        setFrom={setSettingMerchantFrom}
      />
    </>
  );
};

export default ClientWallet;
export type { IClientWalletProps };
