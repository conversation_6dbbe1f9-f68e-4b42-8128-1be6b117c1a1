// libs
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Checkbox, Empty, Form, Input, Space, TableProps } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { useTranslation } from 'react-i18next';

// api
import { ClientInterface } from '@/api/client';
import { MerchantInfo, useDelPayAddress, useMerchantList } from '@/api/merchant';

// components
import TableAlpha from '@/components/TableAlpha';
import { Title, Txt } from '@/components/TypographyMaster';

// utils
import { nTot } from '@/utils';

interface IRemovePayAddressProps {
  active?: boolean;
  fromClient: ClientInterface | undefined;
}

const RemovePayAddress: React.FunctionComponent<IRemovePayAddressProps> = (props) => {
  // props
  const { active, fromClient } = props || {};

  // states
  const [expandedKeys, setExpandedKeys] = useState<Array<number>>([]);
  const [selectedMerchantsAddress, setSelectedMerchantsAddress] = useState<
    Array<{ merchantId: number; addresses: Array<string> }>
  >([]);

  // hooks
  const { t } = useTranslation('removeClientPaymentAddress');
  const { mutate: del, isPending: inDel } = useDelPayAddress({});

  // query
  const { data: mxInfo } = useMerchantList({
    params: { CustomerName: fromClient?.customerName },
    enabled: !!fromClient && active,
  });

  // handlers
  const handleCheckChange = useCallback((e: CheckboxChangeEvent, id: number, address: string) => {
    if (e.target.checked)
      setSelectedMerchantsAddress((pre) => {
        const newList = [
          ...pre,
          {
            merchantId: id,
            addresses: [...(pre.find((findM) => findM.merchantId === id)?.addresses || []), address],
          },
        ];
        return Array.from(new Map(newList.map((mapL) => [mapL.merchantId, mapL])).values());
      });
    else
      setSelectedMerchantsAddress((pre) => {
        const newList = [
          ...pre,
          {
            merchantId: id,
            addresses: (pre.find((findM) => findM.merchantId === id)?.addresses || []).filter(
              (filterA) => filterA !== address,
            ),
          },
        ];
        return Array.from(new Map(newList.map((mapL) => [mapL.merchantId, mapL])).values());
      });
  }, []);

  const handleSubmitDel = useCallback(
    (values: { code: string }) => {
      selectedMerchantsAddress.forEach((eachM) => {
        eachM.addresses.forEach((eachA) => {
          del({
            address: eachA,
            operationPassword: values.code,
          });
        });
      });
    },
    [del, selectedMerchantsAddress],
  );

  // compute
  const dataSource = useMemo(() => {
    if (!mxInfo) return [];
    return mxInfo.items.map((mapI) => {
      const children = mapI.paymentAddress.length
        ? mapI.paymentAddress.map((mapA) => {
            return { ...mapI, key: `${mapI.id}-${mapA}`, address: mapA.addresses };
          })
        : undefined;
      return { ...mapI, key: mapI.id, address: '', children };
    });
  }, [mxInfo]);

  const columns = useMemo(() => {
    const result: TableProps<MerchantInfo & { key: React.Key; address: string }>['columns'] = [
      {
        title: <Txt className='text-nowrap'>{t('merchantColumn')}</Txt>,
        key: 'merchant',
        align: 'center',
        render: (_, { address, merchantName }) => {
          if (!address) return <Txt>{merchantName}</Txt>;
          return <div />;
        },
      },
      {
        title: <Txt className='text-nowrap'>{t('informationColumn')}</Txt>,
        key: 'info',
        align: 'center',
        render: (_, { address, merchantNumber, wallet }) => {
          if (!address)
            return (
              <div className='flex space-x-4'>
                <Space size='small'>
                  <Txt type='secondary'>{t('merchantNumber')}:</Txt>
                  <Txt type='secondary'>{merchantNumber}</Txt>
                </Space>

                <Space size='small'>
                  <Txt type='secondary'>{t('balance')}:</Txt>
                  <Txt type='secondary'>{nTot({ value: wallet.balance })}</Txt>
                </Space>
              </div>
            );

          return <div />;
        },
      },
      {
        title: <Txt className='text-nowrap'>{t('walletColumn')}</Txt>,
        key: 'info',
        align: 'center',
        render: (_, { address }) => {
          return <Txt>{address}</Txt>;
        },
      },
      {
        title: <Txt className='text-nowrap'>{t('operateColumn')}</Txt>,
        key: 'info',
        align: 'center',
        render: (_, { id, address }) => {
          const checked = selectedMerchantsAddress
            .find((findM) => findM.merchantId === id)
            ?.addresses?.includes(address);

          if (address)
            return (
              <Checkbox
                {...{ checked }}
                onChange={(e) => handleCheckChange(e, id, address)}
              />
            );
          return <div />;
        },
      },
    ];
    return result;
  }, [handleCheckChange, selectedMerchantsAddress, t]);

  const validSelectedMcxAdx = useMemo(
    () => selectedMerchantsAddress.filter((filterM) => filterM.addresses.length),
    [selectedMerchantsAddress],
  );

  // init
  const isAlreadyDefaultAllExpand = useRef(false);
  useEffect(() => {
    if (isAlreadyDefaultAllExpand.current || !dataSource.length) return;
    setExpandedKeys(dataSource.map((mapD) => mapD.key));
  }, [dataSource]);

  return (
    <>
      <TableAlpha
        {...{ columns, dataSource }}
        size='small'
        rowKey='key'
        expandable={{
          expandedRowKeys: expandedKeys,
          onExpandedRowsChange: (newKeys) => setExpandedKeys(newKeys as typeof expandedKeys),
        }}
        scroll={{ x: 800 }}
      />
      <footer className='mt-4'>
        <header className='flex justify-between'>
          <Title
            level={3}
            tight
          >
            {t('')}
          </Title>

          <Space size='small'>
            <Form onFinish={handleSubmitDel}>
              <Form.Item
                label={<Txt>{t('verificationCodeLabel')}:</Txt>}
                name='code'
                className='mb-0'
                rules={[{ required: true, message: t('verificationCodeErrorMessage') }]}
              >
                <Input.Password disabled={inDel} />
              </Form.Item>
            </Form>

            <Form.Item className='mb-0'>
              <Button
                disabled={!validSelectedMcxAdx.length}
                type='primary'
                htmlType='submit'
                loading={inDel}
              >
                {t('submit')}
              </Button>
            </Form.Item>
          </Space>
        </header>
        <main>
          {validSelectedMcxAdx.length ? (
            validSelectedMcxAdx.map((mapM, index) => {
              const { merchantId, addresses } = mapM;
              const merchantInfo = mxInfo?.items.find((findI) => findI.id === merchantId);
              const { merchantName } = merchantInfo || {};
              return (
                <div
                  key={merchantId}
                  className={`
                    min-h-20 mt-4 border-l border-r px-2
                  `}
                >
                  {index !== 0 && <hr className='mb-2 mt-1' />}
                  <div
                    className={`
                    flex items-start space-x-4
                  `}
                  >
                    <section>
                      <Txt>{merchantName}</Txt>
                    </section>
                    <section>
                      {addresses.map((mapA) => {
                        const mapKey = `${merchantId}-${mapA}`;
                        return (
                          <div key={mapKey}>
                            <Txt>{mapA}</Txt>
                          </div>
                        );
                      })}
                    </section>
                  </div>
                </div>
              );
            })
          ) : (
            <Empty description={t('emptyDescription')} />
          )}
        </main>
      </footer>
    </>
  );
};

export default RemovePayAddress;
