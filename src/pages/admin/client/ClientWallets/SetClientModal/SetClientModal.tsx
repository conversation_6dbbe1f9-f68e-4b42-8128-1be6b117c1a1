// libs
import { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Tabs } from 'antd';
import { debounce } from 'lodash';
import { useTranslation } from 'react-i18next';

// api
import { ClientInterface } from '@/api/client';

// components
import ModalAlpha from '@/components/ModalAlpha';
import { Title } from '@/components/TypographyMaster';

// pages
import RemovePayAddress from './RemovePayAddress';
import AddAddress from './AddAddress';

interface ISetClientModalProps {
  from: ClientInterface | undefined;
  setFrom: ReactSet<ISetClientModalProps['from']>;
}

const SetClientModal: React.FunctionComponent<ISetClientModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};
  const { customerName } = from || {};

  // hooks
  const location = useLocation();
  const navigator = useNavigate();
  const { t } = useTranslation('setClientModal');

  // states
  const [activeKey, setActiveKey] = useState<'add' | 'removeReceive' | 'removePay'>(
    location.state?.settingActiveKey || 'add',
  );

  // compute
  const tabsObj: TabObjOptions<typeof activeKey> = useMemo(
    () => ({
      add: {
        key: 'add',
        label: t('addWallet'),
        children: (
          <AddAddress
            fromClient={from}
            active={activeKey === 'add'}
          />
        ),
      },

      removeReceive: {
        key: 'removeReceive',
        label: t('removePaymentWallet'),
        children: (
          <RemovePayAddress
            fromClient={from}
            active={activeKey === 'removeReceive'}
          />
        ),
      },
      removePay: {
        key: 'removePay',
        label: '移除白名單錢包(fake)',
        children: <div>(fake)</div>,
      },
    }),
    [activeKey, from, t],
  );
  const items = useMemo(() => Object.values(tabsObj), [tabsObj]);

  // handlers
  const originLocationStates = useRef(location.state);
  const changeD = useMemo(
    () =>
      debounce(
        () =>
          navigator('.', {
            state: originLocationStates.current
              ? { ...originLocationStates.current, settingActiveKey: activeKey }
              : { settingActiveKey: activeKey },
          }),
        400,
      ),
    [activeKey, navigator],
  );

  // init
  useEffect(() => {
    changeD();
    return () => {
      changeD.cancel();
    };
  }, [changeD]);

  return (
    <ModalAlpha
      open={!!from}
      onCancel={() => setFrom(undefined)}
      title={
        <Title
          level={3}
          tight
        >
          {t('title')} {customerName}
        </Title>
      }
      footer={null}
      width={900}
    >
      <Tabs
        {...{ items, activeKey }}
        onChange={(newKey) => {
          setActiveKey(newKey as typeof activeKey);
        }}
      />
    </ModalAlpha>
  );
};

export default SetClientModal;
export type { ISetClientModalProps };
