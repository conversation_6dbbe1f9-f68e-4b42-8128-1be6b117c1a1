// libs
import { useCallback, useMemo, useState } from 'react';
import { Button, Col, Divider, Form, Input, List, Row } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useAddPayTronWallet, useAddReceiveTronWallet } from '@/api';
import { ClientInterface } from '@/api/client';
import { useMerchantList } from '@/api/merchant';

// components
import { TagSelects } from '@/components/TagAlpha';
import { Title, Txt } from '@/components/TypographyMaster';

interface IAddAddressProps {
  active?: boolean;
  fromClient: ClientInterface | undefined;
}

const AddAddress: React.FunctionComponent<IAddAddressProps> = (props) => {
  // props
  const { active, fromClient } = props || {};

  // states
  const [selectedRcx, setSelectedRcx] = useState<Array<React.Key>>([]);
  const [selectedPax, setSelectedPax] = useState<Array<React.Key>>([]);

  // hooks
  const [receiveForm] = Form.useForm();
  const [payForm] = Form.useForm();
  const { t } = useTranslation('addClientAddress');

  // query
  const { data: mxInfo } = useMerchantList({
    params: { CustomerName: fromClient?.customerName },
    enabled: !!fromClient && active,
  });
  const { mutate: setReceive, isPending: inSetReceive } = useAddReceiveTronWallet({
    onSettled: () => {
      setSelectedRcx([]);
      receiveForm.resetFields();
    },
  });
  const { mutate: setPay, isPending: inSetPay } = useAddPayTronWallet({
    onSettled: () => {
      setSelectedPax([]);
      payForm.resetFields();
    },
  });

  // compute
  const selectOptions = useMemo(() => {
    if (!mxInfo) return [];
    return mxInfo?.items.map((mapI) => ({ label: mapI.merchantName, value: mapI.id }));
  }, [mxInfo]);

  const selectedMcx = useMemo(() => {
    return mxInfo?.items.filter((filterI) => selectedRcx.includes(filterI.id));
  }, [mxInfo?.items, selectedRcx]);

  const selectedPayMcx = useMemo(() => {
    return mxInfo?.items.filter((filterI) => selectedPax.includes(filterI.id));
  }, [mxInfo?.items, selectedPax]);

  // handlers
  const handleSubmitReceive = useCallback(
    (values: { address: string; code: string }) => {
      if (!selectedMcx) return;
      selectedMcx.forEach((eachM) => {
        setReceive({
          merchantId: eachM.id,
          address: values.address,
          operationPassword: values.code,
        });
      });
    },
    [selectedMcx, setReceive],
  );

  const handleSubmitPay = useCallback(
    (values: { code: string }) => {
      if (!selectedPayMcx) return;
      selectedPayMcx.forEach((eachM) => {
        setPay({
          merchantId: eachM.id,
          operationPassword: values.code,
        });
      });
    },
    [selectedPayMcx, setPay],
  );

  return (
    <>
      <section>
        <Row gutter={8}>
          <Col span={12}>
            <header className='mb-4 flex items-center justify-between'>
              <Title
                level={3}
                tight
              >
                {t('title')}
              </Title>

              <TagSelects
                activeKeys={selectedRcx}
                setActiveKeys={setSelectedRcx}
                options={selectOptions}
              />
            </header>

            <Form
              layout='vertical'
              onFinish={handleSubmitReceive}
              form={receiveForm}
            >
              <Form.Item
                name='address'
                label={t('receiveAddressLabel')}
                rules={[{ required: true, message: t('receiveAddressErrorMessage') }]}
              >
                <Input
                  autoComplete='off'
                  inputMode='text'
                  disabled={inSetReceive}
                />
              </Form.Item>

              <Form.Item
                name='code'
                label={t('operationPasswordLabel')}
                rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
              >
                <Input.Password
                  autoComplete='off'
                  inputMode='text'
                  disabled={inSetReceive}
                />
              </Form.Item>

              <Form.Item className='mb-0 w-full'>
                <Button
                  className='w-full'
                  type='primary'
                  loading={inSetReceive}
                  disabled={!selectedMcx?.length}
                  htmlType='submit'
                >
                  {t('submit')}
                </Button>
              </Form.Item>
            </Form>
          </Col>

          <Col span={12}>
            <Title
              level={3}
              tight
            >
              {t('merchantReceiveWalletTitle')}
            </Title>
            <List
              dataSource={selectedMcx}
              renderItem={(item) => {
                const { merchantName, receiveAddress } = item;

                return (
                  <List.Item>
                    <List.Item.Meta
                      title={merchantName}
                      description={receiveAddress.map((mapA) => {
                        return (
                          <div
                            key={mapA.addresses}
                            className='pl-2'
                          >
                            <Txt>{mapA.addresses}</Txt>
                          </div>
                        );
                      })}
                    />
                  </List.Item>
                );
              }}
            />
          </Col>
        </Row>
      </section>

      <section>
        <Divider />
        <Row gutter={8}>
          <Col span={12}>
            <header className='mb-4 flex items-center justify-between'>
              <Title
                level={3}
                tight
              >
                {t('paymentWalletTitle')}
              </Title>

              <TagSelects
                activeKeys={selectedPax}
                setActiveKeys={setSelectedPax}
                options={selectOptions}
              />
            </header>

            <Form
              layout='vertical'
              onFinish={handleSubmitPay}
              form={payForm}
            >
              <Form.Item
                name='code'
                label={t('operationPasswordLabel')}
                rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
              >
                <Input.Password
                  autoComplete='off'
                  inputMode='text'
                  disabled={inSetPay}
                />
              </Form.Item>

              <Form.Item className='mb-0 mt-12 w-full'>
                <Button
                  className='w-full'
                  type='primary'
                  htmlType='submit'
                  loading={inSetPay}
                  disabled={!selectedPayMcx?.length}
                >
                  {t('addPaymentWalletButtonText')}
                </Button>
              </Form.Item>
            </Form>
          </Col>

          <Col span={12}>
            <Title
              level={3}
              tight
            >
              {t('merchantPaymentWalletTitle')}
            </Title>
            <List
              dataSource={selectedPayMcx}
              renderItem={(item) => {
                const { merchantName, receiveAddress } = item;

                return (
                  <List.Item>
                    <List.Item.Meta
                      title={merchantName}
                      description={receiveAddress.map((mapA) => {
                        return (
                          <div
                            key={mapA.addresses}
                            className='pl-2'
                          >
                            <Txt>{mapA.addresses}</Txt>
                          </div>
                        );
                      })}
                    />
                  </List.Item>
                );
              }}
            />
          </Col>
        </Row>
      </section>
    </>
  );
};

export default AddAddress;
