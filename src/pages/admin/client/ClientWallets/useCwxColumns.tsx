// libs
import { useCallback, useMemo } from 'react';
import { Descriptions, DescriptionsProps, TableColumnsType, Tooltip } from 'antd';
import { RocketOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// api
import { ClientInterface, ClientListRes, ClientMerchantsResult } from '@/api/client';
import { MerchantInfo, MerchantPaymentAddressInfo, MerchantReceiveAddressInfo } from '@/api/merchant';

// components
import { Txt } from '@/components/TypographyMaster';

// pages
import MerchantMenu from './MerchantMenu';
import ClientMenu from './ClientMenu';
import WalletList from './WalletList';

interface ClientWalletRowInterface {
  key: React.Key;
  clientName?: string; // if have name => isClient
  isTestEnv: boolean;
  merchantCount?: number;
  merchantName?: string;
  usdtTrc20?: {
    receive: Array<MerchantReceiveAddressInfo>;
    payment: Array<MerchantPaymentAddressInfo>;
  };
  customerId: number;
  merchantId?: number;
  children?: Array<ClientWalletRowInterface>;
}

type CwxColumnsProps = {
  setSettingClientFrom: ReactSet<ClientInterface | undefined>;
  setSettingMerchantFrom: ReactSet<MerchantInfo | undefined>;
  cxInfo: ClientListRes | undefined;
  clientsMxQueries: Array<ClientMerchantsResult>;
  dataSource: Array<ClientWalletRowInterface>;
};

const useCwxColumns = (useProps: CwxColumnsProps) => {
  // props
  const { setSettingClientFrom, setSettingMerchantFrom, cxInfo, clientsMxQueries, dataSource } = useProps;

  // hooks
  const { t } = useTranslation('clientWalletColumns');

  // handlers
  const onSettingClient = useCallback(
    (record: ClientWalletRowInterface) => {
      setSettingClientFrom(cxInfo?.find((findC) => findC.customerId === record.customerId));
    },
    [cxInfo, setSettingClientFrom],
  );

  const onSettingMerchant = useCallback(
    (record: ClientWalletRowInterface) => {
      clientsMxQueries.forEach((eachCmx) => {
        const result = eachCmx.data?.merchantsInfo.items.find((findM) => findM.id === record.merchantId);
        if (result) setSettingMerchantFrom(result);
      });
    },
    [clientsMxQueries, setSettingMerchantFrom],
  );

  const columns: TableColumnsType<ClientWalletRowInterface> = useMemo(() => {
    return [
      {
        title: <Txt>{t('clientColumn')}</Txt>,
        key: 'client',
        align: 'center',
        width: 120,
        render: (_, obj) => {
          const { clientName, isTestEnv } = obj;
          if (clientName) return <Txt strong>{clientName}</Txt>;
          if (isTestEnv)
            return (
              <Tooltip title={t('testEnvironment')}>
                <RocketOutlined className='text-[#bfbfbf]' />
              </Tooltip>
            );
          return (
            <Tooltip title={t('productEnvironment')}>
              <RocketOutlined className='text-[#52c41a]' />
            </Tooltip>
          );
        },
      },
      {
        title: <Txt>{t('numberOfMerchantsColumn')}</Txt>,
        key: 'merchantCountOrName',
        align: 'center',
        width: 100,
        render: (_, { merchantName, merchantCount }) => {
          return <Txt>{merchantName || merchantCount || '0'}</Txt>;
        },
      },
      {
        title: <Txt>{t('cryptoColumn')}</Txt>,
        key: 'UsdtTrc20',
        align: 'center',
        render: (_, { usdtTrc20 }) => {
          const items: DescriptionsProps['items'] = [
            {
              key: 'rec',
              label: (
                <Tooltip title='商戶轉出時，僅允許將資金轉出至指定的目的地錢包'>
                  <Txt>{t('receiveWallet')}</Txt>
                </Tooltip>
              ),
              children: <WalletList addressInfos={usdtTrc20?.receive} />,
            },
            {
              key: 'pay',
              label: (
                <Tooltip title='商戶的充值錢包'>
                  <Txt>{t('paymentWallet')}</Txt>
                </Tooltip>
              ),
              children: <WalletList addressInfos={usdtTrc20?.payment} />,
            },
          ];

          return (
            <Descriptions
              {...{ items }}
              column={1}
              bordered
            />
          );
        },
      },
      {
        title: <Txt>{t('actionColumn')}</Txt>,
        key: 'action',
        align: 'center',
        render: (_, record, index) => {
          const { clientName } = record;
          const isLastTwo = dataSource.length - index <= 4;
          return (
            <div>
              {clientName ? (
                <ClientMenu
                  {...{ record }}
                  left={isLastTwo}
                  onSetting={onSettingClient}
                />
              ) : (
                <MerchantMenu
                  {...{ record }}
                  left={isLastTwo}
                  onSetting={onSettingMerchant}
                />
              )}
            </div>
          );
        },
      },
    ];
  }, [dataSource.length, onSettingClient, onSettingMerchant, t]);

  return { columns };
};

export default useCwxColumns;
export type { ClientWalletRowInterface, CwxColumnsProps };
