// libs
import { useMemo } from 'react';
import { MoreOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';

// components
import DropdownAlpha from '@/components/DropdownAlpha';

// pages
import type { ClientWalletRowInterface } from './useCwxColumns';

interface IClientMenuProps {
  left: boolean;
  record: ClientWalletRowInterface;
  onSetting: (record: ClientWalletRowInterface) => void;
}

const ClientMenu: React.FunctionComponent<IClientMenuProps> = (props) => {
  // props
  const { left, onSetting, record } = props || {};

  // hooks
  const { t } = useTranslation('clientWalletMenu');

  // compute
  const items = useMemo(
    () => [
      {
        key: '1',
        item: (
          <Button
            size='small'
            onClick={() => onSetting(record)}
            className='w-full'
          >
            {t('settingButtonText')}
          </Button>
        ),
      },
    ],
    [onSetting, record, t],
  );

  return (
    <DropdownAlpha
      {...{ items }}
      icon={<MoreOutlined />}
      buttonProps={{ type: 'primary', size: 'small', className: 'px-1' }}
      dontStickDown={left ? 'left' : undefined}
      topOffset={-10}
    />
  );
};

export default ClientMenu;
