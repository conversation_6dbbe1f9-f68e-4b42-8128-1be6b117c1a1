// libs
import { useMemo } from 'react';
import { MoreOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';

// components
import DropdownAlpha from '@/components/DropdownAlpha';

// pages
import type { CmxColumnsInfterface } from './useCmxColumns';

interface IClientMenuProps {
  left: boolean;
  record: CmxColumnsInfterface;
  onSettingFee: (record: CmxColumnsInfterface) => void;
  onSetttinCallback: (record: CmxColumnsInfterface) => void;
  onCopy: (record: CmxColumnsInfterface) => void;
  onAddMerchant: (record: CmxColumnsInfterface) => void;
}

const ClientMenu: React.FunctionComponent<IClientMenuProps> = (props) => {
  // props
  const { left, onAddMerchant, onSettingFee, onCopy, onSetttinCallback, record } = props || {};

  // hooks
  const { t } = useTranslation('clientMenu');

  // compute
  const items = useMemo(
    () => [
      {
        key: '5',
        item: (
          <Button
            size='small'
            onClick={() => onAddMerchant(record)}
            className='w-full'
          >
            {t('addNewMerchant')}
          </Button>
        ),
      },
      {
        key: '1',
        item: (
          <Button
            size='small'
            onClick={() => onSettingFee(record)}
            className='w-full'
          >
            {t('setHandlingFee')}
          </Button>
        ),
      },
      {
        key: '4',
        item: (
          <Button
            size='small'
            onClick={() => onSetttinCallback(record)}
            className='w-full'
          >
            {t('setOrderCallback')}
          </Button>
        ),
      },
      {
        key: '2',
        item: (
          <Button
            size='small'
            onClick={() => onCopy(record)}
            className='w-full'
          >
            {t('copyClientSetting')}
          </Button>
        ),
      },
    ],
    [onAddMerchant, onCopy, onSettingFee, onSetttinCallback, record, t],
  );

  return (
    <DropdownAlpha
      {...{ items }}
      icon={<MoreOutlined />}
      buttonProps={{ type: 'primary', size: 'small', className: 'px-1' }}
      dontStickDown={left ? 'left' : undefined}
      topOffset={-10}
    />
  );
};

export default ClientMenu;
