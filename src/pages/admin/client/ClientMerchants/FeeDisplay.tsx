// libs
import { useTranslation } from 'react-i18next';

// api
import { MerchantFeeInfo } from '@/api/merchant';

// components
import { TagCryptoType } from '@/components/TagAlpha';

// utils
import { CryptoEnum } from '@/utils';
import { getEnumKeyByEnumValue } from '@/utils/getEnumKeyByValue';

const FeeDisplay = ({ fees }: { fees: MerchantFeeInfo[] }) => {
  // hooks
  const { t } = useTranslation('feeDisplay');

  const columns = fees.length < 4 ? fees.length : 4; // Max 4 columns, reduce if fewer items

  return (
    <div
      className='rounded border bg-gray-50 p-2 shadow'
      style={{
        width: `min(100%, ${columns * 200}px)`,
      }}
    >
      <div
        className='grid gap-4'
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(${columns}, minmax(180px, 1fr))`,
          gap: '8px',
        }}
      >
        {fees.map((item, index) => (
          <div
            key={index}
            className='rounded border bg-white p-3 text-sm shadow-sm'
          >
            <div className='mb-1 font-medium text-gray-700'>
              {t('encryptionType')}:{' '}
              <span className='text-black'>{getEnumKeyByEnumValue(CryptoEnum, item.cryptoType)}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-500'>{t('fixedFee')}:</span>
              <span className='font-semibold text-black'>
                {Number(item.fixedFee).toLocaleString()}
                <TagCryptoType {...{ cryptoType: item.cryptoType, includeName: false, customSize: 16 }} />
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-500'>{t('percentageFee')}:</span>
              <span className='font-semibold text-black'>{Number(item.percentageFee).toLocaleString()} %</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-500'>{t('withdrawFee')}:</span>
              <span className='font-semibold text-black'>
                {Number(item.withdrawalFee).toLocaleString()}
                <TagCryptoType {...{ cryptoType: item.cryptoType, includeName: false, customSize: 16 }} />
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-500'>{t('depositFee')}:</span>
              <span className='font-semibold text-black'>
                {Number(item.depositFee).toLocaleString()}
                <TagCryptoType {...{ cryptoType: item.cryptoType, includeName: false, customSize: 16 }} />
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FeeDisplay;
