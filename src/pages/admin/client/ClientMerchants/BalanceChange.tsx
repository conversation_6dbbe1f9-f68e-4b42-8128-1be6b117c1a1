// libs
import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const BalanceChange = ({ value }: { value: number }) => {
  // states
  const [prevValue, setPrevValue] = useState(value); // Previous balance
  const [change, setChange] = useState(0); // Balance change
  const [animationKey, setAnimationKey] = useState(0);

  // refs
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const diff = value - prevValue;
    if (diff !== 0) {
      // Clear any ongoing animation timeout before setting a new one
      if (timeoutRef.current) clearTimeout(timeoutRef.current);

      setChange(diff); // Update change state
      setAnimationKey((prevKey) => prevKey + 1); // Trigger a new animation

      // Delay updating the previous value to avoid animation sync issues
      timeoutRef.current = setTimeout(() => {
        setChange(0); // Reset change after animation
        setPrevValue(value); // Update previous value safely
      }, 2000); // Animation duration
    }
  }, [value, prevValue]);

  const isPositive = change > 0;

  return (
    <div className='flex items-center justify-end space-x-1'>
      {/* Display the current balance */}
      <span>{value.toLocaleString()}</span>

      <AnimatePresence mode='popLayout'>
        {change !== 0 && (
          <motion.span
            key={animationKey} // Ensure animation restarts for each update
            initial={{ opacity: 0, y: isPositive ? 10 : -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: isPositive ? -10 : 10 }}
            transition={{ duration: 0.5 }}
            className={`flex items-center ${isPositive ? 'text-green-500' : 'text-red-500'}`}
          >
            {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            <span className='ml-1'>{Math.abs(change).toLocaleString()}</span>
          </motion.span>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BalanceChange;
