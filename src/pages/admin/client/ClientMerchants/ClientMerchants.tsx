import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ClientInterface, ClientMerchantsResult, useClientList, useClientsMerchants } from '@/api/client';
import TableAlpha from '@/components/TableAlpha';
import useDataSourceWithoutPagination from '@/hooks/useDataSourceWithoutPagination';
import useAdminSignalRConnection from '@/hooks/useAdminSignalRConnection';
import { nTot } from '@/utils';
import { MerchantBalanceNotifyDto } from '@/pages/withdraw/type';
import useCmxColumns, { CmxColumnsInfterface } from './useCmxColumns';
import SetFeeModal from '../components/SetFeeModal';
import SetCallbackModal from '../components/SetCallbackModal';
import CopyClientMerchantsModal from '../components/CopyClientMerchantsModal';
import CreateMerchantModal from '../components/CreateMerchantModal';
import SetMerchantFeeModal from '../components/SetMerchantFeeModal.tsx';
import AddMerchantAdminModal from '../components/AddMerchantAdminModal';
import SetMerchantCallbackModal from '../components/SetMerchantCallbackModal';
import AddMerchantCsModal from '../components/AddMerchantCsModal';
import BlockMerchantModal from '../components/BlockMerchantModal/BlockMerchantModal';
import DeleteMerchantModal from '../components/DeleteMerchantModal/DeleteMerchantModal';
import SetMasterModal from '../components/SetMasterModal/SetMasterModal';
import MerchantIpWhitelistModal from '../components/MerchantIpWhitelistModal/MerchantIpWhitelistModal';

interface IDepositProps {}
const ClientMerchants: React.FunctionComponent<IDepositProps> = (props) => {
  // props
  const {} = props || {};

  // states
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [settingFeeFrom, setSettingFeeFrom] = useState<CmxColumnsInfterface>();
  const [settingCallbackFrom, setSettingCallbackFrom] = useState<CmxColumnsInfterface>();
  const [copyFrom, setCopyFrom] = useState<CmxColumnsInfterface>();
  const [addMerchantFrom, setAddMerchantFrom] = useState<CmxColumnsInfterface>();
  const [merchantAddAdmin, setMerchantAddAdmin] = useState<CmxColumnsInfterface>();
  const [merchantSettingFee, setMerchantSettingFee] = useState<CmxColumnsInfterface>();
  const [merchantSettingCallback, setMerchantSettingCallback] = useState<CmxColumnsInfterface>();
  const [merchantTransactionDetail, setMerchantTransactionDetail] = useState<CmxColumnsInfterface>();
  const [blockMerchant, setBlockMerchant] = useState<CmxColumnsInfterface>();
  const [deleteMerchant, setDeleteMerchant] = useState<CmxColumnsInfterface>();
  const [merchantSetMaster, setMerchantSetMaster] = useState<CmxColumnsInfterface>();
  const [addMerchantCsFrom, setAddMerchantCsFrom] = useState<CmxColumnsInfterface>();
  const [merchantIpWhiteList, setMerchantIpWhitelist] = useState<CmxColumnsInfterface>();

  // querys
  const { data: cxlInfo, isPending: gettingCxlInfo, refetch } = useClientList({});
  const clientsNames = useMemo(() => (cxlInfo ? cxlInfo.map((mapC) => mapC.customerName) : []), [cxlInfo]);
  const cdxQueries = useClientsMerchants({ customerNames: clientsNames }) as Array<ClientMerchantsResult>;

  const { dataSource, updateItem } = useDataSourceWithoutPagination<CmxColumnsInfterface, ClientInterface>({
    txInfo: cxlInfo,
    mapper: (mapI) => {
      const { customerId, customerName, merchantCount } = mapI;
      const { data: detail } = cdxQueries.find((findQ) => findQ.data?.clientName === customerName) || {};
      const children = detail?.merchantsInfo.items.length
        ? detail.merchantsInfo.items.map((mapD) => {
            const {
              id,
              merchantName,
              userCount,
              wallet,
              fee,
              paymentAddress,
              receiveAddress,
              merchantNumber,
              isOrderLimitReached,
              pendingDepositCount,
              master,
              enabled,
              ipWhitelist,
              transactionStatus,
            } = mapD;
            const column: CmxColumnsInfterface = {
              key: `${customerId}-${id}`,
              client: 1, // 0 => 測試, 1 => 正式
              customerName,
              isOrderLimitReached,
              pendingDepositCount,
              master,
              enabled,
              ipWhitelist,
              merchantCount: '-',
              customerId: null,
              merchantAccountCount: nTot({ value: userCount }),
              accountName: merchantName,
              permission: '-',
              state: transactionStatus,
              ip: '-',
              isClient: false,
              merchantId: id,
              wallet,
              fee,
              paymentAddress,
              receiveAddress,
              merchantNumber,
            };
            return column;
          })
        : undefined;
      const column: CmxColumnsInfterface = {
        key: customerId,
        client: customerName,
        customerName,
        customerId,
        merchantCount,
        ipWhitelist: undefined,
        isOrderLimitReached: undefined,
        master: undefined,
        pendingDepositCount: undefined,
        enabled: undefined,
        merchantAccountCount: '-',
        accountName: '-',
        permission: '-',
        state: '-',
        ip: '-',
        isClient: true,
        children,
        merchantId: null,
        wallet: undefined,
        fee: undefined,
        paymentAddress: undefined,
        receiveAddress: undefined,
        merchantNumber: undefined,
      };
      return column;
    },
  });

  const { registerHandler, unregisterHandler } = useAdminSignalRConnection({});

  const updateBalanceHandler = useCallback(
    (data: MerchantBalanceNotifyDto) => {
      const customer = dataSource?.find((c) => c.children?.find((child) => child.merchantId === data.merchantId));
      const targetChild = customer?.children?.find((child) => child.merchantId === data.merchantId);

      customer?.children?.forEach((child) => {
        if (child.merchantId === data.merchantId) {
          child.wallet = { balance: data.balance, lockedBalance: data.lockedBalance };
        }
      });

      if (customer && targetChild && customer.children)
        updateItem((c) => c.customerId === customer?.customerId, {
          children: customer.children,
        });
    },
    [dataSource, updateItem],
  );
  useEffect(() => {
    registerHandler('MerchantBalanceUpdateNotify', updateBalanceHandler);
    return () => {
      unregisterHandler('MerchantBalanceUpdateNotify', updateBalanceHandler);
    };
  }, [updateBalanceHandler, dataSource, registerHandler, unregisterHandler]);
  // prettier-ignore
  const { columns } = useCmxColumns({ setAddMerchantCsFrom, dataSource, setSettingFeeFrom, setSettingCallbackFrom, setCopyFrom, setAddMerchantFrom, setMerchantSettingFee, setMerchantSettingCallback, setMerchantAddAdmin, setMerchantTransactionDetail, setBlockMerchant, setDeleteMerchant, setMerchantSetMaster, setMerchantIpWhitelist });

  const firstClientHaveMerchant = useMemo(() => dataSource.find((findD) => findD.children), [dataSource]);

  // init
  const isAlreadyExpandDefault = useRef(false);
  useEffect(() => {
    if (isAlreadyExpandDefault.current || !firstClientHaveMerchant) return;
    isAlreadyExpandDefault.current = true;
    setExpandedRowKeys((pre) => [...pre, firstClientHaveMerchant.key]);
  }, [firstClientHaveMerchant]);

  const navigate = useNavigate();
  useEffect(() => {
    if (merchantTransactionDetail) {
      navigate('/private/merchant', { state: { merchantNumber: merchantTransactionDetail.merchantNumber } });
    }
  }, [merchantTransactionDetail, navigate]);
  return (
    <>
      <TableAlpha
        {...{ columns, dataSource }}
        loading={gettingCxlInfo}
        size='small'
        bordered
        expandable={{
          expandedRowKeys,
          onExpand: (isExpand, record: CmxColumnsInfterface) =>
            setExpandedRowKeys((prevKeys) =>
              !isExpand ? prevKeys.filter((k) => k !== record.key) : [...prevKeys, record.key],
            ),
        }}
      />
      <SetFeeModal
        from={settingFeeFrom}
        setFrom={setSettingFeeFrom}
      />

      <SetCallbackModal
        from={settingCallbackFrom}
        setFrom={setSettingCallbackFrom}
      />

      <CopyClientMerchantsModal
        from={copyFrom}
        setFrom={setCopyFrom}
      />

      <CreateMerchantModal
        from={addMerchantFrom}
        setFrom={setAddMerchantFrom}
      />

      <AddMerchantAdminModal
        from={merchantAddAdmin}
        setFrom={setMerchantAddAdmin}
      />

      <SetMerchantFeeModal
        from={merchantSettingFee}
        setFrom={setMerchantSettingFee}
      />

      <SetMerchantCallbackModal
        from={merchantSettingCallback}
        setFrom={setMerchantSettingCallback}
      />

      <AddMerchantCsModal
        from={addMerchantCsFrom}
        setFrom={setAddMerchantCsFrom}
      />
      <SetMasterModal
        from={merchantSetMaster}
        setFrom={setMerchantSetMaster}
      />
      <MerchantIpWhitelistModal
        from={merchantIpWhiteList}
        setFrom={setMerchantIpWhitelist}
      />
      <BlockMerchantModal
        from={blockMerchant}
        setFrom={setBlockMerchant}
        onFinish={refetch}
      />

      <DeleteMerchantModal
        from={deleteMerchant}
        setFrom={setDeleteMerchant}
        onFinish={refetch}
      />
    </>
  );
};

export default ClientMerchants;
export type { IDepositProps };
