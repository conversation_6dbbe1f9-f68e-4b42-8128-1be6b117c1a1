// libs
import { useCallback, useMemo } from 'react';
import { Flex, TableColumnsType, Tooltip } from 'antd';
import { LockOutlined, RocketOutlined, UnlockOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// api
import {
  MerchantWalletInfo,
  MerchantFeeInfo,
  MerchantPaymentAddressInfo,
  MerchantReceiveAddressInfo,
  MerchantMasterInfo,
  MerchantIpWhiteListInfo,
} from '@/api/merchant';

// components
import { Txt } from '@/components/TypographyMaster';

// utils
import { tronMasterStatusOptions } from '@/utils';

// pages
import ClientMenu from './ClientMenu';
import MerchantMenu from './MerchantMenu';
import BalanceChange from './BalanceChange';

interface CmxColumnsInfterface {
  key: React.Key;
  client: string | 1 | 0;
  customerId: number | null;
  customerName: string;
  merchantCount: number | '-';
  merchantAccountCount: string;
  isOrderLimitReached: boolean | undefined;
  pendingDepositCount: number | undefined;
  master: MerchantMasterInfo | undefined;
  accountName: string; // 商戶
  permission: string;
  state: string | number;
  enabled: boolean | undefined;
  ip: string;
  children?: Array<CmxColumnsInfterface>;
  isClient: boolean;
  merchantId: number | null;
  wallet: MerchantWalletInfo | undefined; // only merchant
  ipWhitelist: MerchantIpWhiteListInfo | undefined;
  fee: MerchantFeeInfo[] | undefined; // only merchant
  paymentAddress: MerchantPaymentAddressInfo[] | undefined; // only merchant
  receiveAddress: MerchantReceiveAddressInfo[] | undefined; // only merchant
  merchantNumber: number | undefined; // 商戶號，可能是用於外部識別的唯一編號
}

type CmxColumnsProps = {
  dataSource: Array<CmxColumnsInfterface>;
  setSettingFeeFrom: ReactSet<CmxColumnsInfterface | undefined>;
  setSettingCallbackFrom: ReactSet<CmxColumnsInfterface | undefined>;
  setCopyFrom: ReactSet<CmxColumnsInfterface | undefined>;
  setAddMerchantFrom: ReactSet<CmxColumnsInfterface | undefined>;
  setMerchantSettingFee: ReactSet<CmxColumnsInfterface | undefined>;
  setMerchantSettingCallback: ReactSet<CmxColumnsInfterface | undefined>;
  setMerchantAddAdmin: ReactSet<CmxColumnsInfterface | undefined>;
  setMerchantTransactionDetail: ReactSet<CmxColumnsInfterface | undefined>;
  setAddMerchantCsFrom: ReactSet<CmxColumnsInfterface | undefined>;
  setBlockMerchant: ReactSet<CmxColumnsInfterface | undefined>;
  setDeleteMerchant: ReactSet<CmxColumnsInfterface | undefined>;
  setMerchantSetMaster: ReactSet<CmxColumnsInfterface | undefined>;
  setMerchantIpWhitelist: ReactSet<CmxColumnsInfterface | undefined>;
};

const useCmxColumns = (useProps: CmxColumnsProps) => {
  // props
  const {
    setMerchantSettingFee,
    setMerchantSettingCallback,
    setAddMerchantCsFrom,
    setMerchantAddAdmin,
    setAddMerchantFrom,
    dataSource,
    setSettingFeeFrom,
    setSettingCallbackFrom,
    setCopyFrom,
    setMerchantTransactionDetail,
    setBlockMerchant,
    setDeleteMerchant,
    setMerchantSetMaster,
    setMerchantIpWhitelist,
  } = useProps;

  // hooks
  const { t } = useTranslation('clientMerchantColumns');
  const { t: optionsT } = useTranslation('options');

  // handlers
  const onSettingFee = useCallback((record: CmxColumnsInfterface) => setSettingFeeFrom(record), [setSettingFeeFrom]);

  const onSetttinCallback = useCallback(
    (record: CmxColumnsInfterface) => setSettingCallbackFrom(record),
    [setSettingCallbackFrom],
  );

  const onCopy = useCallback((record: CmxColumnsInfterface) => setCopyFrom(record), [setCopyFrom]);

  const onAddMerchant = useCallback((record: CmxColumnsInfterface) => setAddMerchantFrom(record), [setAddMerchantFrom]);

  const onMerchantSettingFee = useCallback(
    (record: CmxColumnsInfterface) => setMerchantSettingFee(record),
    [setMerchantSettingFee],
  );

  const onMerchantSetttinCallback = useCallback(
    (record: CmxColumnsInfterface) => setMerchantSettingCallback(record),
    [setMerchantSettingCallback],
  );

  const onMerchantAddAdmin = useCallback(
    (record: CmxColumnsInfterface) => setMerchantAddAdmin(record),
    [setMerchantAddAdmin],
  );

  const onAddMerchantCs = useCallback(
    (record: CmxColumnsInfterface) => setAddMerchantCsFrom(record),
    [setAddMerchantCsFrom],
  );
  const onMerchantTransactionDetail = useCallback(
    (record: CmxColumnsInfterface) => setMerchantTransactionDetail(record),
    [setMerchantTransactionDetail],
  );
  const onBlockMerchant = useCallback((record: CmxColumnsInfterface) => setBlockMerchant(record), [setBlockMerchant]);
  const onDeleteMerchant = useCallback(
    (record: CmxColumnsInfterface) => setDeleteMerchant(record),
    [setDeleteMerchant],
  );
  const onSetMaster = useCallback(
    (record: CmxColumnsInfterface) => setMerchantSetMaster(record),
    [setMerchantSetMaster],
  );
  const onSetMerchantIpWhitelist = useCallback(
    (record: CmxColumnsInfterface) => setMerchantIpWhitelist(record),
    [setMerchantIpWhitelist],
  );
  // compute
  const columns: TableColumnsType<CmxColumnsInfterface> = useMemo(
    () => [
      {
        title: <Txt>{t('clientColumn')}</Txt>,
        dataIndex: 'client',
        key: 'client',
        align: 'center',
        fixed: 'left',
        width: 180,
        render: (_, { client, merchantCount }) => (
          <Flex justify='space-between'>
            <Txt strong>{typeof client === 'string' && client}</Txt>
            <div>
              <Txt type='secondary'>
                <Tooltip title={t('merchantCountColumn')}>{typeof client === 'string' && `(${merchantCount})`}</Tooltip>
              </Txt>
            </div>
          </Flex>
        ),
      },
      {
        title: <Txt>{t('accountNameColumn')}</Txt>,
        key: 'accountName',
        fixed: 'left',
        align: 'center',
        width: 140,
        render: (_, { accountName, isClient }) => {
          if (isClient) return '-';
          return <Txt>{accountName}</Txt>;
        },
      },
      {
        title: <Txt>{t('serialNumber')}</Txt>,
        key: 'merchantNumber',
        fixed: 'left',
        align: 'center',
        width: 140,
        render: (_, { merchantNumber, isClient }) => {
          if (isClient) return '-';
          return <Txt copyable>{merchantNumber}</Txt>;
        },
      },
      {
        title: t('enabledColumn'),
        key: 'enabled',
        fixed: 'left',
        align: 'center',
        width: 80,
        render: (_, { isClient, enabled }) => {
          if (isClient) return '-';
          return (
            <div>
              <Txt type='secondary'>
                {enabled === false && (
                  <Tooltip title={t('blocked')}>
                    <RocketOutlined className='text-[#ff4d4f]' />
                  </Tooltip>
                )}
                {enabled === true && (
                  <Tooltip title={t('active')}>
                    <RocketOutlined className='text-[#389e0d]' />
                  </Tooltip>
                )}
              </Txt>
            </div>
          );
        },
      },
      {
        title: t('merchantAccountCountColumn'),
        key: 'merchantAccountCount',
        align: 'center',
        width: 100,
        render: (_, { isClient, merchantAccountCount, master }) => {
          if (isClient) return '-';
          return (
            <Tooltip title={`${t('merchantAccountCountColumn')}/${t('maxUser')}`}>
              <Txt code>
                {merchantAccountCount}/{master?.maxMerchantUsers}
              </Txt>
            </Tooltip>
          );
        },
      },
      // {
      //   title: <Txt>{t('permissionColumn')}</Txt>,
      //   dataIndex: 'permission',
      //   key: 'permission',
      //   align: 'center',
      // },
      {
        title: <Txt>USDT TRC20 {t('feeColumn')}</Txt>,
        align: 'center',
        children: [
          // 等之後有多幣種在放
          // {
          //   title: <Txt>{t('encryptionType')}</Txt>,
          //   dataIndex: 'fee',
          //   key: 'fee',
          //   align: 'center',
          //   render: (_, { fee }) => {
          //     if (!fee || fee.length === 0) return '-';

          //     const firstFee = fee[0];
          //     return <TagCryptoType {...{ cryptoType: firstFee.cryptoType, includeName: false }} />;
          //   },
          // },
          {
            title: t('percentageFee'),
            dataIndex: 'fee',
            key: 'fee',
            align: 'right',
            width: 100,
            render: (_, { fee }) => {
              if (!fee || fee.length === 0) return '-';

              const firstFee = fee[0];
              return (
                <div>
                  {firstFee.percentageFee}
                  {/* <Txt
                    type='secondary'
                    className='text-xs'
                  >
                    {' '}
                  </Txt> */}
                </div>
              );
            },
          },
          {
            title: t('fixedFee'),
            dataIndex: 'fee',
            key: 'fee',
            align: 'right',
            width: 100,
            render: (_, { fee }) => {
              if (!fee || fee.length === 0) return '-';

              const firstFee = fee[0];
              return (
                <div>
                  {firstFee.fixedFee}
                  <Txt
                    type='secondary'
                    className='text-xs'
                  >
                    {' '}
                    USDT
                  </Txt>
                </div>
              );
            },
          },
          {
            title: t('depositFee'),
            dataIndex: 'fee',
            key: 'fee',
            align: 'right',
            width: 100,
            render: (_, { fee }) => {
              if (!fee || fee.length === 0) return '-';

              const firstFee = fee[0];
              return (
                <div>
                  {firstFee.depositFee}{' '}
                  {/* <Txt
                    type='secondary'
                    className='text-xs'
                  >
                    {' '}
                    %
                  </Txt> */}
                </div>
              );
            },
          },
          {
            title: t('withdrawFee'),
            dataIndex: 'fee',
            key: 'fee',
            align: 'right',
            width: 100,
            render: (_, { fee }) => {
              if (!fee || fee.length === 0) return '-';

              const firstFee = fee[0];
              return (
                <div>
                  {firstFee.withdrawalFee}
                  <Txt
                    type='secondary'
                    className='text-xs'
                  >
                    {' '}
                    USDT
                  </Txt>
                </div>
              );
            },
          },
        ],
      },
      {
        title: <Txt>{t('masterInfoColumn')}</Txt>,
        align: 'center',
        children: [
          {
            title: t('tolerance'),
            dataIndex: 'master',
            key: 'master',
            align: 'right',
            width: 160,
            render: (_, { master }) => {
              if (!master) return '-';

              return (
                <div>
                  {master.allowedDepositTolerance}
                  <Txt
                    type='secondary'
                    className='text-xs'
                  >
                    {' '}
                    USDT
                  </Txt>
                </div>
              );
            },
          },
          {
            title: t('timeout'),
            dataIndex: 'master',
            key: 'master',
            align: 'right',
            width: 140,
            render: (_, { master }) => {
              if (!master) return '-';

              return (
                <div>
                  {master.depositTimeoutMinutes}
                  <Txt
                    type='secondary'
                    className='text-xs'
                  >
                    {' '}
                    min
                  </Txt>
                </div>
              );
            },
          },
          {
            title: t('maxDeposit'),
            dataIndex: 'master',
            key: 'master',
            align: 'right',
            width: 140,
            render: (_, { master, pendingDepositCount, isOrderLimitReached }) => {
              if (!master) return '-';

              return (
                <Tooltip title={`${t('deposit')}/${t('maxDeposit')}`}>
                  <Txt
                    code
                    type={isOrderLimitReached ? 'danger' : undefined}
                  >
                    {pendingDepositCount}/{master.maxPendingDepositOrders}
                  </Txt>
                </Tooltip>
              );
            },
          },
          {
            title: t('maxUserDeposit'),
            dataIndex: 'master',
            key: 'master',
            align: 'right',
            width: 140,
            render: (_, { master }) => {
              if (!master) return '-';

              return (
                <div>
                  {master.maxUserPendingDepositOrders}
                  <Txt
                    type='secondary'
                    className='text-xs'
                  >
                    {' '}
                    {t('qty')}
                  </Txt>
                </div>
              );
            },
          },
          {
            title: t('stateColumn'),
            dataIndex: 'state',
            key: 'state',
            align: 'center',
            width: 160,
            render: (_, { state }) => {
              return (
                <span>
                  {optionsT(tronMasterStatusOptions.find((statusOption) => statusOption.value === state)?.label || '-')}
                </span>
              );
            },
          },
        ],
      },
      // {
      //   title: <Txt>{t('feeColumn')}</Txt>,
      //   dataIndex: 'fee',
      //   key: 'fee',
      //   align: 'center',
      //   render: (_, { fee, key }) => {
      //     if (!fee || fee.length === 0) return '-';

      //     const firstFee = fee[0];
      //     const extraCount = fee.length - 1;

      //     const stringKey = key.toString();
      //     const isExpanded = !!expandedRowKeys[stringKey];
      //     return (
      //       <div className='relative min-w-[240px] rounded border p-2  text-sm shadow-sm'>
      //         {/* Fee Rows */}
      //         <div className='grid grid-cols-[1fr_auto_1fr] gap-y-1'>
      //           {/* Row 1 */}
      //           <div className='flex items-center'>
      //             <span className='text-gray-500'>{t('encryptionType')}:</span>
      //             <span className='ml-1'>
      //               <TagCryptoType {...{ cryptoType: firstFee.cryptoType, includeName: false }} />
      //             </span>
      //           </div>
      //           <div className='mx-2 h-4 w-px bg-gray-300'>{}</div>
      //           <div className='flex items-center'>
      //             <span className='text-gray-500'>{t('fixedFee')}:</span>
      //             <span className='ml-1 font-semibold'>{firstFee.fixedFee}</span>
      //           </div>

      //           {isExpanded && (
      //             <>
      //               {/* Row 2 */}
      //               <div className='flex items-center'>
      //                 <span className='text-gray-500'>{t('percentageFee')}:</span>
      //                 <span className='ml-1 font-semibold'>{firstFee.percentageFee}</span>
      //               </div>
      //               <div className='mx-2 h-4 w-px bg-gray-300'>{}</div>
      //               <div className='flex items-center'>
      //                 <span className='text-gray-500'>{t('withdrawFee')}:</span>
      //                 <span className='ml-1 font-semibold'>{firstFee.withdrawalFee}</span>
      //               </div>
      //               {/* Row 3 */}
      //               <div className='flex items-center'>
      //                 <span className='text-gray-500'>{t('depositFee')}:</span>
      //                 <span className='ml-1 font-semibold'>{firstFee.depositFee}</span>
      //               </div>
      //             </>
      //           )}
      //         </div>

      //         {/* Expand/Collapse Button at the Bottom */}
      //         <div className='relative mt-2 flex justify-center'>
      //           <Button
      //             size='small'
      //             type='link'
      //             onClick={() => toggleExpand(key)}
      //             icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
      //           >
      //             {isExpanded ? t('closeButtonText') : t('expandButtonText')}
      //           </Button>

      //           <div className='absolute right-0 top-[50%] translate-y-[-50%]'>
      //             {extraCount > 0 && (
      //               <Popover
      //                 content={<FeeDisplay fees={fee} />}
      //                 title={t('costDetails')}
      //                 trigger='click'
      //               >
      //                 <Button
      //                   size='small'
      //                   type='link'
      //                   className='ml-2'
      //                 >
      //                   +{extraCount}
      //                 </Button>
      //               </Popover>
      //             )}
      //           </div>
      //         </div>
      //       </div>
      //     );
      //   },
      // },
      // {
      //   title: <Txt>{t('masterInfoColumn')}</Txt>,
      //   key: 'master',
      //   align: 'center',
      //   render: (_, { master, isOrderLimitReached, pendingDepositCount, key }) => {
      //     if (!master) return '-';

      //     const stringKey = key.toString();
      //     const isExpanded = !!expandedMasterKeys[stringKey];

      //     const orderLimitIcon = isOrderLimitReached ? (
      //       <CloseCircleTwoTone twoToneColor='#ff4d4f' />
      //     ) : (
      //       <CheckCircleTwoTone twoToneColor='#389e0d' />
      //     );

      //     return (
      //       <div className='relative min-w-[240px] rounded border p-2 text-sm shadow-sm'>
      //         {/* Master Rows */}
      //         <div className='grid grid-cols-[1fr_auto_1fr] gap-y-1'>
      //           {/* Row 1 - Allowed Deposit Tolerance */}
      //           <div className='flex items-center'>
      //             <span className='text-gray-500'>{t('tolerance')}:</span>
      //             <span className='ml-1 font-semibold'>{master.allowedDepositTolerance}</span>
      //           </div>
      //           <div className='mx-2 h-4 w-px bg-gray-300'>{}</div>
      //           <div className='flex items-center'>
      //             <span className='text-gray-500'>{t('timeout')}:</span>
      //             <span className='ml-1 font-semibold'>{master.depositTimeoutMinutes}</span>
      //           </div>

      //           {/* Expanded Rows */}
      //           {isExpanded && (
      //             <>
      //               {/* Row 2 - Max Pending Deposits */}
      //               <div className='flex items-center'>
      //                 <span className='text-gray-500'>{t('maxDeposit')}:</span>
      //                 <span className='ml-1 font-semibold'>{master.maxPendingDepositOrders}</span>
      //               </div>
      //               <div className='mx-2 h-4 w-px bg-gray-300'>{}</div>
      //               <div className='flex items-center'>
      //                 <span className='text-gray-500'>{t('maxUser')}:</span>
      //                 <span className='ml-1 font-semibold'>{master.maxMerchantUsers}</span>
      //               </div>

      //               {/* Row 3 - New Fields */}
      //               <div className='flex items-center'>
      //                 <span className='text-gray-500'>{t('orderLimit')}:</span>
      //                 <span className='ml-1'>{orderLimitIcon}</span>
      //               </div>
      //               <div className='mx-2 h-4 w-px bg-gray-300'>{}</div>
      //               <div className='flex items-center'>
      //                 <span className='text-gray-500'>{t('deposit')}:</span>
      //                 <span className='ml-1 font-semibold'>{pendingDepositCount}</span>
      //               </div>
      //             </>
      //           )}
      //         </div>

      //         {/* Expand/Collapse Button */}
      //         <div className='relative mt-2 flex justify-center'>
      //           <Button
      //             size='small'
      //             type='link'
      //             onClick={() => toggleMasterExpand(key)}
      //             icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
      //           >
      //             {isExpanded ? t('closeButtonText') : t('expandButtonText')}
      //           </Button>
      //         </div>
      //       </div>
      //     );
      //   },
      // },

      // {
      //   title: <Txt>{t('ipAddressColumn')}</Txt>,
      //   dataIndex: 'ip',
      //   key: 'ip',
      //   align: 'center',
      // },

      {
        title: <Txt>{t('ipWhitelistColumn')}</Txt>,
        dataIndex: 'ipWhitelist',
        key: 'ipWhitelist',
        align: 'center',
        width: 100,
        render: (_, record) => {
          if (record.isClient) return '-';

          return record.ipWhitelist?.enableIpWhitelist ? (
            <Tooltip title={t('enabled')}>
              <UnlockOutlined style={{ fontSize: '16px', color: '#389e0d' }} />
            </Tooltip>
          ) : (
            <Tooltip title={t('disabled')}>
              <LockOutlined style={{ fontSize: '16px', color: '#ff4d4f' }} />
            </Tooltip>
          );
        },
      },
      {
        title: <Txt>{t('walletColumn')}</Txt>,
        key: 'wallet',
        align: 'center',
        fixed: 'right',
        width: 120,
        render: (_, { wallet }) => {
          const { balance = 0 } = wallet || {};
          return (
            <Tooltip title={`${t('balance')}`}>
              {/* Total Balance */}
              <div className='text-[#389e0d]'>
                {/* <BalanceChange value={lockedBalance + balance} /> */}
                <BalanceChange value={balance} />
              </div>
              {/* Locked Balance */}
              {/* <div className='text-xs'>
                <BalanceChange value={lockedBalance} />
              </div> */}
            </Tooltip>
          );
        },
      },
      {
        title: <Txt>{t('actionColumn')}</Txt>,
        key: 'action',
        align: 'center',
        // fixed: 'right',
        width: 60,
        render: (_, record, index) => {
          const { isClient } = record;
          const isLastTwo = dataSource.length - index <= 4;
          return (
            <div className='relative'>
              {isClient ? (
                <ClientMenu
                  {...{ record, onSettingFee, onSetttinCallback, onCopy, onAddMerchant }}
                  left={isLastTwo}
                />
              ) : (
                <MerchantMenu
                  {...{
                    record,
                    onMerchantSettingFee,
                    onMerchantSetttinCallback,
                    onMerchantAddAdmin,
                    onMerchantTransactionDetail,
                    onBlockMerchant,
                    onDeleteMerchant,
                    onSetMaster,
                    onSetMerchantIpWhitelist,
                  }}
                  onAddCs={onAddMerchantCs}
                  left={isLastTwo}
                />
              )}
            </div>
          );
        },
      },
    ],
    [
      dataSource.length,
      onAddMerchant,
      onAddMerchantCs,
      onCopy,
      onMerchantAddAdmin,
      onMerchantSettingFee,
      onMerchantSetttinCallback,
      onSettingFee,
      onSetttinCallback,
      onBlockMerchant,
      onDeleteMerchant,
      onSetMerchantIpWhitelist,
      onSetMaster,
      onMerchantTransactionDetail,
      t,
      optionsT,
    ],
  );
  return { columns };
};

export default useCmxColumns;
export type { CmxColumnsInfterface, CmxColumnsProps };
