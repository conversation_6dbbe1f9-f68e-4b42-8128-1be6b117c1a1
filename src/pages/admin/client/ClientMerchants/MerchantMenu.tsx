// libs
import { useMemo } from 'react';
import { Button } from 'antd';
import { MoreOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// components
import DropdownAlpha from '@/components/DropdownAlpha';

// store
import { useUserStore } from '@/store';

// pages
import type { CmxColumnsInfterface } from './useCmxColumns';

interface IMerchantMenuProps {
  left: boolean;
  record: CmxColumnsInfterface;
  onMerchantAddAdmin: (record: CmxColumnsInfterface) => void;
  onMerchantSettingFee: (record: CmxColumnsInfterface) => void;
  onMerchantSetttinCallback: (record: CmxColumnsInfterface) => void;
  onMerchantTransactionDetail: (record: CmxColumnsInfterface) => void;
  onBlockMerchant: (record: CmxColumnsInfterface) => void;
  onDeleteMerchant: (record: CmxColumnsInfterface) => void;
  onSetMaster: (record: CmxColumnsInfterface) => void;
  onAddCs: (record: CmxColumnsInfterface) => void;
  onSetMerchantIpWhitelist: (record: CmxColumnsInfterface) => void;
}

const MerchantMenu: React.FunctionComponent<IMerchantMenuProps> = (props) => {
  // props
  const {
    left,
    onMerchantAddAdmin,
    onMerchantSettingFee,
    onMerchantSetttinCallback,
    onMerchantTransactionDetail,
    onBlockMerchant,
    onDeleteMerchant,
    onSetMaster,
    onSetMerchantIpWhitelist,
    record,
  } = props || {};

  // hooks
  const { info } = useUserStore();
  const { t } = useTranslation('merchantMenu');

  const items = useMemo(() => {
    const tabs = [
      {
        key: '1',
        item: (
          <Button
            size='small'
            onClick={() => onMerchantAddAdmin(record)}
            className='w-full'
          >
            {t('addAdministrator')}
          </Button>
        ),
      },
      {
        key: '2',
        item: (
          <Button
            size='small'
            onClick={() => onMerchantSettingFee(record)}
            className='w-full'
          >
            {t('setHandlingFee')}
          </Button>
        ),
      },
      {
        key: '3',
        item: (
          <Button
            size='small'
            onClick={() => onMerchantSetttinCallback(record)}
            className='w-full'
          >
            {t('setCallback')}
          </Button>
        ),
      },
      {
        key: '4',
        item: (
          <Button
            size='small'
            onClick={() => onSetMaster(record)}
            className='w-full'
          >
            {t('setMasterInfo')}
          </Button>
        ),
      },
      {
        key: '5',
        item: (
          <Button
            size='small'
            onClick={() => onMerchantTransactionDetail(record)}
            className='w-full'
          >
            {t('viewMerchantTransaction')}
          </Button>
        ),
      },
      {
        key: '6',
        item: (
          <Button
            size='small'
            onClick={() => onSetMerchantIpWhitelist(record)}
            className='w-full'
          >
            {t('ipWhitelist')}
          </Button>
        ),
      },
    ];

    const extraTabs = [
      {
        key: '7',
        item: (
          <Button
            size='small'
            onClick={() => onBlockMerchant(record)}
            className='w-full'
            type='default'
            danger
          >
            {record.enabled ? t('block') : t('unblock')}
          </Button>
        ),
      },
      {
        key: '8',
        item: (
          <Button
            size='small'
            onClick={() => onDeleteMerchant(record)}
            className='w-full'
            type='primary'
            danger
          >
            {t('delete')}
          </Button>
        ),
      },
    ];

    if (!info?.roles.includes('SystemCS')) return tabs.concat(extraTabs);
    return tabs;
  }, [
    onMerchantAddAdmin,
    onMerchantSettingFee,
    onMerchantSetttinCallback,
    record,
    info?.roles,
    t,
    onBlockMerchant,
    onDeleteMerchant,
    onMerchantTransactionDetail,
    onSetMaster,
    onSetMerchantIpWhitelist,
  ]);
  return (
    <DropdownAlpha
      {...{ items }}
      icon={<MoreOutlined />}
      buttonProps={{ size: 'small', className: 'px-1' }}
      dontStickDown={left ? 'left' : undefined}
      topOffset={-10}
    />
  );
};

export default MerchantMenu;
