// libs
import { useEffect, useState } from 'react';
import { Form, Modal } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

// api
import { useMerchantList } from '@/api/merchant';

// utils
import { ClientEnvironmentEnum, LimitTimeInterval } from '@/utils';

// pages
import CreateClientMerchantsForm, { CreateClientMerchantsValues } from '../ClientCreateModal/CreateClientMerchantsForm';
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

interface ICopyClientMerchantsModalProps {
  from: CmxColumnsInfterface | undefined;
  setFrom: ReactSet<ICopyClientMerchantsModalProps['from']>;
}

const CopyClientMerchantsModal: React.FunctionComponent<ICopyClientMerchantsModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};

  // states
  const [isAlreadySetFormFields, setIsAlreadySetFormFields] = useState(false);

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('copyClientMerchantsModal');

  // mutation
  const { data, isPending } = useMerchantList({
    enabled: !!from,
    params: { CustomerName: from?.customerName },
  });

  useEffect(() => {
    if (!from || !data) return;
    const createValues: CreateClientMerchantsValues = {
      clientName: from?.customerName,
      items: data.items.map((mapI) => {
        const item: CreateClientMerchantsValues['items']['0'] = {
          merchantName: mapI.merchantName,
          merchantEnvType: ClientEnvironmentEnum.Formal,
          deposit: {
            amountRange: { from: 1234, to: 2345 },
            trc20Fee: 23,
            erc20Fee: 23,
            vfyAutoMode: true,
            autoOffset: 23,
            payTimeLimit: LimitTimeInterval.OneH,
            limitTimes: {
              limitTime: LimitTimeInterval.OneH,
              limitTimes: 3,
            },
            dailyLimit: {
              limitTimes: 5,
              resetAt: dayjs().set('h', 0).set('m', 0).set('s', 0),
            },
            white: [],
          },
          withdraw: {
            amountRange: { from: 1234, to: 2345 },
            trc20Fee: 23,
            erc20Fee: 23,
            vfyAutoMode: true,
            autoOffset: 23,
            payTimeLimit: LimitTimeInterval.OneH,
            limitTimes: {
              limitTime: LimitTimeInterval.OneH,
              limitTimes: 3,
            },
            dailyLimit: {
              limitTimes: 5,
              resetAt: dayjs().set('h', 0).set('m', 0).set('s', 0),
            },
            white: [],
          },
          transfer: {
            amountRange: { from: 1234, to: 2345 },
            trc20Fee: 23,
            erc20Fee: 23,
            vfyAutoMode: true,
            autoOffset: 23,
            payTimeLimit: LimitTimeInterval.OneH,
            limitTimes: {
              limitTime: LimitTimeInterval.OneH,
              limitTimes: 3,
            },
            dailyLimit: {
              limitTimes: 5,
              resetAt: dayjs().set('h', 0).set('m', 0).set('s', 0),
            },
            white: [],
          },
        };
        return item;
      }),
    };
    form.setFieldsValue(createValues);
    setIsAlreadySetFormFields(true);
  }, [data, form, from]);

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={!!from}
      onCancel={() => setFrom(undefined)}
      afterOpenChange={(isOpen) => {
        if (!isOpen) {
          form.resetFields();
          setIsAlreadySetFormFields(false);
        }
      }}
    >
      <CreateClientMerchantsForm
        loading={isPending || !isAlreadySetFormFields}
        {...{ form }}
      />
    </Modal>
  );
};

export default CopyClientMerchantsModal;
export type { ICopyClientMerchantsModalProps };
