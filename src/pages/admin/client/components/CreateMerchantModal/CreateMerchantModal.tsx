// libs
import { Form, Modal, Input, Button } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';

// api
import { useCreateMerchant } from '@/api/merchant';

// utils
import queryKeys from '@/utils/queryKeys';

// pages
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

type CreateMerchantValues = {
  merchantName: string;
  operationPassword: string;
};

interface ICreateMerchantModalProps {
  from: CmxColumnsInfterface | undefined;
  setFrom: ReactSet<ICreateMerchantModalProps['from']>;
}

const CreateMerchantModal: React.FunctionComponent<ICreateMerchantModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};

  // hooks
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { t } = useTranslation('createMerchantModal');

  // mutate
  const { mutate: createMerchant, isPending: inCreateMerchant } = useCreateMerchant({
    onSuccess: () => {
      form.resetFields();
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successNotificationDescription') },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: CreateMerchantValues) => {
      if (!from || !from.customerId) return;
      createMerchant({
        customerId: from.customerId,
        merchantName: values.merchantName,
        operationPassword: values.operationPassword,
      });
    },
    [createMerchant, from],
  );

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={!!from}
      onCancel={() => setFrom(undefined)}
      afterOpenChange={(isOpen) => {
        if (!isOpen) {
          queryClient.invalidateQueries({ queryKey: queryKeys.query.cliensMerchants(from?.customerName) });
        }
      }}
    >
      <Form
        {...{ form }}
        variant='filled'
        autoComplete='off'
        onFinish={handleSubmit}
        layout='vertical'
      >
        {/*  */}
        <Form.Item
          label={t('merchantNameLabel')}
          name='merchantName'
          rules={[{ required: true, message: t('merchantNameErrorMessage') }]}
        >
          <Input
            placeholder={t('merchantNamePlaceholder')}
            className='min-w-[120px]'
            autoComplete='merchantName'
            inputMode='text'
          />
        </Form.Item>

        <Form.Item
          name='operationPassword'
          label={t('operationPasswordLabel')}
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password placeholder={t('operationPasswordPlaceholder')} />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            loading={inCreateMerchant}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateMerchantModal;
export type { CreateMerchantValues, ICreateMerchantModalProps };
