// libs
import { Input } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { MerchantInfo } from '@/api/merchant';

// components
import { Title, Txt } from '@/components/TypographyMaster';

// utils
import { valueToLabel, cryptoEnumOptions, nTot } from '@/utils';

interface IFeeDetailProps {
  merchant: MerchantInfo | undefined;
  setMerchant: ReactSet<MerchantInfo | undefined>;
}

const FeeDetail: React.FunctionComponent<IFeeDetailProps> = (props) => {
  // props
  const { merchant } = props || {};

  // hooks
  const { t } = useTranslation('feeDetail');

  return merchant?.fee.map((mapF, index) => {
    const { cryptoType, fixedFee, percentageFee, depositFee, withdrawalFee } = mapF;
    const key = `${cryptoType}-${index}`;

    return (
      <main
        key={key}
        className='flex w-48 flex-col space-y-4'
      >
        <header className='flex items-center justify-between'>
          <Title
            level={3}
            tight
          >
            {valueToLabel(cryptoType, cryptoEnumOptions) as string}
          </Title>
        </header>

        <section className='flex flex-col'>
          <Txt>{t('fixedFeeLabel')}:</Txt>
          <Input
            readOnly
            value={nTot({ value: fixedFee })}
          />
        </section>

        <section className='flex flex-col'>
          <Txt>{t('percentageFeeLabel')}:</Txt>
          <Input
            readOnly
            value={nTot({ value: percentageFee })}
            suffix='%'
          />
        </section>

        <section className='flex flex-col'>
          <Txt>{t('depositFeeLabel')}:</Txt>
          <Input
            readOnly
            value={nTot({ value: depositFee })}
          />
        </section>

        <section className='flex flex-col'>
          <Txt>{t('withdrawFeeLabel')}:</Txt>
          <Input
            readOnly
            value={nTot({ value: withdrawalFee })}
          />
        </section>
      </main>
    );
  });
};

export default FeeDetail;
