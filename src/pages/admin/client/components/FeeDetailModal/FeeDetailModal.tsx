// libs
import { Space } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { MerchantInfo } from '@/api/merchant';

// components
import ModalAlpha from '@/components/ModalAlpha';
import { Title } from '@/components/TypographyMaster';

// pages
import FeeDetail from './FeeDetail';

interface IFeeDetailModalProps {
  merchant: MerchantInfo | undefined;
  setMerchant: ReactSet<MerchantInfo | undefined>;
}

const FeeDetailModal: React.FunctionComponent<IFeeDetailModalProps> = (props) => {
  // props
  const { merchant, setMerchant } = props || {};

  // hooks
  const { t } = useTranslation('feeDetailModal');

  return (
    <ModalAlpha
      open={!!merchant}
      onCancel={() => setMerchant(undefined)}
      footer={null}
      title={
        <header>
          <Title level={3}>{t('title')}</Title>
          <Space>
            <Title
              level={5}
              type='secondary'
            >
              {t('merchantNameLabel')}: {merchant?.merchantName}
            </Title>
          </Space>
        </header>
      }
      width='fit-content'
    >
      <FeeDetail {...{ merchant, setMerchant }} />
    </ModalAlpha>
  );
};

export default FeeDetailModal;
