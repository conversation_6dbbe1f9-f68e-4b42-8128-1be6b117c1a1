import { Modal, Form, InputNumber, Input, Button, Space, Radio } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSetMasterDetail } from '@/api/merchant/useSetMasterDetail';
import { TronMasterStatusEnum, tronMasterStatusOptions } from '@/utils';
import { useSetMerchantStatus } from '@/api/merchant';
import { useQueryClient } from '@tanstack/react-query';
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

type SetMasterValues = {
  allowedDepositTolerance: number;
  depositTimeoutMinutes: number;
  maxMerchantUsers: number;
  maxPendingDepositOrders: number;
  maxUserPendingDepositOrders: number;
  operationPassword: string;
};

interface ISetMasterModalProps {
  from: CmxColumnsInfterface | undefined;
  setFrom: ReactSet<ISetMasterModalProps['from']>;
}

const SetMasterModal: React.FunctionComponent<ISetMasterModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};
  const { master } = from || {};

  // states
  const [status, setStatus] = useState<TronMasterStatusEnum>();

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('setMasterModal');
  const { t: optionsT } = useTranslation('options');
  const queryClient = useQueryClient();

  const handleClose = () => {
    setFrom(undefined);
    form.resetFields(['operationPassword']);
  };

  // Mutation Hook
  const { mutate: setMaster, isPending: inSetMaster } = useSetMasterDetail({});
  const { mutate: setMerchantStatus, isPending: pendingSetMerchantStatus } = useSetMerchantStatus({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['client', 'merchants'] });
      handleClose();
    },
  });

  const translateTronMasterStatusOptions = useMemo(
    () => tronMasterStatusOptions.map((option) => ({ ...option, label: optionsT(option.label) })),
    [optionsT],
  );

  useEffect(() => {
    if (from) {
      form.setFieldsValue({
        allowedDepositTolerance: master?.allowedDepositTolerance || 0,
        depositTimeoutMinutes: master?.depositTimeoutMinutes || 0,
        maxMerchantUsers: master?.maxMerchantUsers || 0,
        maxPendingDepositOrders: master?.maxPendingDepositOrders || 0,
        maxUserPendingDepositOrders: master?.maxUserPendingDepositOrders || 0,
      });
      setStatus(from.state as TronMasterStatusEnum);
    }
    // eslint-disable-next-line
  }, [from, form]);

  // Submit Handler
  const handleSubmit = useCallback(
    (values: SetMasterValues) => {
      if (!from) return;
      if (!from.merchantId) return;
      if (status === undefined) return;

      const {
        allowedDepositTolerance,
        depositTimeoutMinutes,
        maxMerchantUsers,
        maxPendingDepositOrders,
        maxUserPendingDepositOrders,
        operationPassword,
      } = values;

      // API Call
      setMaster({
        merchantId: from.merchantId, // Pass Merchant ID
        operationPassword,
        allowedDepositTolerance,
        depositTimeoutMinutes,
        maxMerchantUsers,
        maxPendingDepositOrders,
        maxUserPendingDepositOrders,
      });

      setMerchantStatus({ merchantId: from?.merchantId, newStatus: status, operationPassword });
    },
    [from, setMaster, setMerchantStatus, status],
  );

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={!!from}
      onCancel={handleClose}
    >
      <Form
        {...{ form }}
        variant='filled'
        autoComplete='off'
        onFinish={handleSubmit}
        layout='vertical'
      >
        <Space
          size='large'
          align='baseline'
        >
          {/* Allowed Deposit Tolerance */}
          <Form.Item
            label={t('allowedDepositToleranceLabel')}
            name='allowedDepositTolerance'
            rules={[{ required: true, message: t('allowedDepositToleranceErrorMessage') }]}
          >
            <InputNumber
              min={0}
              step={1}
              placeholder={t('allowedDepositTolerancePlaceholder')}
              className='min-w-40'
            />
          </Form.Item>

          {/* Deposit Timeout */}
          <Form.Item
            label={t('depositTimeoutLabel')}
            name='depositTimeoutMinutes'
            rules={[{ required: true, message: t('depositTimeoutErrorMessage') }]}
          >
            <InputNumber
              min={1}
              step={1}
              placeholder={t('depositTimeoutPlaceholder')}
              className='min-w-40'
            />
          </Form.Item>
        </Space>

        <Space
          size='large'
          align='baseline'
        >
          {/* Max Merchant Users */}
          <Form.Item
            label={t('maxMerchantUsersLabel')}
            name='maxMerchantUsers'
            rules={[{ required: true, message: t('maxMerchantUsersErrorMessage') }]}
          >
            <InputNumber
              min={1}
              step={1}
              placeholder={t('maxMerchantUsersPlaceholder')}
              className='min-w-40'
            />
          </Form.Item>

          {/* Max Pending Deposit Orders */}
          <Form.Item
            label={t('maxPendingDepositOrdersLabel')}
            name='maxPendingDepositOrders'
            rules={[{ required: true, message: t('maxPendingDepositOrdersErrorMessage') }]}
          >
            <InputNumber
              min={0}
              step={1}
              placeholder={t('maxPendingDepositOrdersPlaceholder')}
              className='min-w-40'
            />
          </Form.Item>
        </Space>

        {/* Max User Pending Deposit Orders */}
        <Form.Item
          label={t('maxUserPendingDepositOrdersLabel')}
          name='maxUserPendingDepositOrders'
          rules={[{ required: true, message: t('maxUserPendingDepositOrdersErrorMessage') }]}
        >
          <InputNumber
            min={0}
            step={1}
            placeholder={t('maxUserPendingDepositOrdersPlaceholder')}
            className='min-w-40'
          />
        </Form.Item>

        <Form.Item label={t('statusLabel')}>
          <Radio.Group
            value={status}
            options={translateTronMasterStatusOptions}
            onChange={(event) => {
              setStatus(event.target.value);
            }}
          />
        </Form.Item>

        {/* Operation Password */}
        <Form.Item
          name='operationPassword'
          label={t('operationPasswordLabel')}
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password placeholder={t('operationPasswordPlaceholder')} />
        </Form.Item>
        {/* Submit Button */}
        <Form.Item className='mb-0'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            loading={inSetMaster || pendingSetMerchantStatus}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SetMasterModal;
export type { ISetMasterModalProps };
