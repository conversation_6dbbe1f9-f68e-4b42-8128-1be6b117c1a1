// libs
import { Modal, Input, Form, Button } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

// api
import { useBlockMerchant } from '@/api/merchant/useBlockMerchant';

// pages
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

// Props Interface
type BlockMerchantValues = {
  operationPassword: string;
};

interface IBlockMerchantModalProps {
  from: CmxColumnsInfterface | undefined; // Merchant details
  setFrom: ReactSet<IBlockMerchantModalProps['from']>;
  onFinish: () => void;
}

const BlockMerchantModal: React.FunctionComponent<IBlockMerchantModalProps> = (props) => {
  // props
  const { from, setFrom, onFinish } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('blockMerchantModal');

  // Mutation hook for blocking/unblocking merchant
  const { mutate: toggleBlockMerchant, isPending: inToggleBlockMerchant } = useBlockMerchant({
    onSuccess: () => {
      setFrom(undefined); // Close modal on success
      form.resetFields(); // Reset form fields
      onFinish();
    },
  });

  // Submit Handler
  const handleSubmit = useCallback(
    (values: BlockMerchantValues) => {
      if (!from || !from.merchantId) return; // Ensure merchant data exists
      toggleBlockMerchant({
        merchantId: from.merchantId,
        operationPassword: values.operationPassword,
      });
    },
    [from, toggleBlockMerchant],
  );

  return (
    <Modal
      title={t('title')} // Modal title
      footer={false} // No footer for cleaner look
      open={!!from} // Modal is open if 'from' is defined
      onCancel={() => setFrom(undefined)} // Close modal on cancel
    >
      <Form
        {...{ form }}
        variant='filled'
        autoComplete='off'
        onFinish={handleSubmit}
        layout='vertical'
      >
        {/* Operation Password Input */}
        <Form.Item
          label={t('operationPasswordLabel')}
          name='operationPassword'
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password
            placeholder={t('operationPasswordPlaceholder')}
            className='min-w-[120px]'
            autoComplete='off'
          />
        </Form.Item>

        {/* Submit Button */}
        <Form.Item className='mb-0'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            loading={inToggleBlockMerchant} // Show loading spinner while processing
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default BlockMerchantModal;
export type { IBlockMerchantModalProps };
