// libs
import { Modal, Form, Input, Button } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

// api
import { useAddMerchantCs } from '@/api/merchant';

// pages
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

type SetMerchantFeeValues = {
  userName: string;
  nickName: string;
  password: string;
};

interface IAddMerchantCsModalProps {
  from: CmxColumnsInfterface | undefined;
  setFrom: ReactSet<IAddMerchantCsModalProps['from']>;
}

const AddMerchantCsModal: React.FunctionComponent<IAddMerchantCsModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('addMerchantCsModal');

  // mutation
  const { mutate: addAdmin, isPending: inAdd } = useAddMerchantCs({
    onSuccess: () => {
      form.resetFields();
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successNotificationDescription') },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: SetMerchantFeeValues) => {
      if (!from || !from.merchantId) return;
      const { userName, password, nickName } = values;
      addAdmin({
        userName,
        password,
        nickName,
        merchantId: from.merchantId,
      });
    },
    [addAdmin, from],
  );

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={!!from}
      onCancel={() => setFrom(undefined)}
    >
      <Form
        {...{ form }}
        variant='filled'
        autoComplete='off'
        onFinish={handleSubmit}
        layout='vertical'
      >
        {/*  */}
        <Form.Item
          label={t('usernameLabel')}
          name='userName'
          rules={[{ required: true, message: t('usernameErrorMessage') }]}
        >
          <Input
            placeholder={t('usernamePlaceholder')}
            className='min-w-[120px]'
            autoComplete='off'
            inputMode='text'
          />
        </Form.Item>

        {/*  */}
        <Form.Item
          label={t('nickNameLabel')}
          name='nickName'
          rules={[{ required: true, message: t('nickNameErrorMessage') }]}
        >
          <Input
            placeholder={t('nickNamePlaceholder')}
            className='min-w-[120px]'
            autoComplete='off'
            inputMode='text'
          />
        </Form.Item>

        {/*  */}
        <Form.Item
          label={t('passwordLabel')}
          name='password'
          rules={[{ required: true, message: t('passwordErrorMessage') }]}
        >
          <Input.Password
            placeholder={t('passwordPlaceholder')}
            className='min-w-[120px]'
            autoComplete='off'
            inputMode='text'
          />
        </Form.Item>

        {/*  */}
        <Form.Item
          label={t('confirmPasswordLabel')}
          name='checkPwd'
          rules={[
            {
              required: true,
              message: t('confirmPasswordErrorMessage'),
            },
            {
              validator: (_, value) => {
                const password = form.getFieldValue('password');
                if (password === value) return Promise.resolve();
                return Promise.reject(Error(t('passwordNotMatchMessage')));
              },
            },
          ]}
          dependencies={['password']}
        >
          <Input.Password
            placeholder={t('confirmPasswordPlaceholder')}
            className='min-w-[120px]'
            autoComplete='off'
            inputMode='text'
          />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            loading={inAdd}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddMerchantCsModal;
export type { IAddMerchantCsModalProps };
