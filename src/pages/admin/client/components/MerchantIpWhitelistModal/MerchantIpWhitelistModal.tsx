// libs
import React, { useRef, useState } from 'react';
import { Button, Space, Tag, Typography } from 'antd';
import { PlusOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// api
import { useWhitelistIps, WhitelistIpInterface, WhitelistRes } from '@/api/whitelist/useWhiteList';

// components
import ModalAlpha from '@/components/ModalAlpha';
import TableAlpha from '@/components/TableAlpha';
import { OpwVfyRef, OpwVfy } from '@/components/ModalAlpha/OpwVfyModal';

// hooks
import useDataSource from '@/hooks/useDataSource';

// pages
import { useDeleteWhitelist } from './useDeleteWhitelist';
import { useUpdateWhitelist } from './useUpdateWhitelist';
import { useAddWhitelist } from './useAddWhitelist';
import { useToggleWhitelist } from './useToggleWhitelist';
import useWhitelistColumns from './useWhitelistColumn';
import AddIpModal from './AddIpModal';
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

const { Text } = Typography;

interface IMerchantIpWhitelistModalProps {
  from: CmxColumnsInfterface | undefined;
  setFrom: ReactSet<IMerchantIpWhitelistModalProps['from']>;
}

const MerchantIpWhitelistModal: React.FC<IMerchantIpWhitelistModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};

  // states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [opwVisible, setOpwVisible] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<WhitelistIpInterface | null>(null);
  const [actionType, setActionType] = useState<'save' | 'delete' | 'add' | 'toggle'>('save');
  const [loading, setLoading] = useState<boolean>(false);
  const [whitelistStatus, setWhitelistStatus] = useState(from?.ipWhitelist?.enableIpWhitelist);
  const opwRef = useRef<OpwVfyRef>(null);
  const [addModalVisible, setAddModalVisible] = useState<boolean>(false);

  // hooks
  const { t } = useTranslation('merchantIpWhitelistModal');

  const { data, isPending, refetch } = useWhitelistIps({
    params: { merchantId: from?.merchantId?.toString(), PageNumber: currentPage, PageSize: pageSize },
  });

  const { dataSource } = useDataSource<WhitelistIpInterface, WhitelistRes>({
    txInfo: data,
    mapper: (item) => ({
      ipAddress: item.ipAddress,
      description: item.description,
      createdAt: item.createdAt,
    }),
  });

  const { mutate: updateWhitelist } = useUpdateWhitelist({ onSuccess: () => refetch() });
  const { mutate: deleteWhitelist } = useDeleteWhitelist({ onSuccess: () => refetch() });
  const { mutate: addWhitelist } = useAddWhitelist({ onSuccess: () => refetch() });
  const { mutate: toggleWhitelist } = useToggleWhitelist({ onSuccess: () => refetch() });

  const showOpwModal = (row: WhitelistIpInterface | null, type: 'save' | 'delete' | 'add' | 'toggle') => {
    setSelectedRow(row);
    setActionType(type);
    setOpwVisible(true);
  };

  // handlers
  const handleCancel = () => {
    setLoading(false);
    setSelectedRow(null);
    setOpwVisible(false);
  };
  const handleSave = (values: { code: string }) => {
    if (selectedRow) {
      setLoading(true);
      updateWhitelist({
        merchantNumber: from?.merchantId,
        ipAddress: selectedRow.ipAddress,
        newDescription: selectedRow.description,
        operationPassword: values.code,
      });
    }
    handleCancel();
  };
  const handleDelete = (values: { code: string }) => {
    if (selectedRow) {
      setLoading(true);
      deleteWhitelist({
        merchantNumber: from?.merchantId,
        ipAddress: selectedRow.ipAddress,
        operationPassword: values.code,
      });
    }
    handleCancel();
  };
  const handleToggleWhitelist = (values: { code: string }) => {
    setLoading(true);
    toggleWhitelist(
      {
        merchantId: Number(from?.merchantId),
        operationPassword: values.code,
      },
      {
        onSuccess: ({ isEnabled }) => {
          setWhitelistStatus(isEnabled);
        },
      },
    );
    handleCancel();
  };
  const handleAddIp = (values: { ipAddress: string; description: string; operationPassword: string }) => {
    setLoading(true);
    addWhitelist({
      merchantId: Number(from?.merchantId),
      ipAddress: values.ipAddress,
      description: values.description,
      operationPassword: values.operationPassword,
    });
    setAddModalVisible(false);
    setLoading(false);
  };

  const { columns } = useWhitelistColumns({
    onSave: (row) => showOpwModal(row, 'save'),
    onCancel: handleCancel,
    onDelete: (row) => showOpwModal(row, 'delete'),
  });

  return (
    <>
      <ModalAlpha
        open={!!from}
        onCancel={() => setFrom(undefined)}
        footer={null}
        width={1000}
      >
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Typography.Title
              level={5}
              style={{ margin: 0 }}
            >
              {t('title')}
            </Typography.Title>
            <Text type='secondary'>
              {from?.accountName} - {t('merchantId')}: {from?.merchantId}
            </Text>
            <Tag
              color={whitelistStatus ? 'green' : 'red'}
              style={{ marginLeft: 16 }}
            >
              {whitelistStatus ? t('enabled') : t('disabled')}
            </Tag>
          </div>
          <Space>
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => setAddModalVisible(true)}
            >
              {t('submit')}
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={() => showOpwModal(null, 'toggle')}
            >
              {t('toggleWhitelistButtonText')}
            </Button>
          </Space>
        </div>
        <TableAlpha
          columns={columns}
          dataSource={dataSource}
          rowKey='ipAddress'
          loading={isPending}
          {...{
            totalDataLength: data?.totalCount,
            pageSize,
            setPageSize,
            currentPage,
            setCurrentPage,
          }}
          size='small'
        />
      </ModalAlpha>
      <AddIpModal
        visible={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onSubmit={handleAddIp}
        loading={loading}
      />
      <ModalAlpha
        open={opwVisible}
        onCancel={handleCancel}
        footer={null}
        title={t('verifyOperationPasswordModalTitle')}
        width={400}
      >
        <OpwVfy
          ref={opwRef}
          loading={loading}
          onFinish={(values) => {
            switch (actionType) {
              case 'save':
                handleSave(values);
                break;
              case 'delete':
                handleDelete(values);
                break;
              case 'toggle':
                handleToggleWhitelist(values);
                break;
              default:
            }
          }}
        />
      </ModalAlpha>
    </>
  );
};

export default MerchantIpWhitelistModal;
export type { IMerchantIpWhitelistModalProps };
