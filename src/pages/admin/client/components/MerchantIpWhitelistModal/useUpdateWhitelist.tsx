// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type UpdateWhitelistRes = {};
type UpdateWhitelistProps = {
  merchantNumber?: number | null;
  ipAddress: string;
  newDescription: string;
  operationPassword: string;
};
type Other = {};

const useUpdateWhitelist = (useProps: UseTestMutationProps<UpdateWhitelistRes, UpdateWhitelistProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useUpdateWhitelist');

  const testMutation = useTestMutation<UpdateWhitelistRes, UpdateWhitelistProps>({
    ...config,
    mutationFn: ({ ipAddress, newDescription, operationPassword, merchantNumber }) => {
      const request = axiosRoot
        .put(`/V1.0/ip/whitelist/admin/merchants/${merchantNumber}`, {
          ipAddress,
          newDescription,
          operationPassword,
        })
        .then(({ data }) => data);
      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useUpdateWhitelist };
export type { UpdateWhitelistRes, UpdateWhitelistProps };
