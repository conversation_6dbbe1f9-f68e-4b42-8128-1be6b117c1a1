// libs
import React, { useRef, useState } from 'react';
import { Form, Input, Button } from 'antd';
import { useTranslation } from 'react-i18next';

// components
import ModalAlpha from '@/components/ModalAlpha';
import { OpwVfy, OpwVfyRef } from '@/components/ModalAlpha/OpwVfyModal';

interface IAddIpModalProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: { ipAddress: string; description: string; operationPassword: string }) => void;
  loading?: boolean;
}

const AddIpModal: React.FC<IAddIpModalProps> = ({ visible, onCancel, onSubmit, loading = false }) => {
  // refs
  const opwRef = useRef<OpwVfyRef>(null); // Ref for OpwVfy modal

  // states
  const [opwVisible, setOpwVisible] = useState<boolean>(false); // Opw modal visibility
  const [formValues, setFormValues] = useState<{ ipAddress: string; description: string }>({
    ipAddress: '',
    description: '',
  });

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('addIpModal');

  // Open Opw Modal after validating the form
  const handleOpenOpw = async () => {
    try {
      const values = await form.validateFields(); // Validate the form first
      setFormValues(values); // Save values for final submission
      setOpwVisible(true); // Open OpwVfy modal
    } catch {
      return undefined;
    }
    return undefined;
  };

  // Handle final submit with password
  const handlePasswordSubmit = (opwValues: { code: string }) => {
    onSubmit({ ...formValues, operationPassword: opwValues.code }); // Combine form data with password
    setOpwVisible(false); // Close Opw modal
  };

  return (
    <>
      {/* Add IP Modal */}
      <ModalAlpha
        open={visible}
        onCancel={onCancel}
        footer={null}
        title={t('title')}
        width={400}
      >
        <Form
          form={form}
          layout='vertical'
        >
          <Form.Item
            label={t('ipLabel')}
            name='ipAddress'
            rules={[
              { required: true, message: t('ipEmptyMessage') },
              { pattern: /^\d{1,3}(\.\d{1,3}){3}$/, message: t('ipErrorMessage') },
            ]}
          >
            <Input placeholder={t('ipPlaceholder')} />
          </Form.Item>
          <Form.Item
            label={t('descriptionLabel')}
            name='description'
            rules={[{ required: true, message: t('descriptionErrorMessage') }]}
          >
            <Input placeholder={t('descriptionPlaceholder')} />
          </Form.Item>
          <Button
            type='primary'
            onClick={handleOpenOpw}
            loading={loading}
            style={{ width: '100%' }}
          >
            {t('submit')}
          </Button>
        </Form>
      </ModalAlpha>

      {/* Operational Password Modal */}
      <ModalAlpha
        open={opwVisible}
        onCancel={() => setOpwVisible(false)}
        footer={null}
        title={t('verifyOperationPasswordModalTitle')}
        width={400}
      >
        <OpwVfy
          ref={opwRef}
          loading={loading}
          onFinish={handlePasswordSubmit}
        />
      </ModalAlpha>
    </>
  );
};

export default AddIpModal;
