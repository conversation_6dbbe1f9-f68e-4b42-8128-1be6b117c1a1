// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type AddWhitelistRes = {};
type AddWhitelistProps = {
  merchantId: number;
  ipAddress: string;
  description: string;
  operationPassword: string;
};
type Other = {};

const useAddWhitelist = (useProps: UseTestMutationProps<AddWhitelistRes, AddWhitelistProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useAddWhitelist');

  const testMutation = useTestMutation<AddWhitelistRes, AddWhitelistProps>({
    ...config,
    mutationFn: ({ merchantId, ipAddress, description, operationPassword }) => {
      const request = axiosRoot
        .post(`/V1.0/ip/whitelist/admin/merchants/${merchantId}`, {
          ipAddress,
          description,
          operationPassword,
        })
        .then(({ data }) => data);
      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useAddWhitelist };
export type { AddWhitelistRes, AddWhitelistProps };
