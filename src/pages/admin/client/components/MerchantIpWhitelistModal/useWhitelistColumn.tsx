// libs
import { useState } from 'react';
import { Input, Button, Space, Tooltip, Popconfirm } from 'antd';
import { useTranslation } from 'react-i18next';
import { EditOutlined, SaveOutlined, CloseOutlined, DeleteOutlined } from '@ant-design/icons';

// api
import { WhitelistIpInterface } from '@/api/whitelist/useWhiteList';

// components
import { Txt } from '@/components/TypographyMaster';

interface UseWhitelistColumnsProps {
  onSave: (record: WhitelistIpInterface) => void;
  onCancel: () => void;
  onDelete: (record: WhitelistIpInterface) => void;
}

const useWhitelistColumns = ({ onSave, onCancel, onDelete }: UseWhitelistColumnsProps) => {
  // states
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [editableRow, setEditableRow] = useState<Partial<WhitelistIpInterface>>({});

  // hooks
  const { t } = useTranslation('ipWhitelistModalColumns');

  // handlers
  const handleEdit = (record: WhitelistIpInterface) => {
    setEditingKey(record.ipAddress);
    setEditableRow(record);
  };
  const handleSave = () => {
    onSave(editableRow as WhitelistIpInterface);
    setEditingKey(null);
  };
  const handleCancel = () => {
    setEditingKey(null);
    setEditableRow({});
    onCancel();
  };
  const handleChange = (key: keyof WhitelistIpInterface, value: string) => {
    setEditableRow((prev) => ({ ...prev, [key]: value }));
  };

  const columns = [
    {
      title: <Txt>{t('ipAddressColumn')}</Txt>,
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      render: (_: string, record: WhitelistIpInterface) =>
        editingKey === record.ipAddress ? (
          <Input
            value={editableRow.ipAddress}
            onChange={(e) => handleChange('ipAddress', e.target.value)}
          />
        ) : (
          record.ipAddress
        ),
    },
    {
      title: <Txt>{t('descriptionColumn')}</Txt>,
      dataIndex: 'description',
      key: 'description',
      render: (_: string, record: WhitelistIpInterface) =>
        editingKey === record.ipAddress ? (
          <Input
            value={editableRow.description}
            onChange={(e) => handleChange('description', e.target.value)}
          />
        ) : (
          record.description
        ),
    },
    {
      title: <Txt>{t('actionsColumn')}</Txt>,
      key: 'actions',
      render: (record: WhitelistIpInterface) =>
        editingKey === record.ipAddress ? (
          <Space>
            <Tooltip title={t('saveTooltip')}>
              <Button
                type='link'
                icon={<SaveOutlined />}
                onClick={handleSave}
              />
            </Tooltip>
            <Tooltip title={t('cancelTooltip')}>
              <Button
                type='link'
                danger
                icon={<CloseOutlined />}
                onClick={handleCancel}
              />
            </Tooltip>
          </Space>
        ) : (
          <Space>
            <Tooltip title={t('editTooltip')}>
              <Button
                type='link'
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Popconfirm
              title={t('popConfirmTitle')}
              onConfirm={() => onDelete(record)}
            >
              <Button
                type='link'
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Space>
        ),
    },
  ];

  return { columns };
};

export default useWhitelistColumns;
