// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type DeleteWhitelistRes = {};
type DeleteWhitelistProps = {
  merchantNumber?: number | null;
  ipAddress: string;
  operationPassword: string;
};
type Other = {};

const useDeleteWhitelist = (useProps: UseTestMutationProps<DeleteWhitelistRes, DeleteWhitelistProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useDeleteWhitelist');

  const testMutation = useTestMutation<DeleteWhitelistRes, DeleteWhitelistProps>({
    ...config,
    mutationFn: ({ merchantNumber: merchantId, ipAddress, operationPassword }) => {
      const request = axiosRoot
        .delete(`/V1.0/ip/whitelist/admin/merchants/${merchantId}`, {
          data: { ipAddress, operationPassword },
        })
        .then(({ data }) => data);
      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useDeleteWhitelist };
export type { DeleteWhitelistRes, DeleteWhitelistProps };
