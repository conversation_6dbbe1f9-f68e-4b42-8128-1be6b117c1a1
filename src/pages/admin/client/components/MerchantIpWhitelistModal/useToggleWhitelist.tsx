// libs
// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import { useTranslation } from 'react-i18next';

type ToggleWhitelistRes = {
  isEnabled: boolean;
};
type ToggleWhitelistProps = {
  merchantId: number;
  operationPassword: string;
};
type Other = {};

const useToggleWhitelist = (useProps: UseTestMutationProps<ToggleWhitelistRes, ToggleWhitelistProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useToggleWhitelist');

  const testMutation = useTestMutation<ToggleWhitelistRes, ToggleWhitelistProps>({
    ...config,
    mutationFn: ({ merchantId, operationPassword }) => {
      const request = axiosRoot
        .post(`/V1.0/merchant/ip/toggle`, {
          merchantId,
          operationPassword,
        })
        .then(({ data }) => data);
      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useToggleWhitelist };
export type { ToggleWhitelistRes, ToggleWhitelistProps };
