// libs
import { useCallback } from 'react';
import { Modal, Form, InputNumber, Input, Button } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useSetFeeDetail } from '@/api/merchant';

// utils
import { nTot, tTon } from '@/utils';

// pages
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

type SetMerchantFeeValues = {
  trc20Fee: string;
  code: string;
  fixedFee: string;
  percentageFee: number;
  depositFee: number;
  withdrawalFee: number;
};

interface ISetMerchantFeeModalProps {
  from: CmxColumnsInfterface | undefined;
  setFrom: ReactSet<ISetMerchantFeeModalProps['from']>;
}

const SetMerchantFeeModal: React.FunctionComponent<ISetMerchantFeeModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('setMerchantFeeModal');

  // mutation
  const { mutate: setMerchantFee, isPending: inSetMerchantFee } = useSetFeeDetail({
    onSuccess: () => {
      setFrom(undefined);
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: SetMerchantFeeValues) => {
      if (!from || !from.merchantId) return;
      const { fixedFee, percentageFee, depositFee, withdrawalFee } = values;
      setMerchantFee({
        merchantId: from.merchantId,
        feeDetails: {
          fixedFee: tTon(fixedFee),
          percentageFee,
          depositFee,
          withdrawalFee,
        },
        operationPassword: values.code,
      });
    },
    [from, setMerchantFee],
  );

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={!!from}
      onCancel={() => setFrom(undefined)}
    >
      <Form
        {...{ form }}
        variant='filled'
        autoComplete='off'
        onFinish={handleSubmit}
        layout='vertical'
      >
        {/* 會員入金手續費 */}
        <Form.Item
          label={t('trc20FeeLabel')}
          name='percentageFee'
          rules={[{ required: true, message: t('trc20FeeErrorMessage') }]}
        >
          <InputNumber
            min={0}
            step='0.001'
            placeholder={t('trc20FeePlaceholder')}
            className='w-full'
          />
        </Form.Item>

        {/* 會員出金手續費 */}
        <Form.Item
          label={t('fixedFeeLabel')}
          name='fixedFee'
          rules={[{ required: true, message: t('fixedFeeErrorMessage') }]}
          normalize={(value) => nTot({ value })}
        >
          <Input
            placeholder={t('fixedFeePlaceholder')}
            className='w-full'
            autoComplete='off'
            inputMode='decimal'
            suffix='USDT'
          />
        </Form.Item>

        {/* 商戶轉入手續費 */}
        <Form.Item
          label={t('depositFeeLabel')}
          name='depositFee'
          rules={[{ required: true, message: t('depositFeeErrorMessage') }]}
        >
          <InputNumber
            min={0}
            step='0.001'
            placeholder={t('depositFeePlaceholder')}
            className='w-full'
          />
        </Form.Item>

        {/* 商戶轉出手續費 */}
        <Form.Item
          label={t('withdrawFeeLabel')}
          name='withdrawalFee'
          rules={[{ required: true, message: t('withdrawFeeErrorMessage') }]}
        >
          <InputNumber
            placeholder={t('withdrawFeePlaceholder')}
            suffix='USDT'
            className='w-full'
          />
        </Form.Item>

        <Form.Item
          name='code'
          label={t('operationPasswordLabel')}
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password placeholder={t('operationPasswordPlaceholder')} />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            loading={inSetMerchantFee}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SetMerchantFeeModal;
export type { ISetMerchantFeeModalProps };
