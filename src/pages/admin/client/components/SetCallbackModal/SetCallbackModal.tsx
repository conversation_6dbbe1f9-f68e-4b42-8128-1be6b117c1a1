// libs
import { useCallback } from 'react';
import { Modal, Form, Input, Button } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useMerchantList } from '@/api/merchant';
import { useSetMerchantCallback } from '@/api/client';

// hooks
import { useFormInit } from '@/hooks';

// pages
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

type SetCallbackValues = {
  callbackUrl: string;
  code: string;
};

interface ISetCallbackModalProps {
  from: CmxColumnsInfterface | undefined;
  setFrom: ReactSet<ISetCallbackModalProps['from']>;
}

const SetCallbackModal: React.FunctionComponent<ISetCallbackModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};

  // hooks
  const [form] = Form.useForm();
  const initialValues = useFormInit({ type: 'setCallback', isTest: true });
  const { t } = useTranslation('setCallbackModal');

  // mutation
  const { data } = useMerchantList({ enabled: !!from, params: { CustomerName: from?.customerName } });
  const { mutate: setCallback, isPending: inSetCallback } = useSetMerchantCallback({
    onSuccess: () => {
      setFrom(undefined);
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: SetCallbackValues) => {
      if (!data) return;
      const { callbackUrl, code } = values;
      data.items.forEach((eachM) => {
        setCallback({
          merchantId: eachM.id,
          callbackUrl,
          operationPassword: code,
        });
      });
    },
    [data, setCallback],
  );

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={!!from}
      onCancel={() => setFrom(undefined)}
    >
      <Form
        {...{ form, initialValues }}
        variant='filled'
        autoComplete='off'
        onFinish={handleSubmit}
        layout='vertical'
      >
        {/*  */}
        <Form.Item
          label={t('callbackUrlLabel')}
          name='callbackUrl'
          rules={[{ required: true, message: t('callbackUrlErrorMessage') }]}
        >
          <Input
            placeholder={t('callbackUrlPlaceholder')}
            className='min-w-[120px]'
            autoComplete='off'
            inputMode='url'
          />
        </Form.Item>

        <Form.Item
          name='code'
          label={t('operationPasswordLabel')}
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password placeholder={t('operationPasswordPlaceholder')} />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            loading={inSetCallback}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SetCallbackModal;
export type { ISetCallbackModalProps };
