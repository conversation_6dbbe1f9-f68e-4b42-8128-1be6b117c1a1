// libs
import { useCallback, useMemo, useRef, useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Card, Divider, Form, FormInstance, Input, Radio, Skeleton, Space } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useCreateClient } from '@/api/client';
import { useCreateMerchant, useSetFeeDetail } from '@/api/merchant';

// components
import OpwVfyModal from '@/components/ModalAlpha/OpwVfyModal';
import { AmountRangeInterface } from '@/components/FormItem';

// hooks
import { useFormInit } from '@/hooks';

// utils
import { ClientEnvironmentEnum, LimitTimeInterval, clientEnvOptions } from '@/utils';

// pages
import BasicFields from './BasicFields';
import { DailyLimitInterface } from './DailyLimit';
import { LimitTimesInterface } from './LimitTimes';

type CreateClientMerchantsValues = {
  clientName: string;
  items: Array<
    {
      merchantName: string;
      merchantEnvType: ClientEnvironmentEnum;
    } & {
      [key in 'deposit' | 'withdraw' | 'transfer']: {
        amountRange: AmountRangeInterface;
        trc20Fee: number;
        erc20Fee: number;
        vfyAutoMode: boolean;
        autoOffset: number;
        payTimeLimit?: LimitTimeInterval;
        limitTimes?: LimitTimesInterface;
        dailyLimit?: DailyLimitInterface;
        white?: Array<{
          address: string;
        }>;
      };
    }
  >;
};

interface ICreateClientMerchantsFormProps {
  loading?: boolean;
  form?: FormInstance;
}

const CreateClientMerchantsForm: React.FunctionComponent<ICreateClientMerchantsFormProps> = (props) => {
  // props
  const { form, loading } = props || {};

  // states
  const [formValues, setFormValues] = useState<CreateClientMerchantsValues>();
  const [merchantCount, setMerchantCount] = useState(0);

  // refs
  const btnAddRef = useRef<HTMLButtonElement>(null);

  // hooks
  const { init, getItem } = useFormInit({ type: 'createClient', isTest: true });
  const { t } = useTranslation('createClientMerchantsForm');
  const { t: optionsT } = useTranslation('options');

  // mutates
  const { mutate: setFee, isPending: inSetFee } = useSetFeeDetail({
    onSuccess: () => {
      setFormValues(undefined);
      form?.resetFields();
    },
  });
  const { mutate: creatMerchant, isPending: inCreateMerchant } = useCreateMerchant({
    onSuccess: (res, params) => {
      if (!params || !formValues) return;
      formValues.items.forEach((eachI) => {
        setFee({
          merchantId: res.merchantId,
          feeDetails: {
            fixedFee: eachI.transfer.trc20Fee,
            percentageFee: eachI.transfer.trc20Fee,
            depositFee: eachI.deposit.trc20Fee,
            withdrawalFee: eachI.withdraw.trc20Fee,
          },
          operationPassword: params.operationPassword,
        });
      });
    },
  });
  const { mutate: createClient, isPending: inCreateClient } = useCreateClient({
    onSuccess: (res, params) => {
      if (!params || !formValues) return;
      formValues.items.forEach((eachI) => {
        creatMerchant({
          customerId: res.customerId,
          merchantName: eachI.merchantName,
          operationPassword: params.operationPassword,
        });
      });
    },
  });

  // handlers
  const handleSubmitOpw = useCallback(
    (values: { code: string }) => {
      if (!formValues) return;
      createClient({
        name: formValues.clientName,
        operationPassword: values.code,
      });
    },
    [createClient, formValues],
  );

  // compute
  const formLoading = useMemo(
    () => inSetFee || inCreateMerchant || inCreateClient,
    [inCreateClient, inCreateMerchant, inSetFee],
  );
  const translateClientEnvOptions = useMemo(
    () => clientEnvOptions.map((option) => ({ ...option, label: optionsT(option.label) })),
    [optionsT],
  );

  return (
    <>
      <Form
        form={form}
        variant='filled'
        autoComplete='off'
        onFinish={(values) => {
          setFormValues(values);
        }}
        initialValues={{ ...init }}
      >
        {!loading ? (
          <Form.Item
            name='clientName'
            label={t('clientNameLabel')}
            rules={[{ required: true, message: t('clientNameErrorMessage') }]}
          >
            <Input placeholder={t('clientNamePlaceholder')} />
          </Form.Item>
        ) : (
          <Space>
            <Skeleton.Button
              size='small'
              active
            />
            <Skeleton.Input
              size='small'
              active
            />
          </Space>
        )}

        <Divider
          orientationMargin={10}
          orientation='left'
          plain
        >
          <div className='text-[#389e0d]'>{t('merchant')}</div>
        </Divider>

        {/* List */}
        {!loading ? (
          <Form.List name='items'>
            {(fields, { add, remove }) => (
              <div style={{ display: 'flex', rowGap: 16, flexDirection: 'column' }}>
                {fields.map((field) => (
                  <Card
                    size='small'
                    title={`${t('merchant')} ${field.name + 1}`}
                    key={field.key}
                    extra={
                      <CloseOutlined
                        onClick={() => {
                          remove(field.name);
                        }}
                      />
                    }
                  >
                    {/* Merchant Info */}
                    <Form.Item
                      label={t('merchantNameLabel')}
                      name={[field.name, 'merchantName']}
                      rules={[{ required: true, message: t('merchantNameErrorMessage') }]}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item
                      label={t('merchantTypeLabel')}
                      name={[field.name, 'merchantEnvType']}
                      rules={[{ required: true, message: t('merchantTypeErrorMessage') }]}
                    >
                      <Radio.Group options={translateClientEnvOptions} />
                    </Form.Item>

                    {/* 入金、出金、轉帳 */}
                    {['deposit', 'withdraw', 'transfer'].map((mapName) => {
                      return (
                        <BasicFields
                          {...{ field, form }}
                          key={mapName}
                          itemName={mapName}
                        />
                      );
                    })}
                  </Card>
                ))}

                <Button
                  type='dashed'
                  onClick={() => {
                    add();
                    const items: CreateClientMerchantsValues['items'] | undefined = form?.getFieldValue('items');
                    if (!items) return;
                    const newItems = getItem ? items.map((_, index) => getItem(index)) : items;
                    form?.setFieldValue('items', newItems);
                    setMerchantCount(newItems.length);
                  }}
                  block
                  ref={btnAddRef}
                >
                  + {t('addNewMerchantButtonText')}
                </Button>
              </div>
            )}
          </Form.List>
        ) : (
          <Space
            direction='vertical'
            className='w-full'
          >
            <Skeleton active />
            <Skeleton active />
            <Skeleton active />
          </Space>
        )}

        <Form.Item className='mb-0 mt-6'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            disabled={!merchantCount}
            loading={loading}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>

      <OpwVfyModal
        vfyProps={{ loading: formLoading, onFinish: handleSubmitOpw }}
        open={!!formValues}
        setOpen={(newOpenSet) => {
          const newOpen = (() => {
            if (newOpenSet instanceof Function) return newOpenSet(!!formValues);
            return newOpenSet;
          })();
          if (!newOpen) setFormValues(undefined);
        }}
      />
    </>
  );
};

export default CreateClientMerchantsForm;
export type { ICreateClientMerchantsFormProps, CreateClientMerchantsValues };
