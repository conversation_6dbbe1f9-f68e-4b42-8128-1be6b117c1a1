// libs
import { Space, InputNumber, DatePicker } from 'antd';
import { Dayjs } from 'dayjs';
import { useTranslation } from 'react-i18next';

interface DailyLimitInterface {
  limitTimes: number | null;
  resetAt: Dayjs | null;
}

interface IDailyLimitProps {
  value?: DailyLimitInterface;
  onChange?: (newValue: DailyLimitInterface) => void;
  itemName: string;
}

const DailyLimit: React.FunctionComponent<IDailyLimitProps> = (props) => {
  // props
  const { value, onChange, itemName } = props || {};

  // hooks
  const { t } = useTranslation('dailyLimit');

  return (
    <Space>
      {t('daily')}
      {t(itemName)}
      {t('toSameAddress')}
      <InputNumber
        value={value?.limitTimes}
        onChange={(newTimes) => {
          if (onChange) onChange(value ? { ...value, limitTimes: newTimes } : { limitTimes: newTimes, resetAt: null });
        }}
        min={1}
        max={10}
        placeholder={t('inputPlaceholder')}
        suffix={t('suffix')}
      />
      {t('dailyResetTime')}
      <DatePicker
        value={value?.resetAt}
        onChange={(newDate) => {
          if (onChange) onChange(value ? { ...value, resetAt: newDate } : { resetAt: newDate, limitTimes: null });
        }}
        placeholder={t('datePlaceholder')}
        picker='time'
      />
    </Space>
  );
};

export default DailyLimit;
export type { DailyLimitInterface };
