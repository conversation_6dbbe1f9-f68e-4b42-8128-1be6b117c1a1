// libs
import { useMemo } from 'react';
import { Space, Select, InputNumber } from 'antd';
import { useTranslation } from 'react-i18next';

// utils
import { LimitTimeInterval, limittiOptions } from '@/utils';

interface LimitTimesInterface {
  limitTime: LimitTimeInterval | null;
  limitTimes: number | null;
}

interface ILimitTimesProps {
  value?: LimitTimesInterface;
  onChange?: (newValue: LimitTimesInterface) => void;
}

const LimitTimes: React.FunctionComponent<ILimitTimesProps> = (props) => {
  // props
  const { value, onChange } = props || {};

  // hooks
  const { t } = useTranslation('limitTimes');
  const { t: optionsT } = useTranslation('options');

  // compute
  const translateLimitTimeOptions = useMemo(
    () => limittiOptions.map((option) => ({ ...option, label: optionsT(option.label) })),
    [optionsT],
  );

  return (
    <Space>
      <Select
        value={value?.limitTime}
        onChange={(newInterval) => {
          if (onChange)
            onChange(value ? { ...value, limitTime: newInterval } : { limitTime: newInterval, limitTimes: null });
        }}
        placeholder={t('selectPlaceholder')}
        className='min-w-[120px]'
        options={translateLimitTimeOptions}
      />
      {t('restrictSubmissions')}
      <InputNumber
        min={1}
        max={10}
        placeholder={t('inputPlaceholder')}
        suffix={t('suffix')}
        value={value?.limitTimes}
        onChange={(newTimes) => {
          if (onChange)
            onChange(value ? { ...value, limitTimes: newTimes } : { limitTimes: newTimes, limitTime: null });
        }}
      />
      {t('order')}
    </Space>
  );
};

export default LimitTimes;
export type { LimitTimesInterface };
