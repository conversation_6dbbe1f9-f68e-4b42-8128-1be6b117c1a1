// libs
import { Space, InputNumber } from 'antd';
import { useTranslation } from 'react-i18next';

interface IAutoOffsetProps {
  value?: number;
  onChange?: (newValue: number | null) => void;
}

const AutoOffset: React.FunctionComponent<IAutoOffsetProps> = (props) => {
  // props
  const { value, onChange } = props || {};

  // hooks
  const { t } = useTranslation('autoOffset');

  return (
    <Space>
      {t('firstNote')}
      <InputNumber
        value={value}
        onChange={(newValue) => {
          if (onChange) onChange(newValue);
        }}
        min={0.01}
        placeholder={t('placeholder')}
      />
      {t('secondNote')}
    </Space>
  );
};

export default AutoOffset;
