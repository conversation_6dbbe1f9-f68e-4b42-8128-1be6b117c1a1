// libs
import { useCallback } from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useCreateClient } from '@/api/client';

// hooks
import { useFormInit } from '@/hooks';

type ClientCreateValues = {
  clientName: string;
  code: string;
};

interface IClientCreateModalProps {
  open: boolean;
  onCancel: () => void;
}

const ClientCreateModal: React.FunctionComponent<IClientCreateModalProps> = (props) => {
  // props
  const { open, onCancel } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { init } = useFormInit({ type: 'createClient', isTest: true });
  const { t } = useTranslation('clientCreateModal');

  // mutates
  const { mutate: createClient, isPending: inCreateClient } = useCreateClient({
    onSuccess: () => {
      form.resetFields();
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: ClientCreateValues) => {
      createClient({
        name: values.clientName,
        operationPassword: values.code,
      });
    },
    [createClient],
  );

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={open}
      onCancel={onCancel}
    >
      <Form
        {...{ form }}
        variant='filled'
        autoComplete='off'
        onFinish={handleSubmit}
        initialValues={{ ...init }}
        layout='vertical'
      >
        <Form.Item
          name='clientName'
          label={t('clientNameLabel')}
          rules={[{ required: true, message: t('clientNameErrorMessage') }]}
        >
          <Input
            placeholder={t('clientNamePlaceholder')}
            inputMode='text'
            autoComplete='clientName'
          />
        </Form.Item>

        <Form.Item
          name='code'
          label={t('operationPasswordLabel')}
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password placeholder={t('operationPasswordPlaceholder')} />
        </Form.Item>

        <Form.Item className='mb-0 mt-6'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            loading={inCreateClient}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ClientCreateModal;
export type { IClientCreateModalProps };
