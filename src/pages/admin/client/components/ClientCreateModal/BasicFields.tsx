// libs
import { HtmlHTMLAttributes, useCallback, useMemo, useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { Divider, Form, InputNumber, Switch, Select, FormListFieldData, Button, Flex, Input } from 'antd';
import { FormInstance } from 'antd/lib';
import { useTranslation } from 'react-i18next';

// components
import BtnFuncs from '@/components/BtnFuncs';
import { FormAmountRange } from '@/components/FormItem';
import { Title } from '@/components/TypographyMaster';

// utils
import { limittiOptions } from '@/utils';

// pages
import AutoOffset from './AutoOffset';
import DailyLimit from './DailyLimit';
import LimitTimes from './LimitTimes';

interface IBasicFieldsProps extends HtmlHTMLAttributes<HTMLDivElement> {
  itemName: string;
  field: FormListFieldData;
  form?: FormInstance;
}

const BasicFields: React.FunctionComponent<IBasicFieldsProps> = (props) => {
  // props
  const { itemName, field, form } = props || {};

  // states
  const [autoVfyMode, setAutoVfyMode] = useState(true);

  // hooks
  const { t } = useTranslation('basicFields');
  const { t: optionsT } = useTranslation('options');

  // compute
  const translateLimitTimeOptions = useMemo(
    () => limittiOptions.map((option) => ({ ...option, label: optionsT(option.label) })),
    [optionsT],
  );

  // handlers
  const handleClickSome = useCallback(() => {
    const values = form?.getFieldsValue();
    form?.setFieldsValue({
      ...values,
      items: values.items.map((mapI: { deposit: object; withdraw: object; transfer: Object }) => {
        return {
          ...mapI,
          withdraw: itemName === 'withdraw' && mapI ? { ...mapI.withdraw, ...mapI.deposit } : mapI.withdraw,
          transfer: itemName === 'transfer' && mapI ? { ...mapI.transfer, ...mapI.withdraw } : mapI.transfer,
        };
      }),
    });
  }, [form, itemName]);

  return (
    <div>
      <Divider
        orientationMargin={10}
        orientation='left'
        plain
      >
        <div className='text-[#389e0d]'>
          {t(itemName)} {t('setting')}
        </div>
      </Divider>

      {/*  */}
      <section className='flex w-full justify-between pr-4'>
        {/* left */}
        <FormAmountRange
          label={t('amountLabel')}
          name={[field.name, itemName, 'amountRange']}
          rules={[{ required: true, message: t('amountErrorMessage') }]}
        />
        {/* right */}
        {['withdraw', 'transfer'].includes(itemName) && (
          <BtnFuncs
            onClick={handleClickSome}
            type='dashed'
          >
            {t('submit')}
          </BtnFuncs>
        )}
      </section>

      {/*  */}
      <Form.Item
        label={t('trc20FeeLabel')}
        name={[field.name, itemName, 'trc20Fee']}
        rules={[{ required: true, message: t('trc20FeeErrorMessage') }]}
      >
        <InputNumber
          min={0.01}
          step='0.01'
          placeholder={t('trc20FeePlaceholder')}
          suffix='%'
          className='min-w-[120px]'
        />
      </Form.Item>

      {/*  */}
      <Form.Item
        label={t('erc20FeeLabel')}
        name={[field.name, itemName, 'erc20Fee']}
        rules={[{ required: true, message: t('erc20FeeErrorMessage') }]}
      >
        <InputNumber
          min={0.01}
          step='0.01'
          placeholder={t('erc20FeePlaceholder')}
          suffix='%'
          className='min-w-[120px]'
        />
      </Form.Item>

      {/*  */}
      <Form.Item
        label={t('verificationModeLabel')}
        name={[field.name, itemName, 'vfyAutoMode']}
      >
        <Switch
          onChange={setAutoVfyMode}
          checkedChildren={t('checkedChildren')}
          unCheckedChildren={t('unCheckedChildren')}
          defaultChecked
        />
      </Form.Item>

      {/*  */}
      <Form.Item
        label={t('autoOffsetLabel')}
        name={[field.name, itemName, 'autoOffset']}
        rules={[{ required: true, message: t('autoOffsetErrorMessage') }]}
        className={`${autoVfyMode ? 'visible' : 'hidden'}`}
      >
        <AutoOffset />
      </Form.Item>

      {['deposit'].includes(itemName) && (
        <>
          {/*  */}
          <Form.Item
            label={t('paymentTimeLabel')}
            name={[field.name, itemName, 'payTimeLimit']}
            rules={[{ required: true, message: t('paymentTimeErrorMessage') }]}
          >
            <Select
              placeholder={t('paymentTimePlaceHolder')}
              className='min-w-[120px]'
              options={translateLimitTimeOptions}
            />
          </Form.Item>

          {/*  */}
          <Form.Item
            label={t('limitTimeLabel')}
            name={[field.name, itemName, 'limitTimes']}
            rules={[{ required: true, message: t('limitTimeErrorMessage') }]}
          >
            <LimitTimes />
          </Form.Item>
        </>
      )}

      {['withdraw', 'transfer'].includes(itemName) && (
        <Form.Item
          label={t('dailyLimitLabel')}
          name={[field.name, itemName, 'dailyLimit']}
          rules={[{ required: true, message: t('dailyLimitErrorMessage') }]}
        >
          <DailyLimit {...{ itemName }} />
        </Form.Item>
      )}

      {['transfer'].includes(itemName) && (
        <>
          <Title level={5}>{t('whitelist')}</Title>
          <Form.List name={[field.name, itemName, 'white']}>
            {(subFields, subOpt) => (
              <div style={{ display: 'flex', flexDirection: 'column', rowGap: 16 }}>
                {subFields.map((subField) => (
                  <Flex
                    key={subField.key}
                    gap={10}
                    className='items-center'
                  >
                    <div>{subField.name + 1}</div>
                    <Form.Item
                      noStyle
                      name={[subField.name, 'address']}
                    >
                      <Input placeholder={t('walletAddressPlaceholder')} />
                    </Form.Item>
                    <CloseOutlined
                      onClick={() => {
                        subOpt.remove(subField.name);
                      }}
                    />
                  </Flex>
                ))}

                <Button
                  type='dashed'
                  onClick={() => subOpt.add()}
                  block
                >
                  + {t('addButtonText')}
                </Button>
              </div>
            )}
          </Form.List>
        </>
      )}
    </div>
  );
};

export default BasicFields;
export type { IBasicFieldsProps };
