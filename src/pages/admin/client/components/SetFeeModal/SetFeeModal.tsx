// libs
import { useCallback } from 'react';
import { Modal, Form, InputNumber, Input, Button, Space } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useMerchantList, useSetFeeDetail } from '@/api/merchant';

// utils
import { nTot, tTon } from '@/utils';

// pages
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

type SetFeeValues = {
  trc20Fee: string;
  code: string;
  fixedFee: string;
  percentageFee: number;
  depositFee: number;
  withdrawalFee: number;
};

interface ISetFeeModalProps {
  from: CmxColumnsInfterface | undefined;
  setFrom: ReactSet<ISetFeeModalProps['from']>;
}

const SetFeeModal: React.FunctionComponent<ISetFeeModalProps> = (props) => {
  // props
  const { from, setFrom } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('setFeeModal');

  // mutation
  const { data } = useMerchantList({ enabled: !!from, params: { CustomerName: from?.customerName } });
  const { mutate: setFee, isPending: inSetFee } = useSetFeeDetail({
    onSuccess: () => {
      setFrom(undefined);
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: SetFeeValues) => {
      if (!data) return;
      const { fixedFee, percentageFee, depositFee, withdrawalFee } = values;
      data.items.forEach((eachM) => {
        setFee({
          merchantId: eachM.id,
          feeDetails: {
            fixedFee: tTon(fixedFee),
            percentageFee,
            depositFee,
            withdrawalFee,
          },
          operationPassword: values.code,
        });
      });
    },
    [data, setFee],
  );

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={!!from}
      onCancel={() => setFrom(undefined)}
    >
      <Form
        {...{ form }}
        variant='filled'
        autoComplete='off'
        onFinish={handleSubmit}
        layout='vertical'
      >
        <Space>
          {/*  */}
          <Form.Item
            label={t('fixedFeeLabel')}
            name='fixedFee'
            rules={[{ required: true, message: t('fixedFeeErrorMessage') }]}
            normalize={(value) => nTot({ value })}
          >
            <Input
              placeholder={t('fixedFeePlaceholder')}
              className='min-w-[120px]'
              autoComplete='off'
              inputMode='decimal'
            />
          </Form.Item>

          {/*  */}
          <Form.Item
            label={t('trc20FeeLabel')}
            name='trc20Fee'
            rules={[{ required: true, message: t('trc20FeeErrorMessage') }]}
          >
            <InputNumber
              min={0.01}
              step='0.01'
              placeholder={t('trc20FeePlaceholder')}
              className='min-w-[120px]'
            />
          </Form.Item>
        </Space>

        <Space>
          {/*  */}
          <Form.Item
            label={t('depositFeeLabel')}
            name='depositFee'
            rules={[{ required: true, message: t('depositFeeErrorMessage') }]}
          >
            <InputNumber
              min={0.01}
              step='0.01'
              placeholder={t('depositFeePlaceholder')}
              className='min-w-[120px]'
            />
          </Form.Item>

          {/*  */}
          <Form.Item
            label={t('withdrawFeeLabel')}
            name='withdrawalFee'
            rules={[{ required: true, message: t('withdrawFeeErrorMessage') }]}
          >
            <InputNumber
              min={0.01}
              step='0.01'
              placeholder={t('withdrawFeePlaceholder')}
              className='min-w-[120px]'
            />
          </Form.Item>

          <Form.Item
            name='code'
            label={t('operationPasswordLabel')}
            rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
          >
            <Input.Password placeholder={t('operationPasswordPlaceholder')} />
          </Form.Item>
        </Space>

        <Form.Item className='mb-0'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            loading={inSetFee}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SetFeeModal;
export type { ISetFeeModalProps };
