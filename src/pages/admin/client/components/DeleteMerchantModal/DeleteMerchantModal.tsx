// libs
import { useCallback } from 'react';
import { Modal, Input, Form, Button } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useDeleteMerchant } from '@/api/merchant/useDeleteMerchant';

// pages
import { CmxColumnsInfterface } from '../../ClientMerchants/useCmxColumns';

type DeleteMerchantValues = {
  operationPassword: string;
};

interface IDeleteMerchantModalProps {
  from: CmxColumnsInfterface | undefined; // Merchant details
  setFrom: ReactSet<IDeleteMerchantModalProps['from']>;
  onFinish: () => void;
}

const DeleteMerchantModal: React.FunctionComponent<IDeleteMerchantModalProps> = (props) => {
  // props
  const { from, setFrom, onFinish } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('deleteMerchantModal');

  const { mutate, isPending: isDeleting } = useDeleteMerchant({
    onSuccess: () => {
      setFrom(undefined);
      form.resetFields();
      onFinish();
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: DeleteMerchantValues) => {
      if (!from || !from.merchantId) return;
      mutate({
        merchantId: from.merchantId,
        operationPassword: values.operationPassword,
      });
    },
    [from, mutate],
  );

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={!!from}
      onCancel={() => setFrom(undefined)}
    >
      <Form
        {...{ form }}
        variant='filled'
        autoComplete='off'
        onFinish={handleSubmit}
        layout='vertical'
      >
        <Form.Item
          label={t('operationPasswordLabel')}
          name='operationPassword'
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password
            placeholder={t('operationPasswordPlaceholder')}
            className='min-w-[120px]'
            autoComplete='off'
          />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            type='primary'
            htmlType='submit'
            className='w-full'
            loading={isDeleting}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DeleteMerchantModal;
export type { IDeleteMerchantModalProps };
