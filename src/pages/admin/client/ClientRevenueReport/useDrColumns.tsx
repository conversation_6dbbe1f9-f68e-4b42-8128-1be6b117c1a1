// libs
import { useMemo } from 'react';
import { Space, TableColumnsType, Tag, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { MerchantInfo } from '@/api/merchant';
import { OrderItemInterface } from '@/api/order';

// components
import { Txt } from '@/components/TypographyMaster';

interface IDrRow {
  key: React.Key;
  merchant?: MerchantInfo;
  orderInfo?: OrderItemInterface;
  fee: number;
  children?: Array<IDrRow>;
}

type DrColumnsProps = {};

const useDrColumns = () => {
  // hooks
  const { t } = useTranslation('depositRevenueColumns');

  // compute
  const columns: TableColumnsType<IDrRow> = useMemo(() => {
    return [
      {
        title: <Txt>{t('clientColumn')}</Txt>,
        key: 'client',
        render: (_, { merchant }) => <Txt strong>{merchant?.customerName || t('unknown')}</Txt>,
      },
      {
        title: <Txt>{t('merchantColumn')}</Txt>,
        key: 'merchant',
        render: (_, { merchant }) => <Txt strong>{merchant?.merchantName || 'N/A'}</Txt>,
      },
      {
        title: <Txt>{t('orderInfoColumn')}</Txt>,
        key: 'orderInfo',
        render: (_, { orderInfo }) => {
          if (!orderInfo) return <Txt type='secondary'>{t('emptyOrderMessage')}</Txt>;

          return (
            <Space
              direction='vertical'
              size='small'
            >
              <div>
                <Tooltip title={t('hashTooltip')}>
                  <Tag color='blue'>{t('hash')}</Tag>: {orderInfo.order.hash || 'N/A'}
                </Tooltip>
              </div>
              <div>
                <Tooltip title={t('amountTooltip')}>
                  <Tag color='green'>{t('amount')}</Tag>: ${orderInfo.order.actualAmount.toFixed(2)}
                </Tooltip>
              </div>
              <div>
                <Tooltip title={t('fromTooltip')}>
                  <Tag color='magenta'>{t('from')}</Tag>: {orderInfo.order.from || 'N/A'}
                </Tooltip>
              </div>
              <div>
                <Tooltip title={t('toTooltip')}>
                  <Tag color='purple'>{t('to')}</Tag>: {orderInfo.order.to || 'N/A'}
                </Tooltip>
              </div>
              <div>
                <Tooltip title={t('timeTooltip')}>
                  <Tag color='gold'>{t('created')}</Tag>: {new Date(orderInfo.createdAt).toLocaleString()}
                </Tooltip>
              </div>
              <div>
                <Tooltip title={t('remarkTooltip')}>
                  <Tag color='cyan'>{t('remark')}</Tag>: {orderInfo.order.remark || 'N/A'}
                </Tooltip>
              </div>
            </Space>
          );
        },
      },
      {
        title: <Txt>{t('feeColumn')}</Txt>,
        key: 'fee',
        render: (_, { orderInfo }) => (
          <Txt style={{ color: '#fa541c', fontWeight: 'bold' }}>${orderInfo?.order.fee.toFixed(2)}</Txt>
        ),
      },
    ];
  }, [t]);

  return { columns };
};
export default useDrColumns;
export type { DrColumnsProps, IDrRow };
