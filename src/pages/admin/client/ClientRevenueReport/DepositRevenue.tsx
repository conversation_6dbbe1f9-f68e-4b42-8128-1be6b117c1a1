// libs
import { useCallback, useMemo } from 'react';
import { Table } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { OrderItemInterface } from '@/api/order';

// components
import TableAlpha from '@/components/TableAlpha';

// utils
import { nTot } from '@/utils';

// pages
import useDrColumns, { IDrRow } from './useDrColumns';
import { CallReportSubmitValues } from './CalReportModal';

interface IDepositRevenueProps {
  orders: Array<OrderItemInterface> | undefined;
  calReportValues: CallReportSubmitValues | undefined;
  loading: boolean;
}

const DepositRevenue: React.FunctionComponent<IDepositRevenueProps> = (props) => {
  // props
  const { orders, calReportValues } = props || {};

  // hooks
  const { t } = useTranslation('depositRevenue');

  // compute
  const dataSource: Array<IDrRow> = useMemo(() => {
    if (!orders || !calReportValues) return [];
    return calReportValues.selectedMerchants.map((mapM) => {
      const mOrders = orders.filter((filterO) => filterO.merchantId === mapM.id);
      const children = (() => {
        if (!mOrders.length) return undefined;
        return mOrders.map((mapO) => {
          const key = `${mapM.id}-${mapO.order.hash}`;
          return { orderInfo: mapO, fee: mapO.order.fee, key };
        });
      })();
      const key = `${mapM.id}`;
      const fee = mOrders.reduce((preValue, current) => preValue + current.order.fee, 0);
      return { key, children, merchant: mapM, fee };
    });
  }, [calReportValues, orders]);
  const { columns } = useDrColumns();

  const summary = useCallback(
    (datas: readonly any[]) => {
      const rowDatas = datas as Array<IDrRow>;
      const feeTotal = rowDatas.reduce((preValue, current) => preValue + current.fee, 0);
      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            {columns.map((mapC, index) => {
              const key = `${mapC.key}-${index}`;

              if (mapC.key === 'fee')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                  >
                    {nTot({ value: feeTotal })}
                  </Table.Summary.Cell>
                );
              return (
                <Table.Summary.Cell
                  key={key}
                  index={index}
                />
              );
            })}
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
    [columns],
  );

  return (
    <TableAlpha
      {...{ dataSource, columns, summary }}
      titleRender={t('title')}
      size='small'
    />
  );
};

export default DepositRevenue;
