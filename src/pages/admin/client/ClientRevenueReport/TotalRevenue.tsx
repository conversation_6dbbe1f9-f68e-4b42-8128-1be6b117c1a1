// libs
import { useMemo } from 'react';
import { Table } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { OrderItemInterface } from '@/api/order';

// utils
import { nTot, TxCategoryNum } from '@/utils';

interface ITotalRevenueProps {
  orders: Array<OrderItemInterface>;
}

const TotalRevenue: React.FC<ITotalRevenueProps> = ({ orders }: ITotalRevenueProps) => {
  // hooks
  const { t } = useTranslation('totalRevenue');

  // Summarize data by transaction type
  const summaryData = useMemo(() => {
    const calculateTotals = (transactions: Array<OrderItemInterface>) => {
      const totalAmount = transactions.reduce((sum, o) => sum + o.order.actualAmount, 0);
      const totalFees = transactions.reduce((sum, o) => sum + o.order.fee, 0);
      const netAmount = totalAmount - totalFees;
      return { totalAmount, totalFees, netAmount };
    };
    const depositOrders = orders?.filter((o) => o.order.transactionType === TxCategoryNum.Deposit);
    const withdrawOrders = orders?.filter((o) => o.order.transactionType === TxCategoryNum.Withdraw);

    return {
      deposits: calculateTotals(depositOrders),
      withdrawals: calculateTotals(withdrawOrders),
    };
  }, [orders]);

  // Table columns
  const columns = [
    {
      title: t('typeColumn'),
      dataIndex: 'transactionType',
      key: 'transactionType',
    },
    {
      title: t('totalAmountColumn'),
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (value: any) => `$${nTot({ value })}`,
    },
    {
      title: t('totalFeeColumn'),
      dataIndex: 'totalFees',
      key: 'totalFees',
      render: (value: any) => `$${nTot({ value })}`,
    },
    {
      title: t('netAmountColumn'),
      dataIndex: 'netAmount',
      key: 'netAmount',
      render: (value: any) => `$${nTot({ value })}`,
    },
  ];

  // Data source for summary rows
  const dataSource = [
    {
      key: 'deposits',
      transactionType: t('deposit'),
      ...summaryData.deposits,
    },
    {
      key: 'withdrawals',
      transactionType: t('withdraw'),
      ...summaryData.withdrawals,
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={dataSource}
      pagination={false}
      summary={() => (
        <Table.Summary.Row>
          <Table.Summary.Cell index={0}>{}</Table.Summary.Cell>
          <Table.Summary.Cell
            index={1}
            className='font-bold'
          >
            {`$${nTot({
              value: summaryData.deposits.totalAmount + summaryData.withdrawals.totalAmount,
            })}`}
          </Table.Summary.Cell>
          <Table.Summary.Cell
            index={2}
            className='font-bold'
          >
            {`$${nTot({
              value: summaryData.deposits.totalFees + summaryData.withdrawals.totalFees,
            })}`}
          </Table.Summary.Cell>
          <Table.Summary.Cell
            index={3}
            className='font-bold'
          >
            {`$${nTot({
              value: summaryData.deposits.netAmount + summaryData.withdrawals.netAmount,
            })}`}
          </Table.Summary.Cell>
        </Table.Summary.Row>
      )}
      rowKey='key'
    />
  );
};

export default TotalRevenue;
