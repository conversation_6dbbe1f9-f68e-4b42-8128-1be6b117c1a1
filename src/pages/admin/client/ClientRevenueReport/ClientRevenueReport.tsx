// libs
import { useMemo } from 'react';
import { Space, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useOrderList } from '@/api/order';

// components
import { Title, Txt } from '@/components/TypographyMaster';
import TagAlpha from '@/components/TagAlpha';
import BtnFuncs from '@/components/BtnFuncs';

// hooks
import { dateFormator } from '@/hooks';

// store
import { useUserStore } from '@/store';

// utils
import { TxStatusNum, cryptoCurrencyOptions, valueToLabel } from '@/utils';

// pages
import DepositRevenue from './DepositRevenue';
import { CallReportSubmitValues } from './CalReportModal';
import TotalRevenue from './TotalRevenue';
import OutgoingRevenue from './OutGoingRevenue';

interface IClientRevenueReportProps {
  calReportValues: CallReportSubmitValues | undefined;
}

const ClientRevenueReport: React.FunctionComponent<IClientRevenueReportProps> = (props) => {
  // props
  const { calReportValues } = props || {};
  const { dateRange } = calReportValues || {};

  // hooks
  const { isDark } = useUserStore();
  const { t } = useTranslation('clientRevenueReport');

  // query
  const { data, isError, isPending, refetch, isRefetching } = useOrderList({
    enabled: !!calReportValues,
    params: {
      CreatedAtStart: dateRange?.from?.format(),
      CreatedAtEnd: dateRange?.to?.format(),
      Status: [TxStatusNum.Completed],
    },
  });
  // compute

  const orders = useMemo(() => {
    if (isError || !data) return [];
    return data.items;
  }, [data, isError]);

  return (
    <div
      className={`
				rounded  border p-2
				${isDark ? 'border-slate-600' : 'border-blue-300'}
				${isDark ? 'bg-slate-800' : 'bg-blue-100'}
			`}
    >
      <header className='pt-2'>
        <section className='flex justify-between'>
          <Title
            level={3}
            tight
          >
            {t('title')}
          </Title>
          <Space
            className='items-start'
            size='large'
          >
            {!calReportValues ? (
              <Title type='secondary'>{t('emptyMessage')}</Title>
            ) : (
              <>
                <Space size='small'>
                  <Txt>{t('crypto')}:</Txt>
                  <Txt>{valueToLabel(calReportValues.currency, cryptoCurrencyOptions)}</Txt>
                </Space>

                <Space size='small'>
                  <Txt>{t('date')}:</Txt>
                  <Txt>{`${calReportValues.dateRange.from.format(
                    dateFormator.accurate,
                  )} ~ ${calReportValues.dateRange.to.format(dateFormator.accurate)}`}</Txt>
                </Space>

                <Tooltip title={t('renewTooltip')}>
                  <BtnFuncs
                    iconType='redo'
                    onClick={() => refetch()}
                    type='primary'
                    shape='circle'
                    ghost
                    loading={isRefetching || isPending}
                  />
                </Tooltip>
              </>
            )}
          </Space>
        </section>

        {calReportValues && (
          <section className='my-2'>
            <Title level={5}>{t('merchantLabel')}:</Title>
            <div className='flex space-x-1'>
              {calReportValues.selectedMerchants.map((mapM, index) => {
                const { id, merchantName } = mapM;
                const key = `${id}-${index}`;
                return <TagAlpha key={key}>{merchantName}</TagAlpha>;
              })}
            </div>
          </section>
        )}
      </header>

      <main className='flex flex-col space-y-4'>
        {calReportValues && orders && (
          <>
            <DepositRevenue
              {...{ orders, calReportValues }}
              loading={isPending}
            />
            <OutgoingRevenue
              {...{ orders }}
              loading={isPending}
            />
            <TotalRevenue {...{ orders }} />
          </>
        )}
      </main>
    </div>
  );
};

export default ClientRevenueReport;
