// libs
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// api
import { OrderItemInterface } from '@/api/order';

// components
import TableAlpha from '@/components/TableAlpha';

// utils
import { TxCategoryNum } from '@/utils';

// pages
import useDrColumns, { IDrRow } from './useDrColumns';

interface IOutgoingRevenueProps {
  orders: Array<OrderItemInterface> | undefined;
  loading: boolean;
}

const OutgoingRevenue: React.FunctionComponent<IOutgoingRevenueProps> = (props) => {
  // props
  const { orders } = props || {};

  // hooks
  const { t } = useTranslation('outgoingRevenue');

  // compute
  const dataSource: Array<IDrRow> = useMemo(() => {
    if (!orders) return [];
    return orders
      .filter((o) => o.order.transactionType === TxCategoryNum.Withdraw)
      .map((o) => ({
        key: o.orderUid,
        orderInfo: o,
        fee: o.order.fee,
      }));
  }, [orders]);

  const { columns } = useDrColumns();

  return (
    <TableAlpha
      {...{ dataSource, columns }}
      titleRender={t('title')}
      size='small'
    />
  );
};

export default OutgoingRevenue;
