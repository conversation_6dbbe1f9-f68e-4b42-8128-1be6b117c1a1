// libs
import { Flex, Space, TableProps } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { ClientInterface } from '@/api/client';
import { MerchantInfo } from '@/api/merchant';

// components
import { Txt } from '@/components/TypographyMaster';

interface ICrmRow {
  key: string;
  client?: ClientInterface;
  merchant?: MerchantInfo;
  children?: Array<ICrmRow>;
}

type CrmColumnsProps = {
  setFeeInfoFrom: ReactSet<MerchantInfo | undefined>;
};

const useCrmColumns = (_useProps: CrmColumnsProps) => {
  // props
  // const { setFeeInfoFrom } = useProps;

  // hooks
  const { t } = useTranslation('reportCalModalColumns');

  const columns: TableProps<ICrmRow>['columns'] = [
    {
      title: <Txt>{t('clientColumn')}</Txt>,
      key: 'client',
      render: (_, { client }) => {
        if (client?.customerName) return <Txt>{client?.customerName}</Txt>;
        return undefined;
      },
    },
    {
      title: <Txt>{t('merchantColumn')}</Txt>,
      key: 'merchant',
      render: (_, { merchant }) => {
        if (!merchant) return undefined;
        const { merchantName, merchantNumber } = merchant;
        return (
          <Flex vertical>
            <Space>
              <Txt type='secondary'>{t('merchantName')}:</Txt>
              <Txt>{merchantName}</Txt>
            </Space>

            <Space>
              <Txt type='secondary'>{t('merchantNumber')}:</Txt>
              <Txt>{merchantNumber}</Txt>
            </Space>
          </Flex>
        );
      },
    },
    // {
    //   title: <Txt>{t('feeInfoColumn')}</Txt>,
    //   key: 'merchant',
    //   render: (_, { merchant }) => {
    //     if (!merchant) return undefined;
    //     return (
    //       <BtnFuncs
    //         type='primary'
    //         ghost
    //         iconType='viewList'
    //         onClick={() => setFeeInfoFrom(merchant)}
    //       />
    //     );
    //   },
    //   align: 'center',
    // },
  ];

  return { columns };
};
export default useCrmColumns;
export type { ICrmRow, CrmColumnsProps };
