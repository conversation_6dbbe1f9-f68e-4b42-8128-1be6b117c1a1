// libs
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Form, Select, Space } from 'antd';
import { SelectProps } from 'antd/lib';
import { useTranslation } from 'react-i18next';

// api
import { ClientMerchantsResult, useClientList, useClientsMerchants } from '@/api/client';
import { MerchantInfo } from '@/api/merchant';

// components
import { DateRangeFields, DateRangeOptions } from '@/components/DateRange';
import ModalAlpha from '@/components/ModalAlpha';
import TableAlpha from '@/components/TableAlpha';
import { Title } from '@/components/TypographyMaster';
import BtnFuncs from '@/components/BtnFuncs';

// hooks
import { useFormInit } from '@/hooks';

// utils
import {
  CryptoCurrencyEnum,
  CryptoProtocolEnum,
  cryptoCurrencyOptions,
  cryptoProtocolOptions,
  valuesToOptions,
} from '@/utils';

// pages
import useCrmColumns, { ICrmRow } from './useCrmColumns';
import FeeDetailModal from '../../components/FeeDetailModal';

type CalReportFormValues = {
  protocol: CryptoProtocolEnum;
  currency: CryptoCurrencyEnum;
  dateRange: DateRangeOptions;
};

type CallReportSubmitValues = {
  selectedMerchantRows: Array<ICrmRow>;
  selectedMerchants: Array<MerchantInfo>;
} & CalReportFormValues;

interface ICalReportModalProps {
  open: boolean;
  setOpen: ReactSet<boolean>;
  onSubmit?: (values: CallReportSubmitValues) => void;
}

const CalReportModal: React.FunctionComponent<ICalReportModalProps> = (props) => {
  // props
  const { open, setOpen, onSubmit } = props || {};

  // states
  const [currencyOptions, setCurrencyOptions] = useState<SelectProps['options']>([]);
  const [feeInfoFrom, setFeeInfoFrom] = useState<MerchantInfo>();
  const [expandedRows, setExpandedRows] = useState<Array<string>>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<string>>([]);

  // hooks
  const initialValues = useFormInit({ type: 'calReport', isTest: false });
  const { t } = useTranslation('calReportModal');

  // queries
  const { data: clxInfo, isError } = useClientList({});
  const customerNames = useMemo(
    () => (clxInfo && !isError ? clxInfo.map((mapC) => mapC.customerName) : []),
    [clxInfo, isError],
  );

  const cmxQueries = useClientsMerchants({ customerNames }) as Array<ClientMerchantsResult>;

  // compute
  const dataSource: Array<ICrmRow> = useMemo(() => {
    if (!clxInfo || isError) return [];
    return clxInfo.map((mapC) => {
      const key = `${mapC.customerId}`;
      const { customerName } = mapC;
      const mxQuery = cmxQueries.find((findQ) => findQ.data?.clientName === customerName);
      const children =
        mxQuery && !!mxQuery.data?.merchantsInfo.totalCount
          ? mxQuery.data?.merchantsInfo.items.map((mapI) => {
              const merchantKey = `${mapC.customerId}-${mapI.id}`;
              return { key: merchantKey, merchant: mapI };
            })
          : undefined;

      return { key, client: mapC, children };
    });
  }, [clxInfo, cmxQueries, isError]);

  const { columns } = useCrmColumns({ setFeeInfoFrom });

  const firstCmxSource = useMemo(() => {
    return dataSource.find((findD) => !!findD.children);
  }, [dataSource]);

  const clientRowKey = useMemo(() => dataSource.map((mapD) => mapD.key), [dataSource]);

  const allMerchantsRows = useMemo(
    () =>
      dataSource.reduce((preRows, current) => {
        if (!current.children) return preRows;
        return [...preRows, ...current.children];
      }, [] as Array<ICrmRow>),
    [dataSource],
  );

  const selectedMerchantRows = useMemo(() => {
    return selectedRowKeys
      .filter((filterK) => !clientRowKey.includes(filterK))
      .map((mapK) => {
        return allMerchantsRows.find((findR) => findR.key === mapK);
      }) as Array<ICrmRow>;
  }, [allMerchantsRows, clientRowKey, selectedRowKeys]);

  const selectedMerchants = useMemo(() => {
    return selectedMerchantRows
      .map((mapR) => {
        return mapR?.merchant;
      })
      .filter((filterM) => !!filterM) as Array<MerchantInfo>;
  }, [selectedMerchantRows]);

  const isSubmitable = useMemo(() => selectedMerchants.length > 0, [selectedMerchants.length]);

  // handlers
  const handleSelectKeys = useCallback(
    (datas: Array<React.Key>) => {
      const newKeys = datas as Array<string>;
      if (newKeys.length < selectedRowKeys.length) {
        const newKeySubs = selectedRowKeys.filter((filterK) => !newKeys.includes(filterK));
        if (newKeySubs.length > 1) setSelectedRowKeys(newKeys);
        else {
          const subKey = newKeySubs.at(0);
          if (!subKey) setSelectedRowKeys(newKeys);
          else {
            const row = dataSource.find((findD) => findD.key === subKey);
            if (!row) setSelectedRowKeys(newKeys);
            else {
              setSelectedRowKeys(() => {
                const childrenKeys = row.children?.map((mapC) => mapC.key) || [];
                return newKeys.filter((filterK) => !childrenKeys.includes(filterK));
              });
            }
          }
        }
      } else {
        const newKeyAdds = newKeys.filter((findK) => !selectedRowKeys.includes(findK));
        if (newKeyAdds.length > 1) setSelectedRowKeys(newKeys);
        else {
          const newKey = newKeyAdds.at(0);
          if (!newKey) setSelectedRowKeys(newKeys);
          else {
            const row = dataSource.find((findD) => findD.key === newKey);
            if (!row) setSelectedRowKeys(newKeys);
            else {
              setSelectedRowKeys(() => {
                const childrenKeys = row.children?.map((mapC) => mapC.key) || [];
                const concatKeys = newKeys.concat(childrenKeys);
                const uniqKeys = Array.from(new Map(concatKeys.map((mapK) => [mapK, mapK])).values());
                return uniqKeys;
              });
            }
          }
        }
      }
    },
    [dataSource, selectedRowKeys],
  );

  const handleSubmit = useCallback(
    (values: CalReportFormValues) => {
      if (onSubmit) onSubmit({ ...values, selectedMerchantRows, selectedMerchants });
    },
    [onSubmit, selectedMerchantRows, selectedMerchants],
  );

  // init
  const defaultExpandFirst = useRef(false);
  useEffect(() => {
    if (defaultExpandFirst.current || !firstCmxSource) return;
    defaultExpandFirst.current = true;
    setExpandedRows([firstCmxSource.key]);
  }, [firstCmxSource]);

  return (
    <>
      <ModalAlpha
        {...{ open }}
        onCancel={() => setOpen(false)}
        footer={null}
        title={
          <header>
            <Title level={3}>{t('title')}</Title>
          </header>
        }
        maximize
      >
        <Form
          {...{ initialValues }}
          layout='vertical'
          onFieldsChange={(cfs) => {
            cfs.forEach((eachF) => {
              const { value, name } = eachF;
              const fieldName = (() => {
                if (Array.isArray(name)) return name;
                return [name];
              })();
              if (fieldName.includes('protocol')) {
                const protocolOption = cryptoProtocolOptions.find((findP) => findP.value === value);
                const newCurrencyOptions = protocolOption
                  ? valuesToOptions(protocolOption.currencies, cryptoCurrencyOptions)
                  : [];
                if (newCurrencyOptions) setCurrencyOptions(newCurrencyOptions as typeof currencyOptions);
              }
            });
          }}
          onFinish={handleSubmit}
        >
          <div className='flex justify-between'>
            {/* left */}
            <Space
              className='w-full items-start'
              size='large'
            >
              <Form.Item
                label={t('protocolLabel')}
                name='protocol'
              >
                <Select
                  placeholder={t('protocolPlaceholder')}
                  options={cryptoProtocolOptions}
                />
              </Form.Item>

              <Form.Item
                label={t('cryptoLabel')}
                name='currency'
              >
                <Select
                  placeholder={t('cryptoPlaceholder')}
                  options={currencyOptions}
                />
              </Form.Item>

              <Form.Item
                label={t('dateLabel')}
                name='dateRange'
              >
                <DateRangeFields />
              </Form.Item>
            </Space>

            {/* right */}
            <div className=' flex items-end pb-4'>
              <BtnFuncs
                iconType='submit'
                type='primary'
                htmlType='submit'
                disabled={!isSubmitable}
              >
                {t('submit')}
              </BtnFuncs>
            </div>
          </div>

          <TableAlpha
            {...{ dataSource, columns }}
            size='small'
            rowKey='key'
            expandable={{
              expandedRowKeys: expandedRows,
              onExpandedRowsChange: (newKeys) => {
                setExpandedRows([...newKeys] as typeof expandedRows);
              },
            }}
            rowSelection={{
              selectedRowKeys,
              onChange: handleSelectKeys,
              hideSelectAll: true,
            }}
          />
        </Form>
      </ModalAlpha>

      <FeeDetailModal
        merchant={feeInfoFrom}
        setMerchant={setFeeInfoFrom}
      />
    </>
  );
};

export default CalReportModal;
export type { CalReportFormValues, CallReportSubmitValues, ICalReportModalProps };
