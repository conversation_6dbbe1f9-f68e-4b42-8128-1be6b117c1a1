// libs
import { useMemo, useState } from 'react';

// api
import { useAllStaffAccs } from '@/api';

// components
import TableAlpha from '@/components/TableAlpha';

// hooks
import { useTableStates } from '@/hooks';

// pages
import { PlusOutlined } from '@ant-design/icons';
import ButtonAlpha from '@/components/ButtonAlpha';
import useSfxColumns from './useSfxColumnx';

interface IStaffAccListProps {
  isActive?: boolean;
}

const StaffAccList: React.FunctionComponent<IStaffAccListProps> = (props) => {
  // props
  const { isActive } = props || {};

  // states
  const [appUserId] = useState<string>();
  const [nickName] = useState<string>();
  const [username] = useState<string>();

  // hooks
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({ sizeInit: 15 });

  const { data, isPending, isError } = useAllStaffAccs({
    enabled: isActive,
    params: {
      PageSize: pageSize,
      PageNumber: currentPage,
      AppUserId: appUserId,
      NickName: nickName,
      UserName: username,
    },
  });

  const dataSource = useMemo(() => {
    if (!data || isError) return [];
    return data.items;
  }, [data, isError]);

  const { columns } = useSfxColumns({ dataSource });

  return (
    <div>
      <ButtonAlpha
        icon={<PlusOutlined />}
        shape='round'
        type='primary'
        className='mb-2'
      >
        建立人員(fake)
      </ButtonAlpha>
      <TableAlpha
        {...{ columns, dataSource, pageSize, setPageSize, currentPage, setCurrentPage }}
        totalDataLength={data?.totalCount}
        rowKey='id'
        loading={isPending}
        size='small'
      />
    </div>
  );
};

export default StaffAccList;
