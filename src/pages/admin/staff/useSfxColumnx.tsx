// libs
import { useMemo, useState } from 'react';
import { Badge, Checkbox, Space, TableProps, Tag, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

// components
import { Txt, TxtCompressible } from '@/components/TypographyMaster';

// api
import { StaffAccOptions } from '@/api/staff';

// store
import { useUserStore } from '@/store';

type SfxColumnsProps = {
  dataSource: Array<StaffAccOptions>;
};

const useSfxColumns = (useProps: SfxColumnsProps) => {
  // props
  // Temporary fix for undefined error
  const { dataSource = [] } = useProps;

  // states
  const [expandedUserId, setExpandedUserID] = useState<Array<string>>([]);
  const [hoverUserId, setHoverUserId] = useState<Array<string>>([]);

  // hooks
  const { isDark } = useUserStore();
  const { t } = useTranslation('staffAccListColumns');

  // compute
  const allUserId = useMemo(() => dataSource.map((mapI) => mapI.appUserId), [dataSource]);

  const columns: TableProps<StaffAccOptions>['columns'] = useMemo(
    () => [
      {
        title: () => <Txt className='text-nowrap'>{t('usernameColumn')}</Txt>,
        key: 'userName',
        align: 'center',
        render: (_, { userName }) => <Txt>{userName}</Txt>,
      },
      {
        title: () => {
          const isAllExpanded = allUserId.every((everyI) => expandedUserId.includes(everyI));
          return (
            <Space>
              <Txt className='text-nowrap items-center justify-center px-5'>{t('userIdColumn')}</Txt>
              <Tooltip title={t('expandTooltip')}>
                <Checkbox
                  checked={isAllExpanded}
                  indeterminate={!isAllExpanded && !!expandedUserId.length}
                  onChange={(e) => {
                    if (e.target.checked) setExpandedUserID(allUserId);
                    else setExpandedUserID([]);
                  }}
                />
              </Tooltip>
            </Space>
          );
        },
        key: 'appUserId',
        align: 'center',
        render: (_, { appUserId }) => {
          const isHovered = hoverUserId.includes(appUserId);
          const isExpanded = expandedUserId.includes(appUserId);
          return (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={appUserId}
              setIsHover={setHoverUserId}
              setIsExpanded={setExpandedUserID}
            />
          );
        },
      },

      {
        title: () => <Txt className='text-nowrap'>{t('nickNameColumn')}</Txt>,
        key: 'nickName',
        align: 'center',
        render: (_, { nickName }) => <Txt>{nickName}</Txt>,
      },
      {
        title: () => <Txt className='text-nowrap'>{t('roleColumn')}</Txt>,
        key: 'roles',
        align: 'center',
        render: (_, { roles }) => (
          <Space>
            {roles.map((role, index) => (
              <Tag key={index}>{role}</Tag>
            ))}
          </Space>
        ),
      },
      {
        title: () => <Txt className='text-nowrap'>{t('timeColumn')}</Txt>,
        key: 'createdAt',
        align: 'center',
        render: (_, { createdAt }) => <Txt>{new Date(createdAt).toLocaleString()}</Txt>,
      },

      // add operationPasswordEnabled
      {
        title: () => <Txt className='text-nowrap'>{t('operationPasswordColumn')}</Txt>,
        key: 'operationPasswordEnabled',
        align: 'center',
        render: (_, { operationPasswordEnabled }) => (
          <Badge
            status={operationPasswordEnabled ? 'success' : 'error'}
            text={operationPasswordEnabled ? t('enabled') : t('disabled')}
          />
        ),
      },
      // add twoFactorEnabled
      {
        title: () => <Txt className='text-nowrap'>{t('_2FAColumn')}</Txt>,
        key: 'twoFactorEnabled',
        align: 'center',
        render: (_, { twoFactorEnabled }) => (
          <Badge
            status={twoFactorEnabled ? 'success' : 'error'}
            text={twoFactorEnabled ? t('enabled') : t('disabled')}
          />
        ),
      },
      {
        title: () => <Txt className='text-nowrap'>{t('actionColumn')}</Txt>,
        key: 'action',
        align: 'center',
        render: (_, _record) => {
          return <div>{t('emptyFunctional')}</div>;
        },
        width: 100,
      },
    ],
    [isDark, t],
  );

  return { columns };
};

export default useSfxColumns;
export type { StaffAccOptions, SfxColumnsProps };
