// libs
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { But<PERSON>, Row, Col } from 'antd';
import { CheckOutlined, ContainerOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// api
import { OrderItemInterface, useAppropveDeposit, useCancelOrder } from '@/api/order';

// components
import { TxVfyModal } from '@/components/ModalAlpha';
import OpwVfyModal from '@/components/ModalAlpha/OpwVfyModal';

// store
import { useNotifyStore } from '@/store';

// utils
import { TxStatusNum } from '@/utils';

// pages
import OrderDescriptions from '../deposit/OrderDetailModal/OrderDescriptions';

interface IWithdrawDetailProps {
  order: OrderItemInterface | undefined;
}

const WithdrawDetail: React.FunctionComponent<IWithdrawDetailProps> = (props) => {
  // props
  const { order } = props || {};

  // states
  const [vfyFrom, setVfyFrom] = useState<OrderItemInterface>();
  const [failFrom, setFailFrom] = useState<OrderItemInterface>();
  const [isHashSelected] = useState(false);
  const [isMatched, setIsMatched] = useState(false); // Track matching state

  // hooks
  const { pushBEQ } = useNotifyStore();
  const { t } = useTranslation('withdrawDetail');

  // Compute if the order needs matching
  const needMatch = useMemo(() => order && [TxStatusNum.Timeout].includes(order.order.status), [order]);
  const anomalyTableRef = useRef<{ getCheckedHash: () => string | undefined }>(null);

  // Mutate handlers
  const { mutate: cancel, isPending: inCancel } = useCancelOrder({});
  const { mutate: approve, isPending: inAppropve, isSuccess } = useAppropveDeposit({ isTimeOut: needMatch });

  useEffect(() => {
    if (isSuccess) {
      setVfyFrom(undefined);
      setIsMatched(true);
    }
  }, [isSuccess]);

  // Cancel handler
  const handleSubmitCancel = useCallback(
    (values: { code: string }) => {
      if (!failFrom) return;
      cancel({
        orderUid: failFrom.orderUid,
        operationPassword: values.code,
      });
    },
    [cancel, failFrom],
  );

  // Approve handler
  const handleSubmitApprove = useCallback(
    (values: { code: string; hash: string; remark?: string }) => {
      if (!vfyFrom) return;

      let transactionHash = values.hash;

      // If needMatch = true, retrieve the checkedHash from DepositAnomalyMatchTable
      if (needMatch && anomalyTableRef.current) {
        transactionHash = anomalyTableRef.current.getCheckedHash()!;
      }

      approve({
        transactionHash,
        operationPassword: values.code,
        remark: values.remark,
        merchantOrderId: order?.merchantOrderId,
      });
    },
    [approve, needMatch, order?.merchantOrderId, vfyFrom],
  );

  return (
    <>
      <main>
        <Row
          gutter={3}
          justify='space-around'
          style={{ width: needMatch ? 1000 : 900 }}
        >
          <Col span={needMatch ? 13 : 24}>
            <OrderDescriptions orderDetail={order} />
          </Col>
        </Row>
      </main>

      <footer>
        <Row
          gutter={10}
          className='mt-4'
        >
          <Col flex='auto'>
            <Button
              icon={isMatched ? <CheckCircleOutlined /> : <CheckOutlined />}
              type={isMatched ? 'default' : 'primary'}
              block
              style={{
                background: isMatched ? 'green' : undefined,
                color: isMatched ? 'white' : undefined,
                transition: 'all 0.3s ease-in-out',
              }}
              onClick={() => {
                if (needMatch && !anomalyTableRef.current?.getCheckedHash()) {
                  pushBEQ([
                    {
                      title: 'UXM Settlement Corp',
                      des: t('emptyTransactionDescription'),
                    },
                  ]);
                  return;
                }
                setVfyFrom(order);
              }}
              disabled={
                isMatched ||
                !order ||
                [
                  TxStatusNum.Broadcasted,
                  TxStatusNum.Completed,
                  TxStatusNum.Canceled,
                  TxStatusNum.MerchantCallbackFailed,
                  TxStatusNum.BlockchainTransactionFailed,
                ].includes(order.order.status) ||
                (needMatch && !isHashSelected)
              }
            >
              {isMatched ? t('matchButtonText') : t('verifyButtonText')}
            </Button>
          </Col>
          <Col flex='auto'>
            <Button
              icon={<ContainerOutlined />}
              type='primary'
              ghost
              block
              danger
              onClick={() => setFailFrom(order)}
              disabled={
                !order ||
                [
                  TxStatusNum.Broadcasted,
                  TxStatusNum.Confirmed,
                  TxStatusNum.Completed,
                  TxStatusNum.Canceled,
                  TxStatusNum.MerchantCallbackFailed,
                  TxStatusNum.BlockchainTransactionFailed,
                ].includes(order.order.status)
              }
            >
              {t('determinateButtonText')}
            </Button>
          </Col>
        </Row>
      </footer>

      {/* Dialogues */}
      {/* Verify */}
      <TxVfyModal
        open={!!vfyFrom}
        setOpen={(isOpenSet) => {
          const isOpen = (() => {
            if (isOpenSet instanceof Function) return isOpenSet(!!vfyFrom);
            return isOpenSet;
          })();
          if (!isOpen) setVfyFrom(undefined);
        }}
        vfyProps={{
          loading: inAppropve,
          onFinish: handleSubmitApprove,
        }}
        needMatch={needMatch}
      />

      {/* Fail */}
      <OpwVfyModal
        vfyProps={{ loading: inCancel, onFinish: handleSubmitCancel }}
        open={!!failFrom}
        setOpen={(isOpenSet) => {
          const isOpen = (() => {
            if (isOpenSet instanceof Function) return isOpenSet(!!failFrom);
            return isOpenSet;
          })();
          if (!isOpen) setFailFrom(undefined);
        }}
      />
    </>
  );
};

export default WithdrawDetail;
