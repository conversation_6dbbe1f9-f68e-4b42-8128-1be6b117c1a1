import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Breadcrumb } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { MerchantOrderListRes, useMerchantOrderList } from '@/api/order/useMerchantOrder';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import SearchMaster from '@/components/SearchMaster';
import BtnTableFuncs from '@/components/BtnFuncs/BtnTableFuncs';
import PrintModal, { PrintMerchantWithdraw } from '@/components/PrintModal';
import TableAlpha from '@/components/TableAlpha';
import { dateFormator, useTableStates } from '@/hooks';
import useTranslateExcelTitleRow from '@/hooks/useTranslateExcelTitleRow';
import useDataSource from '@/hooks/useDataSource';
import useAdminSignalRConnection from '@/hooks/useAdminSignalRConnection';
import { useNotifyStore, useUserStore } from '@/store';
import {
  MerchantTransactionStatus,
  cryptoEnumOptions,
  exportSheetByArray,
  merchantTxStatusOptions,
  storageHelper,
} from '@/utils';
import { MerchantTransDto } from './type';
import useMerchantOrderColumns from './useMerchantWithdrawColumns';
import { MerchantOrderRowInterface } from './OperationPasswordModal';

interface IMerchantWithdrawProps {}

const MerchantWithdraw: React.FC<IMerchantWithdrawProps> = () => {
  const storageRange = storageHelper<{ from: string; to: string }>('merchantOrderRange').getItem();
  const defaultDateRange = storageRange
    ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
    : { from: dayjs().startOf('d'), to: dayjs().endOf('d') };

  // hooks
  const location = useLocation();
  const merchantNumber = location.state?.merchantNumber as number;
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const { pushBEQ } = useNotifyStore();
  const { isDark } = useUserStore();
  const printOrderRef = useRef(null);
  const { t } = useTranslation('merchantWithdraw');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({ translator: 'merchantWithdraw' });

  const orderUid = location.state?.orderUid as string;

  // states
  const [OrderUid, setOrderUid] = useState<string | undefined>(orderUid);
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange);
  const [MerchantNumber, setMerchantNumber] = useState<number | undefined>(merchantNumber);
  const [ApplicantName, setApplicantName] = useState<string>();
  const [TransactionHash, setTransactionHash] = useState<string>();
  const [Status, setStatus] = useState<Array<MerchantTransactionStatus>>([]);
  const [openPrint, setOpenPrint] = useState<boolean>(false);

  // Fetch transaction data
  const {
    data: orderInfo,
    isPending,
    isRefetching,
    refetch,
  } = useMerchantOrderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      CreatedAtStart: dateRange.from.format(),
      CreatedAtEnd: dateRange.to.format(),
      MerchantNumber,
      ApplicantName,
      Hash: TransactionHash,
      Status,
    },
  });

  const { dataSource, updateItem, applyFilters } = useDataSource<MerchantOrderRowInterface, MerchantOrderListRes>({
    txInfo: orderInfo,
    mapper: (items) =>
      ({
        ...items,
      }) as MerchantOrderRowInterface,
  });

  const { registerHandler, unregisterHandler } = useAdminSignalRConnection({});

  const handleFilterChange = (filters: { [key: string]: string[] }) => {
    if (filters.status) {
      setStatus(filters.status as unknown as Array<MerchantTransactionStatus>);
    }
    if (filters.merchantNumber) {
      applyFilters({ merchantNumber: filters.merchantNumber });
      // setMerchantNumber(filters["merchantNumber"] as unknown as Array<MerchantTransactionStatus>)
    }
    setCurrentPage(1);
  };
  const handleMemberOrderNotifiy = useCallback(
    (data: MerchantTransDto) => {
      updateItem((item) => item.id === data.id, {
        order: {
          requireAmount: data.requireAmount,
          actualAmount: data.actualAmount,
          transactionType: data.transactionType,
          cryptoType: data.cryptoType,
          status: data.status,
          remark: data.remark,
          hash: data.hash,
          to: data.to,
          from: data.from,
          gas: data.gas,
          fee: data.fee,
          confirmedAt: data.confirmedAt?.toString() || null,
        },
      });
    },
    [updateItem],
  );
  useEffect(() => {
    registerHandler('MerchantOrderNotify', handleMemberOrderNotifiy);
    return () => {
      unregisterHandler('MerchantOrderNotify', handleMemberOrderNotifiy);
    };
  }, [handleMemberOrderNotifiy, registerHandler, unregisterHandler]);
  useEffect(() => {
    if (location.state?.OrderUid) {
      setOrderUid(location.state.OrderUid);
    }
  }, [location.state]);
  const { columns, ModalComponent } = useMerchantOrderColumns({
    dataSource,
    refetch,
  });

  // Handlers
  const handleOnDateSubmit = useCallback(
    (newDate: DateRangeOptions) => {
      setDateRange(newDate);
      storageHelper<DateRangeOptions>('merchantOrderRange').setItem(newDate);
      refetch();
    },
    [refetch],
  );

  const handleSearch = useCallback(
    (values: { MerchantNumber?: string; ApplicantName?: string; TransactionHash?: string }) => {
      const isAllSame = Object.entries(values).every(([key, value]) => {
        if (key === 'MerchantNumber') return value === (MerchantNumber ? MerchantNumber.toString() : undefined);
        if (key === 'ApplicantName') return value === ApplicantName;
        if (key === 'TransactionHash') return value === TransactionHash;
        return false;
      });

      if (isAllSame) refetch();
      else {
        setMerchantNumber(values.MerchantNumber ? parseInt(values.MerchantNumber, 10) : undefined);
        setApplicantName(values.ApplicantName || undefined);
        setTransactionHash(values.TransactionHash || undefined);
      }
    },
    [MerchantNumber, ApplicantName, TransactionHash, refetch],
  );

  const handlePrint = (_: React.MouseEvent<HTMLElement, MouseEvent>) => {
    if (dataSource.length) {
      setOpenPrint(true);
    } else {
      pushBEQ([{ title: 'UXM Settlement Corp', des: t('emptyRecordsMessage') }]);
    }
  };
  const handleExportXlsx = useCallback(() => {
    const sheetTitleRow = [
      'merchantNumberTitleRow',
      'merchantNameTitleRow',
      'applicantTitleRow',
      'approverTitleRow',
      'createTimeTitleRow',
      'confirmedTimeTitleRow',
      'requireAmountTitleRow',
      'actualAmountTitleRow',
      'cryptoTitleRow',
      'toTitleRow',
      'statusTitleRow',
      'remarkTitleRow',
    ];
    const sheetDataRows = dataSource.map((mapM) => {
      const cryptoOption = cryptoEnumOptions.find((findO) => findO.value === mapM.order.cryptoType);
      const statusOption = merchantTxStatusOptions.find((findO) => findO.value === mapM.order.status);
      const createTime = mapM.createdAt ? dayjs(mapM.createdAt).format(dateFormator.accurate) : '';
      const confirmTime = mapM.order.confirmedAt ? dayjs(mapM.order.confirmedAt).format(dateFormator.accurate) : '';
      return [
        mapM.merchantNumber,
        mapM.merchantName,
        mapM.applicantName,
        mapM.approverName,
        createTime,
        confirmTime,
        mapM.order.requireAmount,
        mapM.order.actualAmount,
        cryptoOption?.label || '', // Crypto
        mapM.order.to,
        optionsT(statusOption?.label || 'undefined'),
        mapM.order.remark,
      ];
    });
    exportSheetByArray({
      arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
      sheetName: t('sheetName'),
      fileName: `${t('fileName')} ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
        dateFormator.accurate,
      )} `,
    });
  }, [dataSource, dateRange.from, dateRange.to, translateExcelTitleRow, t, optionsT]);

  return (
    <>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('homeBreadcrumb')}</Link>,
          },
          { title: t('currentBreadcrumb') },
        ]}
      />

      <header className='my-4 flex flex-col flex-wrap gap-y-2'>
        <DateRange
          loading={isRefetching}
          onDateSubmit={handleOnDateSubmit}
          defaultValues={dateRange}
        />

        <section className='flex flex-wrap items-end justify-between gap-2'>
          <SearchMaster
            titles={[
              { key: 'MerchantNumber', label: t('searchMerchantNumberLabel') },
              { key: 'ApplicantName', label: t('searchApplicantNameLabel') },
              { key: 'TransactionHash', label: t('searchTransactionHashLabel') },
            ]}
            defaultValues={{
              MerchantNumber: MerchantNumber ? MerchantNumber.toString() : '',
            }}
            onSearch={handleSearch}
          />
          <BtnTableFuncs
            loading={isPending || isRefetching}
            onPrintClick={handlePrint}
            onExcelClick={handleExportXlsx}
          />
        </section>
      </header>

      <main>
        <TableAlpha
          onFilterChange={handleFilterChange}
          {...{
            dataSource,
            columns,
            totalDataLength: orderInfo?.totalCount,
            currentPage,
            pageSize,
            setCurrentPage,
            setPageSize,
          }}
          rowClassName={(record) => {
            let className = '';

            if (record.id === OrderUid) {
              if (isDark) className = 'highlight-row-dark';
              else className = 'highlight-row';
            }
            return className;
          }}
          size='small'
          rowKey='id'
          loading={isPending}
        />
      </main>

      {ModalComponent}

      <PrintModal
        open={openPrint}
        setOpen={setOpenPrint}
        contentRef={printOrderRef}
        documentTitle={`Merchant withdraw ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
          dateFormator.accurate,
        )}`}
      >
        <PrintMerchantWithdraw
          {...{ columns, dataSource }}
          contentRef={printOrderRef}
        />
      </PrintModal>
    </>
  );
};

export default MerchantWithdraw;
