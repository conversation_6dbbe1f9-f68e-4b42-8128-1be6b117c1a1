// libs
import { useTranslation } from 'react-i18next';

// api
import { OrderItemInterface } from '@/api/order';

// components
import ModalAlpha from '@/components/ModalAlpha';
import { Title } from '@/components/TypographyMaster';

// pages
import WithdrawDetail from './WithdrawDetail';

interface IWithDrawDetailModalProps {
  order: OrderItemInterface | undefined;
  setOrder: ReactSet<IWithDrawDetailModalProps['order']>;
}

const WithDrawDetailModal: React.FunctionComponent<IWithDrawDetailModalProps> = (props) => {
  // props
  const { order, setOrder } = props || {};

  // hooks
  const { t } = useTranslation('withdrawDetailModal');

  return (
    <ModalAlpha
      title={<Title level={3}>{t('title')}</Title>}
      open={!!order}
      onCancel={() => setOrder(undefined)}
      footer={null}
      width='fit-content'
    >
      <WithdrawDetail {...{ order }} />
    </ModalAlpha>
  );
};

export default WithDrawDetailModal;
export type { IWithDrawDetailModalProps };
