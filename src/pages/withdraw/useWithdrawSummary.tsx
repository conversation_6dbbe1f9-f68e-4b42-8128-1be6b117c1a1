import { useCallback } from 'react';
import { Table, TableProps } from 'antd';
import { useTranslation } from 'react-i18next';
import { nTot } from '@/utils';
import { WithdrawRowInterface } from './useWithdrawColumns';

type WithdrawSummaryProps = {
  columns: TableProps['columns'];
};

const useWithdrawSummary = (useProps: WithdrawSummaryProps) => {
  // props
  const { columns } = useProps;

  // hooks
  const { t } = useTranslation('withdrawSummary');

  // compute
  const summary = useCallback(
    (pageData: readonly WithdrawRowInterface[]) => {
      if (!columns) return undefined;
      const summaryDatas = pageData as Array<WithdrawRowInterface>;

      // Initialize totals
      let totalRequireAmount = 0;
      let totalGas = 0;
      let totalFee = 0;
      let totalActualAmount = 0;

      // Calculate totals
      summaryDatas.forEach(({ order }) => {
        totalRequireAmount += order?.requireAmount || 0;
        totalGas += order?.gas || 0;
        totalFee += order?.fee || 0;
        totalActualAmount += order?.actualAmount || 0;
      });

      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            {columns.map((col, index) => {
              switch (col.key) {
                case 'amount':
                  return (
                    <Table.Summary.Cell
                      key={col.key}
                      index={index}
                      className='font-bold'
                    >
                      <div>
                        <div>
                          {t('totalRequireAmount')}: {nTot({ value: totalRequireAmount, digitsType: 'USD' })}
                        </div>
                        <div>
                          {t('totalActualAmount')}: {nTot({ value: totalActualAmount - totalFee, digitsType: 'USD' })}
                        </div>
                      </div>
                    </Table.Summary.Cell>
                  );
                case 'gas':
                  return (
                    <Table.Summary.Cell
                      key={col.key}
                      index={index}
                      align='right'
                      className='font-bold'
                    >
                      {totalGas.toFixed(2)}
                    </Table.Summary.Cell>
                  );
                case 'fee':
                  return (
                    <Table.Summary.Cell
                      key={col.key}
                      index={index}
                      align='right'
                      className='font-bold'
                    >
                      {totalFee.toFixed(2)}
                    </Table.Summary.Cell>
                  );
                case 'cryptoType':
                  return (
                    <Table.Summary.Cell
                      key={col.key}
                      index={index}
                      className='text-right font-bold'
                    >
                      {t('subTotal')}:
                    </Table.Summary.Cell>
                  );
                default:
                  return (
                    <Table.Summary.Cell
                      key={col.key}
                      index={index}
                    />
                  );
              }
            })}
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
    [columns, t],
  );

  return summary;
};

export default useWithdrawSummary;
