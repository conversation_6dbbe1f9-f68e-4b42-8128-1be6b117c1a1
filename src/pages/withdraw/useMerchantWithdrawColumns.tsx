import { useCallback, useEffect, useMemo, useState } from 'react';
import { TableColumnsType, Tooltip, Tag, Flex } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { LoadingOutlined } from '@ant-design/icons';
import { useRetryWithdrawal } from '@/api/order';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import TagMerchantTxStatus from '@/components/TagAlpha/TagMerchantTransactionStatus';
import OpwVfyModal from '@/components/ModalAlpha/OpwVfyModal';
import { useUserStore } from '@/store';
import { dateFormator } from '@/hooks';
import useAdminSignalRConnection from '@/hooks/useAdminSignalRConnection';
import { MerchantTransactionStatus, nTot } from '@/utils';
import OperationPasswordModal, { MerchantOrderRowInterface } from './OperationPasswordModal';

interface MerchantOrderColumnsProps {
  dataSource: MerchantOrderRowInterface[];
  refetch: (...args: any[]) => any;
}

const useMerchantOrderColumns = (props: MerchantOrderColumnsProps) => {
  // props
  const { dataSource, refetch } = props;

  // states
  const [open, setOpen] = useState<boolean>(false);
  // State for hovered and expanded items
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);
  // State for modal visibility and current order
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentOrder, setCurrentOrder] = useState<MerchantOrderRowInterface | undefined>();

  // hooks
  const { registerHandler, unregisterHandler } = useAdminSignalRConnection({});
  const { isDark } = useUserStore();
  const { t } = useTranslation('merchantWithdrawColumns');
  const { mutate: retry } = useRetryWithdrawal({
    onSuccess: () => {
      refetch();
      setOpen(false);
      setCurrentOrder(undefined);
    },
  });

  // handlers
  const showPasswordModal = (order: MerchantOrderRowInterface) => {
    setCurrentOrder(order);
    setIsModalVisible(true);
  };
  const handleModalClose = () => {
    setIsModalVisible(false);
    setCurrentOrder(undefined);
  };
  const handleModalSubmit = () => {
    setIsModalVisible(false);
  };
  const showRetryModal = (order: MerchantOrderRowInterface) => {
    setCurrentOrder(order);
    setOpen(true);
  };
  const handleRetry = (value: { code: string }) => {
    if (!currentOrder?.monitor) return;

    retry({
      monitorId: currentOrder?.monitor.id,
      operationPassword: value.code,
    });
  };
  const renderAllMerchantStatus = () => {
    const status = Object.fromEntries(
      Object.entries(MerchantTransactionStatus).filter(
        ([key]) => key !== MerchantTransactionStatus.Completed.toString(),
      ),
    );
    const keys = Object.keys(status)
      .map((item) => {
        if (!Number.isNaN(Number(item))) {
          return Number(item);
        }
        return undefined;
      })
      .filter((item) => item !== undefined) as Array<MerchantTransactionStatus>;

    return keys.map((key) => {
      return {
        value: key,
        text:
          // Display the BlockchainConfirmed status as Completed.
          key === MerchantTransactionStatus.BlockchainConfirmed ? (
            <TagMerchantTxStatus
              className='w-32 border text-center'
              status={MerchantTransactionStatus.Completed}
            />
          ) : (
            <TagMerchantTxStatus
              className='w-32 border text-center'
              status={key}
            />
          ),
        diabled: true,
      };
    });
  };
  const handleMerchantNotification = useCallback(() => {
    refetch();
  }, [refetch]);

  useEffect(() => {
    registerHandler('MerchantOrderNotify', handleMerchantNotification);

    return () => {
      unregisterHandler('MerchantOrderNotify', handleMerchantNotification);
    };
  }, [registerHandler, unregisterHandler, handleMerchantNotification]);

  const renderMerchantNumberFilters = useMemo(
    () =>
      Array.from(new Set(dataSource.map((item) => item.merchantNumber))).map((num) => ({
        text: num,
        value: num,
      })),
    [dataSource],
  );
  // Memoized columns
  const columns: TableColumnsType<MerchantOrderRowInterface> = useMemo(() => {
    return [
      {
        key: 'transactionType',
        dataIndex: 'transactionType',
        title: <Txt>{t('transactionTypeColumn')}</Txt>,
        align: 'center',
        width: 120,
        render: (_, { order }) => (
          <Tag
            color={
              (order.transactionType === 1 && 'blue-inverse') ||
              (order.transactionType === 2 && 'orange-inverse') ||
              'default'
            }
          >
            {(order.transactionType === 1 && t('transferIn')) || (order.transactionType === 2 && t('transferOut'))}
          </Tag>
        ),
      },
      {
        title: <Txt>{t('merchantColumn')}</Txt>,
        key: 'merchant',
        width: 100,
        render: (_, { merchantName, merchantNumber }) => {
          return (
            <Flex vertical>
              <Tooltip title={t('merchantName')}>
                <Txt>{merchantName}</Txt>
              </Tooltip>
              <Tooltip title={t('merchantNumber')}>
                <Txt code>{merchantNumber}</Txt>
              </Tooltip>
            </Flex>
          );
        },
      },
      {
        key: 'time',
        title: <Txt>{t('timeColumn')}</Txt>,
        width: 210,
        render: (_, { createdAt, order }) => {
          const createFormat = createdAt ? dayjs(createdAt).format(dateFormator.accurate) : '-';
          const confirmeFormat = order.confirmedAt ? dayjs(order.confirmedAt).format(dateFormator.accurate) : '-';
          return (
            <Flex gap={5}>
              <Flex vertical>
                <Txt type='secondary'>{t('createTime')}:</Txt>
                <Txt type='secondary'>{t('confirmTime')}:</Txt>
              </Flex>
              <Flex vertical>
                <Txt>{createFormat}</Txt>
                <Txt>{confirmeFormat}</Txt>
              </Flex>
            </Flex>
          );
        },
      },
      {
        title: <Txt>{t('addressesColumn')}</Txt>,
        key: 'addresses',
        width: 150,
        render: (_, { order }) => {
          return (
            <Flex gap={5}>
              <Flex vertical>
                <Txt type='secondary'>{t('from')}:</Txt>
                <Txt type='secondary'>{t('to')}:</Txt>
              </Flex>

              <Flex
                vertical
                className='w-20'
              >
                {order.from ? (
                  <Tooltip title={order.from}>
                    <Txt
                      copyable
                      ellipsis
                    >
                      {order.from}
                    </Txt>
                  </Tooltip>
                ) : (
                  <Txt ellipsis>-</Txt>
                )}
                {order.to ? (
                  <Tooltip title={order.to}>
                    <Txt
                      copyable
                      ellipsis
                    >
                      {order.to}
                    </Txt>
                  </Tooltip>
                ) : (
                  <Txt ellipsis>-</Txt>
                )}
              </Flex>
            </Flex>
          );
        },
      },
      {
        key: 'transactionHash',
        title: <Txt>{t('transactionHashColumn')}</Txt>,
        width: 150,
        render: (_, { order }) => {
          const isExpanded = expandedKeys.includes(order.hash || '');
          const isHovered = hoveredKeys.includes(order.hash || '');
          return order.hash ? (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={order.hash || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          ) : (
            '-'
          );
        },
      },
      {
        key: 'applicationName',
        dataIndex: 'applicationName',
        width: 120,
        title: <Txt>{t('applicationNameColumn')}</Txt>,
        render: (_, { applicantName }) => <Txt>{applicantName || '-'}</Txt>,
      },
      // 以後有多幣種再放
      // {
      //   key: 'cryptoType',
      //   align: 'center',
      //   title: <Txt>{t('cryptoTypeColumn')}</Txt>,
      //   render: (_, { order }) =>
      //     order.cryptoType ? <TagCryptoType cryptoType={order.cryptoType} /> : <LoadingOutlined />,
      // },

      // transactionType===2 轉出才算有消耗，後續在做加總時要注意這點
      {
        key: 'requireAmount',
        title: <Txt>{t('requireAmount')}</Txt>,
        align: 'right',
        width: 100,
        render: (_, { order }) => <Txt>{nTot({ value: order.requireAmount, digitsType: 'USD' })}</Txt>,
        sorter: (a, b) => a.order.requireAmount - b.order.requireAmount,
      },
      {
        title: <Txt>{t('feeColumn')}</Txt>,
        key: 'fee',
        dataIndex: 'fee',
        align: 'right',
        width: 100,
        sorter: (a, b) => a.order.fee - b.order.fee,
        render: (_, { order }) => {
          return <Txt>{order.fee ? nTot({ value: order.fee }) : '-'}</Txt>;
        },
      },
      {
        key: 'actualAmount',
        title: <Txt>{t('actualAmount')}</Txt>,
        align: 'right',
        width: 100,
        render: (_, { order }) => (
          <Txt
            strong
            className='text-[#389e0d]'
          >
            {order.actualAmount ? nTot({ value: order.actualAmount + order.fee, digitsType: 'USD' }) : '-'}
          </Txt>
        ),
        sorter: (a, b) => a.order.actualAmount - b.order.actualAmount,
      },
      {
        title: <Txt>{t('gasColumn')}</Txt>,
        key: 'gas',
        dataIndex: 'gas',
        align: 'right',
        width: 100,
        sorter: (a, b) => a.order.gas - b.order.gas,
        render: (_, { order }) => {
          return (
            <Flex
              justify='end'
              gap={5}
            >
              <Txt>{order.gas ? nTot({ value: order.gas }) : '-'}</Txt>
              <Txt type='secondary'>{(order.cryptoType === 1 && 'TRX') || ''}</Txt>
            </Flex>
          );
        },
      },
      {
        key: 'status',
        title: <Txt>{t('statusColumn')}</Txt>,
        align: 'center',
        width: 120,
        filters: renderAllMerchantStatus(),
        onFilter: (record, value) => {
          return record === value.order.status;
        },
        render: (_, record) => {
          const { order } = record;
          const isClickable = [MerchantTransactionStatus.Created].includes(order.status);
          const isRetryable = [MerchantTransactionStatus.Canceled, MerchantTransactionStatus.BlockchainFailed].includes(
            order.status,
          );

          return order.status ? (
            <TagMerchantTxStatus
              // Marks 'Blockchain Confirmed' as 'Completed' for Merchant withdrawal
              status={
                order.status === MerchantTransactionStatus.BlockchainConfirmed
                  ? MerchantTransactionStatus.Completed
                  : order.status
              }
              style={{
                cursor: isClickable || isRetryable ? 'pointer' : 'default',
              }}
              onClick={() => {
                if (isClickable) showPasswordModal(record);
                if (isRetryable) showRetryModal(record);
              }}
            />
          ) : (
            <LoadingOutlined />
          );
        },
      },
      {
        key: 'approverName',
        dataIndex: 'approverName',
        width: 100,
        title: <Txt>{t('approverNameColumn')}</Txt>,
        render: (_, { approverName }) => <Txt>{approverName || '-'}</Txt>,
      },
      {
        key: 'remark',
        dataIndex: 'remark',
        width: 70,
        title: <Txt>{t('remarkColumn')}</Txt>,
        render: (_, { order }) => {
          return <Txt>{order.remark || '-'}</Txt>;
        },
      },
    ];
  }, [expandedKeys, hoveredKeys, isDark, t, renderMerchantNumberFilters]);

  return {
    columns,
    ModalComponent: (
      <>
        <OperationPasswordModal
          key={currentOrder?.id}
          isVisible={isModalVisible}
          onClose={handleModalClose}
          onSubmit={handleModalSubmit}
          order={currentOrder}
        />
        <OpwVfyModal
          vfyProps={{ onFinish: handleRetry }}
          title={t('retryModalTitle')}
          open={open}
          setOpen={setOpen}
        />
      </>
    ),
  };
};

export default useMerchantOrderColumns;
