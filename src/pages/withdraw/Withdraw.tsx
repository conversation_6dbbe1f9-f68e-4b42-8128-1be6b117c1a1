import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Breadcrumb } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { useOrderList } from '@/api/order';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import PrintModal, { PrintWithdrawal } from '@/components/PrintModal';
import SearchMaster from '@/components/SearchMaster';
import TableAlpha from '@/components/TableAlpha';
import BtnTableFuncs from '@/components/BtnFuncs/BtnTableFuncs';
import { dateFormator, useTableStates } from '@/hooks';
import useExcelSummary from '@/hooks/useExcelSummary';
import useTranslateExcelTitleRow from '@/hooks/useTranslateExcelTitleRow';
import useDataSource from '@/hooks/useDataSource';
import useAdminSignalRConnection from '@/hooks/useAdminSignalRConnection';
import { useNotifyStore, useUserStore } from '@/store';
import {
  CryptoEnum,
  TxCategoryNum,
  TxStatusNum,
  cryptoEnumOptions,
  exportSheetByArray,
  storageHelper,
  txStatusOptions,
} from '@/utils';
import { MemberOrderNotifyDto } from './type';
import useWithdrawColumns, { WithdrawRowInterface } from './useWithdrawColumns';
import WithdrawDetailModal from './WithdrawDetailModail';
import useWithdrawSummary from './useWithdrawSummary';

type WithdrawSearchValues = {
  [key in 'MerchantNumber' | 'OrderUid' | 'MerchantOrderId' | 'SystemOrderId' | 'TransactionHash']?: string;
};

interface IWithdrawProps {}

const Withdraw: React.FC<IWithdrawProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const location = useLocation();
  const storageRange = storageHelper<{ from: string; to: string }>('withdrawRange').getItem();
  const defaultDateRange = storageRange
    ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
    : { from: dayjs().startOf('d'), to: dayjs().endOf('d') };
  const { t } = useTranslation('withdraw');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({ translator: 'withdraw' });
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const { pushBEQ } = useNotifyStore();
  const { isDark } = useUserStore();

  // states
  const [opeWithdrawDetail, setWithdrawDetail] = useState<WithdrawRowInterface>();
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange);
  const [OrderUid, setOrderUid] = useState<string | undefined>(location.state?.OrderUid);
  const [MerchantOrderId, setMerchantOrderId] = useState<string>();
  const [MerchantNumber, setMerchantNumber] = useState<number>();
  const [CryptoType, setCryptoType] = useState<CryptoEnum>();
  const [Status, setStatus] = useState<Array<TxStatusNum>>();
  const [SystemOrderId, setSystemOrderId] = useState<string>();
  const [TransactionHash, setTransactionHash] = useState<string>();
  const [openPrint, setOpenPrint] = useState<boolean>(false); // Controls the open/close state of the `PrintModal`.

  // refs
  const printWithdrawalRef = useRef(null); // `ref` of an element will be printed.

  useEffect(() => {
    if (location.state?.OrderUid) {
      setOrderUid(location.state.OrderUid);
    }
  }, [location.state]);
  // Fetch transaction data
  const {
    data: txInfo,
    isPending,
    isRefetching,
    refetch,
  } = useOrderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      TransactionType: TxCategoryNum.Withdraw,
      CreatedAtStart: dateRange.from.format(),
      CreatedAtEnd: dateRange.to.format(),
      OrderUid,
      MerchantOrderId,
      MerchantNumber,
      SystemOrderId,
      Hash: TransactionHash,
      CryptoType,
      Status,
    },
  });
  const { dataSource, updateItem, applyFilters } = useDataSource({
    txInfo,
    mapper: (items) =>
      ({
        ...items,
      }) as WithdrawRowInterface,
  });
  const { registerHandler, unregisterHandler } = useAdminSignalRConnection({});

  const handleMemberOrderNotifiy = useCallback(
    (data: MemberOrderNotifyDto) => {
      updateItem((item) => item.orderUid === data.orderUid, {
        order: {
          requireAmount: data.requireAmount,
          actualAmount: data.actualAmount,
          transactionType: data.transactionType,
          cryptoType: data.cryptoType,
          status: data.status,
          remark: data.remark,
          hash: data.hash,
          to: data.to,
          from: data.from,
          gas: data.gas,
          fee: data.fee,
          confirmedAt: data.confirmedAt?.toString() || '',
        },
      });
    },
    [updateItem],
  );
  useEffect(() => {
    registerHandler('MemberOrderNotify', handleMemberOrderNotifiy);

    return () => {
      unregisterHandler('MemberOrderNotify', handleMemberOrderNotifiy);
    };
  }, [handleMemberOrderNotifiy, registerHandler, unregisterHandler]);

  const { columns, Modals } = useWithdrawColumns({
    dataSource,
    setWithdrawDetail,
    refetch,
  });

  const viewDetailFromOrder = useMemo(
    () => txInfo?.items.find((findI) => findI.orderUid === opeWithdrawDetail?.orderUid),
    [opeWithdrawDetail?.orderUid, txInfo?.items],
  );

  // handlers
  const handleOnDateSubmit = useCallback(
    (newDate: DateRangeOptions) => {
      setDateRange(newDate);
      storageHelper<DateRangeOptions>('withdrawRange').setItem(newDate);
      refetch();
    },
    [refetch],
  );

  const summary = useWithdrawSummary({ columns });
  const excelSummary = useExcelSummary<any>({
    dataSource: dataSource.map((item) => ({ ...item, ...item.order })),
    offset: 9, // indexOf requireAmount = 9
    keysToSum: ['requireAmount', 'actualAmount', 'fee', 'gas'],
  });

  const handleSearch = useCallback(
    (values: WithdrawSearchValues) => {
      const isAllSame = Object.entries(values).every(([key, value]) => {
        if (key === 'MerchantNumber') return value === MerchantNumber?.toString();
        if (key === 'OrderUid') return value === OrderUid;
        if (key === 'MerchantOrderId') return value === MerchantOrderId;
        if (key === 'SystemOrderId') return value === SystemOrderId;
        if (key === 'TransactionHash') return value === TransactionHash;
        return false;
      });

      if (isAllSame) refetch();
      else {
        setOrderUid(values.OrderUid);
        setMerchantOrderId(values.MerchantOrderId || undefined);
        setSystemOrderId(values.SystemOrderId || undefined);
        setMerchantNumber(values.MerchantNumber ? parseInt(values.MerchantNumber, 10) : undefined);
        setTransactionHash(values.TransactionHash || undefined);
      }
    },
    [MerchantNumber, MerchantOrderId, OrderUid, SystemOrderId, TransactionHash, refetch],
  );

  /**
   * Opens the `PrintModal` component if `dataSource` is not empty; otherwise, shows an error notification.
   */
  const handlePrint = (_: React.MouseEvent<HTMLElement, MouseEvent>) => {
    if (dataSource.length) {
      setOpenPrint(true);
    } else {
      pushBEQ([{ title: 'UXM Settlement Corp', des: t('emptyRecordsMessage') }]);
    }
  };

  const handleExportXlsx = useCallback(() => {
    const sheetTitleRow = [
      'merchantNumberTitleRow',
      'merchantNameTitleRow',
      'systemUsernameTitleRow',
      'createTimeTitleRow',
      'confirmedTimeTitleRow',
      'merchantOrderNumberTitleRow',
      'transactionHashTitleRow',
      'cryptoTitleRow',
      'toTitleRow',
      'requireAmountTitleRow',
      'actualAmountTitleRow',
      'feeTitleRow',
      'gasTitleRow',
      'stateTitleRow',
      'remarkTitleRow',
    ];
    const sheetDataRows = [
      ...dataSource.map((mapW) => {
        const cryptoOption = cryptoEnumOptions.find((findO) => findO.value === mapW.order.cryptoType);
        const statusOption = txStatusOptions.find((findO) => findO.value === mapW.order.status);
        const createTime = mapW.createdAt ? dayjs(mapW.createdAt).format(dateFormator.accurate) : '';
        const confirmTime = mapW.order.confirmedAt ? dayjs(mapW.order.confirmedAt).format(dateFormator.accurate) : '';
        return [
          mapW.merchantNumber,
          mapW.merchantName,
          '--',
          createTime,
          confirmTime,
          mapW.merchantOrderId,
          mapW.order.hash,
          cryptoOption?.label || '', // Crypto
          mapW.order.to,
          mapW.order.requireAmount, // index = offset = 9 for excelSummary hook
          mapW.order.actualAmount,
          mapW.order.fee,
          mapW.order.gas,
          optionsT(statusOption?.label || 'undefined'),
          mapW.order.remark,
        ];
      }),
      excelSummary,
    ];
    exportSheetByArray({
      arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
      sheetName: t('sheetName'),
      fileName: `${t('fileName')} ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
        dateFormator.accurate,
      )} `,
    });
  }, [dataSource, dateRange.from, dateRange.to, excelSummary, translateExcelTitleRow, t, optionsT]);

  return (
    <>
      {/* Nav title */}
      <Breadcrumb
        items={[{ title: <Link to='private'>{t('homeBreadcrumb')}</Link> }, { title: t('currentBreadcrumb') }]}
      />

      {/* Header */}
      <header className='my-4 flex flex-col flex-wrap gap-y-2'>
        <DateRange
          loading={isRefetching}
          onDateSubmit={handleOnDateSubmit}
          defaultValues={dateRange}
        />
        <section className='flex flex-wrap items-end justify-between gap-2'>
          <SearchMaster
            titles={[
              { key: 'MerchantNumber', label: t('searchMerchantNumberLabel') },
              { key: 'OrderUid', label: t('searchOrderUidLabel') },
              { key: 'MerchantOrderId', label: t('searchMerchantOrderIdLabel') },
              { key: 'SystemOrderId', label: t('searchSystemOrderIdLabel') },
              { key: 'TransactionHash', label: t('searchTransactionHashLabel') },
            ]}
            onSearch={handleSearch}
            defaultValues={{
              OrderUid: OrderUid ?? '',
            }}
          />

          <BtnTableFuncs
            loading={isPending || isRefetching}
            onPrintClick={handlePrint}
            onExcelClick={handleExportXlsx}
          />
        </section>
      </header>

      <main>
        <TableAlpha
          {...{
            dataSource,
            columns,
            summary,
            totalDataLength: txInfo?.totalCount,
            currentPage,
            pageSize,
            setCurrentPage,
            setPageSize,
          }}
          size='small'
          rowKey='orderUid'
          loading={isPending}
          rowClassName={(record) => {
            let className = '';
            if (record.orderUid === OrderUid) {
              if (isDark) className = 'highlight-row-dark';
              else className = 'highlight-row';
            }
            return className;
          }}
          onFilterChange={(filters) => {
            if ('status' in filters) {
              setStatus(filters.status as unknown as Array<TxStatusNum>);
            }
            if ('crypto' in filters) {
              setCryptoType(filters.crypto.at(0) as typeof CryptoType);
            }

            if ('merchantNumber' in filters) {
              applyFilters({ merchantNumber: filters.merchantNumber });
            }
            setCurrentPage(1);
          }}
        />
      </main>

      {Modals}

      <WithdrawDetailModal
        order={viewDetailFromOrder}
        setOrder={(newOrderSet: any) => {
          const newOrder = (() => {
            if (newOrderSet instanceof Function) return newOrderSet(viewDetailFromOrder);
            return newOrderSet;
          })();
          if (!newOrder) setWithdrawDetail(undefined);
        }}
      />

      <PrintModal
        open={openPrint}
        setOpen={setOpenPrint}
        contentRef={printWithdrawalRef}
        documentTitle={`Withdraw ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
          dateFormator.accurate,
        )}`}
      >
        <PrintWithdrawal
          {...{ columns, dataSource, summary }}
          contentRef={printWithdrawalRef}
        />
      </PrintModal>
    </>
  );
};

export default Withdraw;
