import { useCallback, useEffect, useMemo, useState } from 'react';
import { TableColumnsType, Tooltip, Flex } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { LoadingOutlined } from '@ant-design/icons';
import { useRetryWithdrawal } from '@/api/order';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import { TagTxStatus } from '@/components/TagAlpha';
import TagMerchantTxStatus from '@/components/TagAlpha/TagMerchantTransactionStatus';
import OpwVfyModal from '@/components/ModalAlpha/OpwVfyModal';
import { dateFormator } from '@/hooks';
import useAdminSignalRConnection from '@/hooks/useAdminSignalRConnection';
import { useUserStore } from '@/store';
import { MerchantTransactionStatus, nTot, TxStatusNum } from '@/utils';

interface WithdrawRowInterface {
  systemOrderId: string; // System order ID
  monitor: {
    id: number;
    status: number;
    isUsdtEnough: boolean;
    usdtStatus: number;
    isTrxEnough: boolean;
    trxStatus: number;
    isEnergyEnough: boolean;
    energyStatus: number;
    usdtTransferredAmount: number;
    usdtToBeTransferred: number;
  } | null;
  orderUid: string; // Unique order ID
  systemUserName: string | null;
  createdAt: string; // Creation time
  merchantOrderId: string; // Merchant's order ID
  merchantId: number;
  merchantNumber: string; // Merchant number
  merchantName: string; // Merchant name
  merchantUserName: string | null;
  order: {
    requireAmount: number; // Requested amount
    actualAmount: number; // Actual amount
    transactionType: number; // Transaction type
    cryptoType: number; // Crypto type (e.g., 1 = USDT)
    status: number; // Transaction status
    remark: string | null; // Remarks
    hash: string | null; // Transaction hash
    to: string; // Destination address
    from: string | null; // Source address
    gas: number; // Gas fee
    fee: number; // Transaction fee
    confirmedAt: string | null; // Confirmation time
  };
}

interface WithdrawColumnsProps {
  dataSource: WithdrawRowInterface[];
  setWithdrawDetail: ReactSet<WithdrawRowInterface | undefined>;
  refetch: (...args: any[]) => any;
}

const useWithdrawColumns = (props: WithdrawColumnsProps) => {
  // props
  const { dataSource, setWithdrawDetail, refetch } = props;

  // states
  const [open, setOpen] = useState<boolean>(false);
  const [currentOrder, setCurrentOrder] = useState<WithdrawRowInterface | undefined>();
  // State for hovered and expanded items
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  // hooks
  const { isDark } = useUserStore();
  const { t } = useTranslation('withdrawColumns');
  const { registerHandler, unregisterHandler } = useAdminSignalRConnection({});
  const { mutate: retry } = useRetryWithdrawal({
    onSuccess: () => {
      refetch();
      setOpen(false);
      setCurrentOrder(undefined);
    },
  });

  // handlers
  const showRetryModal = (order: WithdrawRowInterface) => {
    setCurrentOrder(order);
    setOpen(true);
  };
  const handleRetry = (value: { code: string }) => {
    if (!currentOrder?.monitor) return;

    retry({
      monitorId: currentOrder?.monitor.id,
      operationPassword: value.code,
    });
  };
  const renderAllMerchantStatus = () => {
    const keys = Object.keys(TxStatusNum)
      .map((item) => {
        if (!Number.isNaN(Number(item))) {
          return Number(item);
        }
        return undefined;
      })
      .filter((item) => item !== undefined) as Array<TxStatusNum>;

    return keys.map((key) => {
      return {
        value: key,
        text: (
          <TagTxStatus
            className='w-32 border text-center'
            status={key}
          />
        ),
        diabled: true,
      };
    });
  };

  const handleMemberNotification = useCallback(() => {
    refetch();
  }, [refetch]);

  useEffect(() => {
    registerHandler('MemberOrderNotify', handleMemberNotification);

    return () => {
      unregisterHandler('MemberOrderNotify', handleMemberNotification);
    };
  }, [registerHandler, unregisterHandler, handleMemberNotification]);

  // Memoized columns
  const columns: TableColumnsType<WithdrawRowInterface> = useMemo(() => {
    return [
      {
        key: 'merchant',
        dataIndex: 'merchantNumber',
        title: <Txt>{t('merchantColumn')}</Txt>,
        filters: Array.from(new Set(dataSource.map((item) => item.merchantNumber))).map((num) => ({
          text: num,
          value: num,
        })),
        onFilter: (value, record) => record.merchantNumber === value,
        render: (_, { merchantName, merchantNumber }) => {
          return (
            <Flex vertical>
              <Tooltip title={t('merchantName')}>
                <Txt>{merchantName}</Txt>
              </Tooltip>
              <Tooltip title={t('merchantNumber')}>
                <Txt code>{merchantNumber}</Txt>
              </Tooltip>
            </Flex>
          );
        },
      },
      // {
      //   title: <Txt>{t('typeColumn')}</Txt>,
      //   key: 'type',
      //   align: 'center',
      //   render: (_, { order }) => {
      //     return <TagTxType type={order.transactionType} />;
      //   },
      // },
      {
        key: 'time',
        title: <Txt>{t('timeColumn')}</Txt>,
        render: (_, { createdAt, order }) => {
          const createFormat = createdAt ? dayjs(createdAt).format(dateFormator.accurate) : '-';
          const confirmeFormat = order.confirmedAt ? dayjs(order.confirmedAt).format(dateFormator.accurate) : '-';

          return (
            <Flex gap={5}>
              <Flex vertical>
                <Txt type='secondary'>{t('createTime')}:</Txt>
                <Txt type='secondary'>{t('confirmTime')}:</Txt>
              </Flex>
              <Flex vertical>
                <Txt>{createFormat}</Txt>
                <Txt>{confirmeFormat}</Txt>
              </Flex>
            </Flex>
          );
        },
      },
      {
        title: <Txt>{t('addressesColumn')}</Txt>,
        key: 'addresses',
        render: (_, { order }) => {
          return (
            <Flex gap={5}>
              <Flex vertical>
                <Txt type='secondary'>{t('from')}:</Txt>
                <Txt type='secondary'>{t('to')}:</Txt>
              </Flex>
              <Flex
                vertical
                className='w-20'
              >
                {order.from ? (
                  <Tooltip title={order.from}>
                    <Txt
                      copyable
                      ellipsis
                    >
                      {order.from}
                    </Txt>
                  </Tooltip>
                ) : (
                  <Txt ellipsis>-</Txt>
                )}

                {order.to ? (
                  <Tooltip title={order.to}>
                    <Txt
                      copyable
                      ellipsis
                    >
                      {order.to}
                    </Txt>
                  </Tooltip>
                ) : (
                  <Txt ellipsis>-</Txt>
                )}
              </Flex>
            </Flex>
          );
        },
      },
      {
        key: 'merchantOrderId',
        dataIndex: 'merchantOrderId',
        title: <Txt>{t('merchantOrderIdColumn')}</Txt>,
        render: (_, { merchantOrderId }) => {
          const isExpanded = expandedKeys.includes(merchantOrderId);
          const isHovered = hoveredKeys.includes(merchantOrderId);
          return (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={merchantOrderId}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: <Txt>UXM {t('orderUidColumn')}</Txt>,
        key: 'orderUid',
        render: (_, { orderUid }) => {
          const isExpanded = expandedKeys.includes(orderUid.toString() || '');
          const isHovered = hoveredKeys.includes(orderUid.toString() || '');
          return (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={orderUid.toString() || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: <Txt>UXM {t('systemOrderId')}</Txt>,
        key: 'systemOrderId',
        render: (_, { systemOrderId }) => {
          const isExpanded = expandedKeys.includes(systemOrderId.toString() || '');
          const isHovered = hoveredKeys.includes(systemOrderId.toString() || '');
          return (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={systemOrderId.toString() || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        key: 'transactionHash',
        title: <Txt>{t('transactionHashColumn')}</Txt>,
        render: (_, { order }) => {
          const isExpanded = expandedKeys.includes(order.hash || '');
          const isHovered = hoveredKeys.includes(order.hash || '');
          return order.hash ? (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={order.hash || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          ) : (
            '-'
          );
        },
      },

      // {
      //   key: 'cryptoType',
      //   align: 'center',
      //   title: <Txt>{t('cryptoTypeColumn')}</Txt>,
      //   render: (_, { order }) =>
      //     order.cryptoType ? <TagCryptoType cryptoType={order.cryptoType} /> : <LoadingOutlined />,
      // },

      {
        key: 'actualAmount',
        title: <Txt>{t('actualAmount')}</Txt>,
        align: 'right',
        render: (_, { order }) => (
          <Txt>{order.actualAmount ? nTot({ value: order.actualAmount, digitsType: 'USDT' }) : '-'}</Txt>
        ),
        sorter: (a, b) => a.order.requireAmount - b.order.requireAmount,
      },
      {
        title: <Txt>{t('feeColumn')}</Txt>,
        key: 'fee',
        dataIndex: 'fee',
        align: 'right',
        sorter: (a, b) => a.order.fee - b.order.fee,
        render: (_, { order }) => {
          return <Txt>{order.fee ? nTot({ value: order.fee }) : '-'}</Txt>;
        },
      },
      {
        key: 'transferAmount',
        title: <Txt>{t('transferAmount')}</Txt>,
        align: 'right',
        render: (_, { order }) => (
          <Txt
            strong
            className='text-[#389e0d]'
          >
            {order.actualAmount ? nTot({ value: order.actualAmount + order.fee, digitsType: 'USDT' }) : '-'}
          </Txt>
        ),
        sorter: (a, b) => a.order.requireAmount - b.order.requireAmount,
      },
      {
        title: <Txt>{t('gasColumn')}</Txt>,
        key: 'gas',
        dataIndex: 'gas',
        align: 'right',
        sorter: (a, b) => a.order.gas - b.order.gas,
        render: (_, { order }) => {
          return (
            <Flex
              justify='end'
              gap={5}
            >
              <Txt>{nTot({ value: order.gas })}</Txt>
              <Txt type='secondary'>{order.cryptoType === 1 && 'TRX'}</Txt>
            </Flex>
          );
        },
      },
      {
        title: <Txt>{t('statusColumn')}</Txt>,
        key: 'status',
        align: 'center',
        filters: renderAllMerchantStatus(),
        render: (_, record) => {
          const { order } = record;
          const isRetryable = [MerchantTransactionStatus.Canceled, MerchantTransactionStatus.BlockchainFailed].includes(
            order.status,
          );

          return order.status ? (
            <TagMerchantTxStatus
              status={order.status}
              style={{
                cursor: isRetryable ? 'pointer' : 'default',
              }}
              onClick={() => {
                if (isRetryable) {
                  showRetryModal(record);
                  return;
                }
                setWithdrawDetail(record);
              }}
            />
          ) : (
            <LoadingOutlined />
          );
        },
      },
    ];
  }, [dataSource, expandedKeys, hoveredKeys, isDark, setWithdrawDetail, t]);

  return {
    columns,
    Modals: (
      <OpwVfyModal
        vfyProps={{ onFinish: handleRetry }}
        title={t('retryModalTitle')}
        open={open}
        setOpen={setOpen}
      />
    ),
  };
};

export default useWithdrawColumns;
export type { WithdrawRowInterface };
