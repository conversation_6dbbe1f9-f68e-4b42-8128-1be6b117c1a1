// libs
import { FC } from 'react';
import { Input, Form, Button } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useTranslation } from 'react-i18next';

// api
import { useApproveWithdrawal } from '@/api/order/useApproveWithdrawal';

// components
import ModalAlpha from '@/components/ModalAlpha';
import { MerchantOrderItemInterface } from '@/api/order/useMerchantOrder';

interface MerchantOrderRowInterface {
  id: number; // Unique ID of the order
  merchantNumber: string; // Merchant number
  merchantName: string; // Merchant name
  applicantName: string; // Name of the applicant
  approverName: string | null; // Name of the approver (if any)
  order: {
    requireAmount: number; // Requested amount
    actualAmount: number; // Actual amount
    transactionType: number; // Transaction type
    cryptoType: number; // Crypto type (e.g., 1 = USDT)
    status: number; // Transaction status
    remark: string | null; // Remarks
    hash: string | null; // Transaction hash
    to: string; // Destination address
    from: string | null; // Source address
    gas: number; // Gas fee
    fee: number; // Transaction fee
    confirmedAt: string | null; // Confirmation time
  };
  monitor: {
    id: number;
    status: number;
    isUsdtEnough: boolean;
    usdtStatus: number;
    isTrxEnough: boolean;
    trxStatus: number;
    isEnergyEnough: boolean;
    energyStatus: number;
    usdtTransferredAmount: number;
    usdtToBeTransferred: number;
  } | null;
  createdAt: string; // Creation time
}

interface OperationPasswordModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSubmit: () => void;
  order?: MerchantOrderItemInterface;
}

const OperationPasswordModal: FC<OperationPasswordModalProps> = ({ isVisible, onClose, onSubmit, order }) => {
  // hooks
  const [form] = useForm();
  const { mutate: approveMutate, isPending: isApprovePending } = useApproveWithdrawal({});
  const { t } = useTranslation('operationPasswordModal');

  // handlers
  const handleSubmit = async (values: { operationPassword: string }) => {
    form.resetFields(['operationPassword']);
    if (order?.id) {
      approveMutate(
        { operationPassword: values.operationPassword, merchantTransactionId: order.id },
        {
          onSuccess: async () => {
            onSubmit();
          },
        },
      );
    }
  };

  return (
    <ModalAlpha
      title={`${t('title')} #${order?.id || ''}`}
      open={isVisible}
      onCancel={onClose}
      footer={null}
    >
      <Form
        form={form}
        onFinish={handleSubmit}
        layout='vertical'
      >
        <Form.Item
          label={t('operationPasswordLabel')}
          name='operationPassword'
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item className='mb-0'>
          <Button
            htmlType='submit'
            type='primary'
            loading={isApprovePending}
            className='mx-auto flex items-center justify-center rounded-lg'
            style={{
              backgroundColor: '#2563eb',
              border: 'none',
              color: '#fff',
            }}
          >
            {t('approveButtonText')}
          </Button>
        </Form.Item>
      </Form>
    </ModalAlpha>
  );
};

export default OperationPasswordModal;
export type { MerchantOrderRowInterface };
