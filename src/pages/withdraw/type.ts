export interface BaseTransferDto {
  hash: string;
  to: string;
  from: string;
  gas: number;
  cryptoType: CryptoType;
  remark: string;
}
export interface SupervisorTransDto extends BaseTransferDto {
  id: number;
  supervisorName: string;
  cryptoType: CryptoType;
  actualAmount: number;
  status: TransactionStatus;
  confirmedAt: Date | null; // 確認時間可為空
  createdAt: Date;
}

export interface MemberOrderNotifyDto extends BaseTransferDto {
  merchantNumber: string;
  orderUid: string;
  merchantOrderId: string;
  systemOrderId: string;
  requireAmount: number;
  actualAmount: number;
  transactionType: TransactionType;
  fee: number;
  confirmedAt?: Date;
  status: OrderStatus;
  merchantId: number;
  merchantUserId?: number;
  systemUserId?: number;
  isFeeSettled: boolean;
  isSentUSDT: boolean;
  createdAt: string;
}
export interface SupervisorTransaction {
  id: number;
  supervisorName: string;
  transactionType: number;
  cryptoType: number;
  actualAmount: number;
  to: string;
  from: string;
  hash: string | null;
  gas: number;
  status: TransactionStatus;
  remark: string;
  confirmedAt: string | null;
  createdAt: string;
}
export interface MerchantTransDto extends BaseTransferDto {
  id: number;
  merchantNumber: string;
  merchantName: string;
  applicantName: string; // 執行申請的人名
  approverName: string; // 允許申請的人名
  transactionType: TransactionType;
  requireAmount: number;
  actualAmount: number;
  fee: number;
  status: TransactionStatus;
  confirmedAt?: Date;
  createdAt: Date;
}

export interface EnergyLeaseFailedDto {
  monitorId?: number;
  paymentHash: string;
  expectedEnergyRecipientAddress: string;
  isOrderCanceled: boolean;
}

export interface DepositAnomalyDto extends BaseTransferDto {
  amount: number;
  status: OrderStatus;
  resolutionStatus: ResolutionStatus;
  resolvedAt?: Date;
  createdAt: Date;
}

export enum TransactionType {
  Deposit = 1,
  Withdrawal = 2,
}

export enum CryptoType {
  TRC20_USDT = 1,
  ERC20_USDT = 2,
  TRX = 3,
  ETH = 4,
}

export enum TransactionStatus {
  PendingReview = 1, // 等待管理員審核
  Approved = 2, // 已批准，等待執行
  InProgress = 3, // 處理中
  BlockchainBroadcast = 4, // 區塊鏈已廣播
  BlockchainConfirmed = 5, // 區塊鏈已確認

  Canceled = 100, // 已取消
  ResourceInsufficient = 1001, // 資源不足
  BlockchainFailed = 1002, // 區塊鏈失敗
  OtherError = 1003, // 其他錯誤
}

export interface MerchantBalanceNotifyDto {
  merchantId: number;
  balance: number;
  lockedBalance: number;
}
export enum OrderStatus {
  Retry = -1,
  Created = 1,
  BlockchainBroadcast = 2,
  BlockchainConfirmed = 3,
  Completed = 4,
  Canceled = 100,
  Timeout = 101,
  CallbackFail = 1001,
  BlockchainFailed = 1002,
}

export enum ResolutionStatus {
  Pending = 1, // 待處理
  InProgress = 2, // 處理中
  Completed = 3, // 處理完成
  Failed = 4, // 處理失敗
}
