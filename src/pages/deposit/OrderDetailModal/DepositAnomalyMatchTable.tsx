import { CheckCard } from '@ant-design/pro-components';
import { Card, Descriptions, Empty, Flex, Input, InputRef, Select, Skeleton, Space } from 'antd';
import dayjs from 'dayjs';
import { forwardRef, useCallback, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DepositAnomalyItemInterface, OrderItemInterface, useDepositAnomaly } from '@/api/order';
import BtnFuncs from '@/components/BtnFuncs';
import SpinMaster from '@/components/SpinMaster';
import { TagResolutionStatus, TagTxStatus } from '@/components/TagAlpha';
import { Txt } from '@/components/TypographyMaster';
import { dateFormator } from '@/hooks';
import { useUserStore } from '@/store';
import {
  AnomalyResolutionStatusEnum,
  CryptoEnum,
  TxStatusNum,
  anomalyResolutionOptions,
  cryptoEnumOptions,
  nTot,
} from '@/utils';

type DepositAnomalyMatchTableRef = {};

interface IDepositAnomalyMatchTableProps {
  orderDetail: OrderItemInterface | undefined;
  matchAnomalyDespoit: DepositAnomalyItemInterface | undefined;
  setMatchAnomalyDespoit: ReactSet<IDepositAnomalyMatchTableProps['matchAnomalyDespoit']>;
}

const DepositAnomalyMatchTable = forwardRef<DepositAnomalyMatchTableRef, IDepositAnomalyMatchTableProps>(
  (props, ref) => {
    // props
    const { orderDetail, matchAnomalyDespoit, setMatchAnomalyDespoit } = props || {};

    // states
    const [TransactionHash, setTransactionHash] = useState<string>();
    const [CryptoType, setCryptoType] = useState<CryptoEnum>(CryptoEnum.TRC20_USDT);
    const [ResolutionStatus, setResolutionStatus] = useState<AnomalyResolutionStatusEnum | undefined>(
      AnomalyResolutionStatusEnum.Pending,
    );

    // refs
    const inputRef = useRef<InputRef>(null);
    const inputValueTemp = useRef<string>();

    // hooks
    const { isDark } = useUserStore();
    const { t } = useTranslation('depositAnomalyMatchTable');
    const { t: optionsT } = useTranslation('options');

    // query
    const { data, isPending, isRefetching, isError, refetch } = useDepositAnomaly({
      staleTime: orderDetail ? 1000 * 5 : undefined,
      enabled: !!orderDetail && [TxStatusNum.Timeout].includes(orderDetail.order.status),
      params: { TransactionHash, ResolutionStatus, CryptoType, To: orderDetail?.order.to },
    });

    // compute
    const translateSelectOptions = useMemo(() => {
      return anomalyResolutionOptions.map((option) => ({ ...option, label: optionsT(option.label) }));
    }, [optionsT]);

    // handlers
    const handleSearch = useCallback(() => {
      setTransactionHash(inputRef.current?.input?.value || undefined);
      refetch();
    }, [refetch]);

    // Expose getCheckedHash method to parent via ref
    useImperativeHandle(ref, () => ({}));

    return (
      <Card
        title={
          <Flex
            vertical
            gap={5}
            className='py-2'
          >
            <Flex justify='space-between'>
              <Txt
                strong
                className='text-xl'
              >
                {t('title')}
              </Txt>
              <section>{isRefetching && <SpinMaster />}</section>
            </Flex>
            <Flex
              gap={20}
              align='center'
            >
              <Space>
                <Txt type='secondary'>{t('state')}</Txt>
                <Select
                  options={translateSelectOptions}
                  value={ResolutionStatus}
                  onChange={(newStatus) => setResolutionStatus(newStatus)}
                />
              </Space>

              <Space>
                <Txt type='secondary'>{t('currency')}</Txt>
                <Select
                  options={cryptoEnumOptions}
                  value={CryptoType}
                  onChange={(newType) => setCryptoType(newType)}
                />
              </Space>
            </Flex>
            <Flex
              gap={5}
              align='center'
            >
              <Txt type='secondary'>{t('hash')}</Txt>
              <Input
                inputMode='text'
                autoComplete='off'
                ref={inputRef}
                placeholder={t('hashPlaceholder')}
                onKeyUp={(e) => {
                  if (e.key === 'Enter') handleSearch();
                }}
                onChange={(e) => {
                  // Trigger query when pasted
                  const { value } = e.target;
                  const lengthD = value.length - (inputValueTemp.current?.length || 0);
                  if (lengthD > 1) handleSearch();
                  inputValueTemp.current = value;
                }}
              />
              <BtnFuncs
                loading={isPending || isRefetching}
                iconType='search'
                type='primary'
                onClick={handleSearch}
              />
            </Flex>
          </Flex>
        }
        styles={{
          body: { padding: 4, overflowY: 'auto', overflowX: 'hidden', scrollbarWidth: 'thin', maxHeight: '620px' },
        }}
      >
        {isPending && (
          <div className='flex flex-col space-y-6'>
            <Skeleton active />
            <Skeleton active />
            <Skeleton active />
          </div>
        )}
        {!isError &&
          !!data?.items.length &&
          data?.items.map((mapI) => {
            // prettier-ignore
            const { hash, amount, from, to,  remark, resolutionStatus, createdAt, status } = mapI;
            const isChecked = matchAnomalyDespoit?.hash === hash;

            const items = [
              {
                key: 'from',
                label: t('from'),
                children: (
                  <a
                    href={`https://tronscan.org/#/address/${from}/transfers`}
                    target='__blank'
                  >
                    <Txt
                      className='text-blue-500'
                      copyable
                    >
                      {from}
                    </Txt>
                  </a>
                ),
              },
              {
                key: 'to',
                label: t('to'),
                children: (
                  <a
                    href={`https://tronscan.org/#/address/${to}/transfers`}
                    target='__blank'
                  >
                    <Txt
                      className='text-blue-500'
                      copyable
                    >
                      {to}
                    </Txt>
                  </a>
                ),
              },
              {
                key: 'hash',
                label: t('hash'),
                children: (
                  <a
                    href={`https://tronscan.org/#/transaction/${hash}`}
                    target='__blank'
                  >
                    <Txt
                      ellipsis={{ suffix: hash.slice(-5) }}
                      className='w-96 text-blue-500'
                      copyable
                    >
                      {hash}
                    </Txt>
                  </a>
                ),
              },
            ];

            return (
              <div key={hash}>
                <CheckCard
                  className={`w-full ${isDark ? 'bg-black' : 'bg-white'}`}
                  checked={isChecked}
                  onChange={(newChecked) => {
                    if (newChecked) setMatchAnomalyDespoit(mapI);
                    else setMatchAnomalyDespoit(undefined);
                  }}
                  title={
                    <Space>
                      <TagResolutionStatus status={resolutionStatus} />
                      <Txt strong>{t('amount')}:</Txt>
                      <Txt>{nTot({ value: amount, digitsType: 'USD' })} </Txt>
                    </Space>
                  }
                  description={
                    <Flex vertical>
                      <Space>
                        <Space>
                          <Txt strong>{t('time')}:</Txt>
                          <Txt>{dayjs(createdAt).format(dateFormator.accurate)} </Txt>
                        </Space>
                        <Space>
                          <Txt strong>{t('state')}:</Txt>
                          <TagTxStatus {...{ status }} />
                        </Space>
                      </Space>
                      <Space>
                        <Txt strong>{t('remark')}:</Txt>
                        <Txt>{remark || '-'} </Txt>
                      </Space>
                      <Descriptions
                        items={items}
                        bordered
                        column={1}
                        contentStyle={{ padding: 4 }}
                        labelStyle={{ padding: 4 }}
                      />
                    </Flex>
                  }
                />
              </div>
            );
          })}
        {!isError && !data?.items.length && <Empty description={t('emptyMessage')} />}
      </Card>
    );
  },
);

export default DepositAnomalyMatchTable;
export type { DepositAnomalyMatchTableRef, IDepositAnomalyMatchTableProps };
