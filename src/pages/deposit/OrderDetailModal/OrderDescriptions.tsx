import { DescriptionsProps, Space, Avatar, Button, Descriptions } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { OrderItemInterface } from '@/api/order';
import { useReCallback } from '@/api/order/useReCallback';
import usdt from '@/assets/usdt.png';
import BtnFuncs from '@/components/BtnFuncs';
import OpwVfyModal from '@/components/ModalAlpha/OpwVfyModal';
import { TagTxStatus } from '@/components/TagAlpha';
import { Txt } from '@/components/TypographyMaster';
import { dateFormator } from '@/hooks';
import { useUserStore } from '@/store';
import { CryptoEnum, TxStatusNum, nTot, valueToLabel, cryptoEnumOptions } from '@/utils';
// import OrderLogs from '../components/OrderLogs';
import { useQueryClient } from '@tanstack/react-query';
import { ShopOutlined, UserOutlined } from '@ant-design/icons';
import CallbackLogs from '../components/CallbackLogs';

interface IOrderDescriptionsProps {
  orderDetail: OrderItemInterface | undefined;
}

const OrderDescriptions: React.FunctionComponent<IOrderDescriptionsProps> = (props) => {
  // props
  const { orderDetail } = props || {};

  // states
  const [reNtFrom, setReNtFrom] = useState<OrderItemInterface>();

  // hooks
  const { isDark } = useUserStore();
  const { t } = useTranslation('orderDescriptions');
  const queryClient = useQueryClient();

  // mutate
  const { mutate: reCallback, isPending: inReCallback } = useReCallback({
    onSuccess: () => {
      setReNtFrom(undefined);
      queryClient.invalidateQueries({ queryKey: ['callbackLog'] });
    },
  });

  // handlers
  const handleSubmitRecallback = useCallback(
    (values: { code: string }) => {
      if (!reNtFrom) return;
      reCallback({
        orderUid: reNtFrom.orderUid,
        operationPassword: values.code,
      });
    },
    [reCallback, reNtFrom],
  );

  // compute
  const items: DescriptionsProps['items'] = useMemo(() => {
    if (!orderDetail) return [];
    const avatarSrc = (() => {
      if (orderDetail.order.cryptoType === CryptoEnum.TRC20_USDT) return usdt;
      if (orderDetail.order.cryptoType === CryptoEnum.ERC20_USDT) return usdt;
      return '';
    })();

    const chainDetailURL = orderDetail?.order.hash
      ? `https://${import.meta.env.DEV ? 'nile.' : ''}tronscan.org/#/transaction/${orderDetail.order.hash}`
      : '-';

    return [
      {
        key: '1',
        label: t('orderType'),
        children: (
          <Space>
            <Avatar
              className={`${!orderDetail.merchantUserName ? 'bg-red-500' : 'bg-[#0958d9]'}`}
              icon={!orderDetail.merchantUserName ? <UserOutlined /> : <ShopOutlined />}
              shape='square'
            />
            <Txt
              strong
              className='text-xl'
            >
              {!orderDetail.merchantUserName ? t('memberOrderCreation') : t('merchantOrderCreation')}
            </Txt>
          </Space>
        ),
        span: 2,
      },
      {
        key: '2',
        label: t('uuppOrderNumber'),
        children: orderDetail.orderUid,
      },
      {
        key: '3',
        label: <Txt>UXM {t('serialNumber')}</Txt>,
        children: orderDetail.systemOrderId,
      },
      {
        key: '4',
        label: t('merchantOrderNumber'),
        children: orderDetail.merchantOrderId,
      },
      {
        key: '6',
        label: t('state'),
        children: (
          <div className='flex'>
            <TagTxStatus status={orderDetail.order.status} />

            {orderDetail.order.status === TxStatusNum.MerchantCallbackFailed && (
              <Space>
                <BtnFuncs
                  size='small'
                  iconType='redo'
                  className={`
									${isDark ? 'bg-purple-500' : 'bg-purple-300'}
								`}
                  onClick={() => setReNtFrom(orderDetail)}
                >
                  {t('resendCallback')}
                </BtnFuncs>
              </Space>
            )}
          </div>
        ),
      },
      {
        key: '4',
        label: t('createTime'),
        children: dayjs(orderDetail.createdAt).format(dateFormator.accurate),
      },
      {
        key: '5',
        label: t('verifiedTime'),
        children: orderDetail.order.confirmedAt
          ? dayjs(orderDetail.order.confirmedAt).format(dateFormator.accurate)
          : '-',
      },
      {
        key: '7',
        label: t('merchantName'),
        children: <Txt>{orderDetail.merchantName}</Txt>,
      },
      {
        key: '7',
        label: t('merchantNumber'),
        children: <Txt>{orderDetail.merchantNumber}</Txt>,
      },
      {
        key: '7',
        label: t('applicant'),
        children: <Txt>{orderDetail.merchantUserName || '-'}</Txt>,
        span: 2,
      },
      {
        key: '8',
        label: t('requireAmount'),
        children: <Txt>{nTot({ value: orderDetail.order.requireAmount, digitsType: 'USD' })}</Txt>,
      },
      {
        key: '9',
        label: t('currency'),
        children: (
          <Space>
            <Avatar
              size='small'
              src={avatarSrc}
            />
            <Txt>{valueToLabel(orderDetail.order.cryptoType, cryptoEnumOptions)}</Txt>
          </Space>
        ),
      },
      {
        key: '12',
        label: t('actualAmount'),
        span: 2,
        children: (
          <Txt
            strong
            className='text-[#389e0d]'
          >
            {orderDetail.order.actualAmount ? nTot({ value: orderDetail.order.actualAmount, digitsType: 'USD' }) : '-'}
          </Txt>
        ),
      },
      {
        key: '10',
        label: t('fee'),
        span: 2,
        children: <Txt>{orderDetail.order.fee ? nTot({ value: orderDetail.order.fee, digitsType: 'USD' }) : '-'}</Txt>,
      },
      // {
      //   key: '11',
      //   label: t('protocol'),
      //   children: <Txt>TRC20</Txt>,
      // },
      {
        key: '13',
        label: t('toAddress'),
        children: <Txt copyable>{orderDetail.order.to}</Txt>,
        span: 2,
      },
      {
        key: '13',
        label: t('publicChain'),
        children: (
          <Button
            type='link'
            size='small'
            onClick={() => {
              window.open(chainDetailURL, '_blank');
            }}
            className='px-0'
            disabled={!orderDetail?.order.hash}
          >
            {chainDetailURL}
          </Button>
        ),
        span: 2,
      },
      {
        key: '14',
        label: t('remark'),
        children: <Txt>{orderDetail.order.remark}</Txt>,
        span: 2,
      },
      {
        key: '14',
        label: t('approver'),
        children: <Txt>{orderDetail.systemUserName ? orderDetail.systemUserName : '-'}</Txt>,
        span: 2,
      },
      {
        key: '15',
        label: 'Log',
        children: (
          <main className='flex flex-col space-y-2'>
            {/* <section className='flex flex-col'> */}
            {/*   <Txt */}
            {/*     type='secondary' */}
            {/*     strong */}
            {/*   > */}
            {/*     {t('order')} */}
            {/*   </Txt> */}
            {/*   <OrderLogs order={orderDetail} /> */}
            {/* </section> */}

            <section className='flex flex-col'>
              <Txt
                type='secondary'
                strong
              >
                {t('callback')}
              </Txt>
              <CallbackLogs order={orderDetail} />
            </section>
          </main>
        ),
        span: 2,
      },
    ];
  }, [isDark, orderDetail, t]);

  return (
    <>
      <Descriptions
        size='small'
        bordered
        items={items}
        column={2}
      />

      {/* Notify */}
      <OpwVfyModal
        vfyProps={{ loading: inReCallback, onFinish: handleSubmitRecallback }}
        open={!!reNtFrom}
        setOpen={(isOpenSet) => {
          const isOpen = (() => {
            if (isOpenSet instanceof Function) return isOpenSet(!!reNtFrom);
            return isOpenSet;
          })();
          if (!isOpen) setReNtFrom(undefined);
        }}
      />
    </>
  );
};

export default OrderDescriptions;
