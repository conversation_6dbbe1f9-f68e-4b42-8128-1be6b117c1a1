import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Breadcrumb, Table } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { useSupervisorOrders, SupervisorOrderRes } from '@/api/supervisor/useSupervisorOrders';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import SearchMaster from '@/components/SearchMaster';
import BtnTableFuncs from '@/components/BtnFuncs/BtnTableFuncs';
import TableAlpha from '@/components/TableAlpha';
import PrintModal, { PrintSupervisorOrders } from '@/components/PrintModal';
import ModalAlpha from '@/components/ModalAlpha';
import { Title } from '@/components/TypographyMaster';
import { dateFormator, useTableStates } from '@/hooks';
import useTranslateExcelTitleRow from '@/hooks/useTranslateExcelTitleRow';
import useDataSource from '@/hooks/useDataSource';
import { useNotifyStore } from '@/store';
import { exportSheetByArray, nTot, storageHelper, txStatusOptions } from '@/utils';
import SupervisorOrderDetail from './SupervisorOrderDetail';
import useSupervisorOrderColumns, { SupervisorOrderRowInterface } from './useSupervisorOrderColumns';

interface ISupervisorOrdersProps {}

const SupervisorOrders: React.FC<ISupervisorOrdersProps> = () => {
  const storageRange = storageHelper<{ from: string; to: string }>('supervisorOrderRange').getItem();
  const defaultDateRange = storageRange
    ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
    : { from: dayjs().startOf('d'), to: dayjs().endOf('d') };

  // hooks
  const location = useLocation();
  const { t } = useTranslation('supervisorOrders');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({
    translator: 'supervisorOrders',
  });
  const supervisorName = location.state?.supervisorName as string;

  // states
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange);
  const [SupervisorName, setSupervisorName] = useState<string | undefined>(supervisorName);
  const [TransactionHash, setTransactionHash] = useState<string>();
  const [Status] = useState<number | undefined>();
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const { pushBEQ } = useNotifyStore();
  const [openPrint, setOpenPrint] = useState<boolean>(false);
  const printOrderRef = useRef(null);
  const [openOrderDetail, setOpenOrderDetail] = useState<SupervisorOrderRowInterface | undefined>();
  const [selectedRows, setSelectedRows] = useState<SupervisorOrderRowInterface[]>([]);

  const {
    data: orderInfo,
    isPending,
    isRefetching,
    refetch,
  } = useSupervisorOrders({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      CreatedAtStart: dateRange.from.format(),
      CreatedAtEnd: dateRange.to.format(),
      SupervisorName,
      Hash: TransactionHash,
      Status,
    },
  });
  const { dataSource } = useDataSource<SupervisorOrderRowInterface, SupervisorOrderRes>({
    txInfo: orderInfo,
    mapper: (items) =>
      ({
        ...items,
      }) as SupervisorOrderRowInterface,
  });

  const defaultAllSelected = useRef(false);
  useEffect(() => {
    if (defaultAllSelected.current || !dataSource.length) return;
    defaultAllSelected.current = true;

    setSelectedRows(dataSource);
  }, [dataSource]);

  const { columns, Modals } = useSupervisorOrderColumns({ dataSource, setOpenOrderDetail, refetch });

  const handleOnDateSubmit = useCallback(
    (newDate: DateRangeOptions) => {
      setDateRange(newDate);
      storageHelper<DateRangeOptions>('supervisorOrderRange').setItem(newDate);
      refetch();
    },
    [refetch],
  );

  const handleSearch = useCallback(
    (values: { SupervisorName?: string; TransactionHash?: string }) => {
      const isAllSame = Object.entries(values).every(([key, value]) => {
        if (key === 'SupervisorName') return value === SupervisorName;
        if (key === 'TransactionHash') return value === TransactionHash;
        return false;
      });

      if (isAllSame) refetch();
      else {
        setSupervisorName(values.SupervisorName || undefined);
        setTransactionHash(values.TransactionHash || undefined);
      }
    },
    [SupervisorName, TransactionHash, refetch],
  );

  const handlePrint = () => {
    if (dataSource.length) {
      setOpenPrint(true);
    } else {
      pushBEQ([{ title: 'Notice', des: t('emptyRecordsMessage') }]);
    }
  };

  const handleExportXlsx = useCallback(() => {
    const sheetTitleRow = [
      'supervisorNameTitleRow',
      'typeTitleRow',
      'createTimeTitleRow',
      'confirmedTimeTitleRow',
      'amountTitleRow',
      'hashTitleRow',
      'toTitleRow',
      'statusTitleRow',
      'gasTitleRow',
      'remarkTitleRow',
    ];
    const sheetDataRows = dataSource.map((item: SupervisorOrderRowInterface) => {
      const statusOption = txStatusOptions.find((findO) => findO.value === item.order.status);

      return [
        item.supervisorName,
        item.order.transactionType === 1 ? t('deposit') : t('withdraw'),
        item.createdAt,
        item.order.confirmedAt,
        item.order.actualAmount,
        item.order.hash,
        item.order.to,
        optionsT(statusOption?.label || 'undefined'),
        item.order.gas,
        item.order.remark,
      ];
    });
    exportSheetByArray({
      arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
      sheetName: t('sheetName'),
      fileName: `${t('fileName')} ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
        dateFormator.accurate,
      )}`,
    });
  }, [dataSource, dateRange, translateExcelTitleRow, t, optionsT]);

  const summary = () => {
    const totalRequireAmount = selectedRows.reduce((sum, row) => sum + row.order.requireAmount, 0);
    const totalActualAmount = selectedRows.reduce((sum, row) => sum + row.order.actualAmount, 0);
    const totalGas = selectedRows.reduce((sum, row) => sum + row.order.gas, 0);

    return (
      <Table.Summary.Row>
        <Table.Summary.Cell
          index={0}
          colSpan={6}
          className='text-end'
        >
          <strong>{t('subTotal')}:</strong>
        </Table.Summary.Cell>
        <Table.Summary.Cell
          index={1}
          align='right'
          className='font-bold'
        >
          {totalGas.toFixed(2)}
        </Table.Summary.Cell>
        <Table.Summary.Cell
          index={2}
          className='font-bold'
        >
          <div>
            <div>
              {t('totalRequireAmount')}: {nTot({ value: totalRequireAmount, digitsType: 'USD' })}
            </div>
            <div>
              {t('totalActualAmount')}: {nTot({ value: totalActualAmount, digitsType: 'USD' })}
            </div>
          </div>
        </Table.Summary.Cell>
      </Table.Summary.Row>
    );
  };

  return (
    <>
      <Breadcrumb
        items={[{ title: <Link to='dashboard'>{t('homeBreadcrumb')}</Link> }, { title: t('currentBreadcrumb') }]}
      />

      <header className='my-4 flex flex-col gap-y-2'>
        <DateRange
          loading={isRefetching}
          onDateSubmit={handleOnDateSubmit}
          defaultValues={dateRange}
        />
        <section className='flex flex-wrap items-end justify-between gap-2'>
          <SearchMaster
            titles={[
              { key: 'SupervisorName', label: t('searchNameLabel') },
              { key: 'TransactionHash', label: t('searchHashLabel') },
            ]}
            defaultValues={{
              SupervisorName: SupervisorName ?? '',
            }}
            onSearch={handleSearch}
          />
          <BtnTableFuncs
            loading={isPending || isRefetching}
            onPrintClick={handlePrint}
            onExcelClick={handleExportXlsx}
          />
        </section>
      </header>

      <main>
        <TableAlpha
          {...{
            dataSource,
            columns,
            totalDataLength: orderInfo?.totalCount,
            currentPage,
            pageSize,
            setCurrentPage,
            setPageSize,
          }}
          size='small'
          rowKey='id'
          loading={isPending}
          summary={summary}
        />
      </main>

      {Modals}

      <ModalAlpha
        title={<Title level={3}>{t('title')}</Title>}
        open={!!openOrderDetail}
        onCancel={() => setOpenOrderDetail(undefined)}
        footer={null}
        width='fit-content'
      >
        <SupervisorOrderDetail {...{ onCancel: () => setOpenOrderDetail(undefined), order: openOrderDetail }} />
      </ModalAlpha>
      <PrintModal
        open={openPrint}
        setOpen={setOpenPrint}
        documentTitle={`Supervisor Orders ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
          dateFormator.accurate,
        )}`}
        contentRef={printOrderRef}
      >
        <PrintSupervisorOrders
          {...{ columns, dataSource, summary }}
          contentRef={printOrderRef}
        />
      </PrintModal>
    </>
  );
};

export default SupervisorOrders;
