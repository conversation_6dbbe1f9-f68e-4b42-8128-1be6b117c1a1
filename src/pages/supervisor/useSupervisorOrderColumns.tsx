import { useCallback, useEffect, useMemo, useState } from 'react';
import { TableColumnsType, Space, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { LoadingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useRetryWithdrawal } from '@/api/order';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import { TagCryptoType, TagTxType } from '@/components/TagAlpha';
import OpwVfyModal from '@/components/ModalAlpha/OpwVfyModal';
import TagMerchantTxStatus from '@/components/TagAlpha/TagMerchantTransactionStatus';
import { useUserStore } from '@/store';
import { dateFormator } from '@/hooks';
import useAdminSignalRConnection from '@/hooks/useAdminSignalRConnection';
import { MerchantTransactionStatus, nTot } from '@/utils';

interface SupervisorOrderRowInterface {
  id: number;
  supervisorName: string;
  order: {
    requireAmount: number;
    actualAmount: number;
    transactionType: number;
    cryptoType: number;
    status: number;
    remark: string;
    hash: string | null;
    to: string;
    from: string;
    gas: number;
    fee: number;
    confirmedAt: string | null;
  };
  monitor: {
    id: number;
    status: number;
    isUsdtEnough: boolean;
    usdtStatus: number;
    isTrxEnough: boolean;
    trxStatus: number;
    isEnergyEnough: true;
    energyStatus: number;
    usdtTransferredAmount: number;
    usdtToBeTransferred: number;
  } | null;
  createdAt: string;
}

interface SupervisorOrderColumnsProps {
  dataSource: SupervisorOrderRowInterface[];
  setOpenOrderDetail: ReactSet<SupervisorOrderRowInterface | undefined>;
  refetch: (...args: any[]) => any;
}

const useSupervisorOrderColumns = (props: SupervisorOrderColumnsProps) => {
  // props
  const { dataSource, setOpenOrderDetail, refetch } = props;

  // states
  const [open, setOpen] = useState<boolean>(false);
  const [currentOrder, setCurrentOrder] = useState<SupervisorOrderRowInterface | undefined>();
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  // hooks
  const { isDark } = useUserStore();
  const { t } = useTranslation('supervisorOrderColumns');
  const { registerHandler, unregisterHandler } = useAdminSignalRConnection({});
  const { mutate: retry } = useRetryWithdrawal({
    onSuccess: () => {
      refetch();
      setOpen(false);
      setCurrentOrder(undefined);
    },
  });

  // handlers
  const showRetryModal = (order: SupervisorOrderRowInterface) => {
    setCurrentOrder(order);
    setOpen(true);
  };
  const handleRetry = (value: { code: string }) => {
    if (!currentOrder?.monitor) return;

    retry({
      monitorId: currentOrder?.monitor.id,
      operationPassword: value.code,
    });
  };
  const onClickStatus = useCallback(
    (row: SupervisorOrderRowInterface) => {
      setOpenOrderDetail(dataSource.find((item) => item.id === row.id));
    },
    [setOpenOrderDetail, dataSource],
  );

  const handleSupervisorNotification = useCallback(() => {
    refetch();
  }, [refetch]);

  useEffect(() => {
    registerHandler('SupervisorOrderNotify', handleSupervisorNotification);

    return () => {
      unregisterHandler('SupervisorOrderNotify', handleSupervisorNotification);
    };
  }, [registerHandler, unregisterHandler, handleSupervisorNotification]);

  const renderAllStatus = () => {
    const status = Object.fromEntries(
      Object.entries(MerchantTransactionStatus).filter(
        ([key]) => key !== MerchantTransactionStatus.Completed.toString(),
      ),
    );
    const keys = Object.keys(status)
      .map((item) => {
        if (!Number.isNaN(Number(item))) {
          return Number(item);
        }
        return undefined;
      })
      .filter((item) => item !== undefined) as Array<MerchantTransactionStatus>;

    return keys.map((key) => {
      return {
        value: key,
        text:
          // Display the BlockchainConfirmed status as Completed.
          key === MerchantTransactionStatus.BlockchainConfirmed ? (
            <TagMerchantTxStatus
              className='w-32 border text-center'
              status={MerchantTransactionStatus.Completed}
            />
          ) : (
            <TagMerchantTxStatus
              className='w-32 border text-center'
              status={key}
            />
          ),
        diabled: true,
      };
    });
  };

  const renderSupervisorFilters = useCallback(
    () =>
      Array.from(new Set(dataSource.map((item) => item.supervisorName))).map((name) => ({
        text: name,
        value: name,
      })),
    [dataSource],
  );

  const columns: TableColumnsType<SupervisorOrderRowInterface> = useMemo(() => {
    return [
      {
        key: 'supervisorName',
        dataIndex: 'supervisorName',
        title: <Txt>{t('supervisorColumn')}</Txt>,
        align: 'center',
        filters: renderSupervisorFilters(),
        onFilter: (value, record) => record.supervisorName === value,
        render: (_, { supervisorName }) => <Txt>{supervisorName}</Txt>,
      },
      {
        key: 'transactionType',
        dataIndex: 'transactionType',
        title: <Txt>{t('typeColumn')}</Txt>,
        align: 'center',
        render: (_, { order }) => {
          return <TagTxType type={order.transactionType} />;
        },
      },
      {
        key: 'time',
        align: 'center',
        title: <Txt>{t('timeColumn')}</Txt>,
        width: 180,
        render: (_, { createdAt, order }) => {
          const createFormat = createdAt ? dayjs(createdAt).format(dateFormator.accurate) : '--';
          const confirmFormat = order.confirmedAt ? dayjs(order.confirmedAt).format(dateFormator.accurate) : '--';

          return (
            <main className='flex flex-col text-left'>
              <Space>
                <Txt type='secondary'>{t('created')}:</Txt>
                <Txt>{createFormat}</Txt>
              </Space>
              <Space>
                <Txt type='secondary'>{t('confirmed')}:</Txt>
                <Txt>{confirmFormat}</Txt>
              </Space>
            </main>
          );
        },
      },
      {
        key: 'hash',
        align: 'center',
        dataIndex: 'hash',
        title: <Txt>{t('hashColumn')}</Txt>,
        width: 200,
        render: (_, { order }) => {
          const isExpanded = expandedKeys.includes(order.hash || '');
          const isHovered = hoveredKeys.includes(order.hash || '');

          return order.hash ? (
            <TxtCompressible
              {...{ isDark, isHovered, isExpanded }}
              text={order.hash || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          ) : (
            '--'
          );
        },
      },
      {
        title: <Txt>{t('addressesColumn')}</Txt>,
        key: 'addresses',
        align: 'center',
        render: (_, { order }) => {
          return (
            <main className='flex justify-center'>
              <div className='flex flex-col text-left'>
                <Space>
                  <Txt type='secondary'>{t('from')}:</Txt>
                  {order.from ? (
                    <Tooltip title={order.from}>
                      <Txt
                        copyable
                        className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                        ellipsis
                      >
                        {order.from}
                      </Txt>
                    </Tooltip>
                  ) : (
                    <Txt
                      className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                      ellipsis
                    >
                      --
                    </Txt>
                  )}
                </Space>

                <Space>
                  <Txt type='secondary'>{t('to')}:</Txt>
                  {order.to ? (
                    <Tooltip title={order.to}>
                      <Txt
                        copyable
                        className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                        ellipsis
                      >
                        {order.to}
                      </Txt>
                    </Tooltip>
                  ) : (
                    <Txt
                      className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                      ellipsis
                    >
                      --
                    </Txt>
                  )}
                </Space>
              </div>
            </main>
          );
        },
      },
      {
        key: 'cryptoType',
        align: 'center',
        title: <Txt>{t('cryptoTypeColumn')}</Txt>,
        render: (_, { order }) =>
          order.cryptoType ? <TagCryptoType cryptoType={order.cryptoType} /> : <LoadingOutlined />,
      },
      {
        title: <Txt>{t('gasColumn')}</Txt>,
        key: 'gas',
        dataIndex: 'gas',
        align: 'right',
        sorter: (a, b) => a.order.gas - b.order.gas,
        render: (_, { order }) => {
          return <Txt>{order.gas ? nTot({ value: order.gas }) : '--'}</Txt>;
        },
      },
      {
        key: 'amount',
        title: <Txt>{t('amountColumn')}</Txt>,
        align: 'center',
        render: (_, { order }) => (
          <main className='min-w-24 flex flex-col text-left'>
            <Space>
              <Txt type='secondary'>{t('requireAmount')}:</Txt>
              <Txt>{order.requireAmount ? nTot({ value: order.requireAmount, digitsType: 'USDT' }) : '--'}</Txt>
            </Space>
            <Space>
              <Txt type='secondary'>{t('actualAmount')}:</Txt>
              <Txt>{order.actualAmount ? nTot({ value: order.actualAmount, digitsType: 'USDT' }) : '--'}</Txt>
            </Space>
          </main>
        ),
        sorter: (a, b) => a.order.requireAmount - b.order.requireAmount,
      },
      {
        key: 'status',
        title: <Txt>{t('statusColumn')}</Txt>,
        align: 'center',
        filters: renderAllStatus(),
        onFilter: (record, value) => {
          return record === value.order.status;
        },
        render: (_, record) => {
          const { order } = record;
          const isRetryable = [MerchantTransactionStatus.Canceled, MerchantTransactionStatus.BlockchainFailed].includes(
            order.status,
          );

          return order.status ? (
            <TagMerchantTxStatus
              status={
                order.status === MerchantTransactionStatus.BlockchainConfirmed
                  ? MerchantTransactionStatus.Completed
                  : order.status
              }
              style={{
                cursor: isRetryable ? 'pointer' : 'default',
              }}
              onClick={() => {
                if (isRetryable) {
                  showRetryModal(record);
                  return;
                }
                onClickStatus(record);
              }}
            />
          ) : (
            <LoadingOutlined />
          );
        },
      },
      {
        key: 'remark',
        dataIndex: 'remark',
        title: <Txt className='pr-2'>{t('remarkColumn')}</Txt>,
        render: (_, { order }) => <Txt className='pr-2'>{order.remark || '--'}</Txt>,
      },
    ];
  }, [expandedKeys, hoveredKeys, isDark, onClickStatus, renderSupervisorFilters, t]);

  return {
    columns,
    Modals: (
      <OpwVfyModal
        vfyProps={{ onFinish: handleRetry }}
        title={t('retryModalTitle')}
        open={open}
        setOpen={setOpen}
      />
    ),
  };
};

export default useSupervisorOrderColumns;
export type { SupervisorOrderRowInterface };
