// libs
import React, { useState } from 'react';
import dayjs from 'dayjs';
import { Descriptions, Button, Row, Col, Divider } from 'antd';
import { CheckCircleOutlined, ContainerOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// components
import { TagCryptoType, TagTxStatus } from '@/components/TagAlpha';

// hooks
import { dateFormator } from '@/hooks';

// utils
import { MerchantTransactionStatus, nTot } from '@/utils';

// pages
import { SupervisorOrderRowInterface } from './useSupervisorOrderColumns';

interface ISupervisorOrderDetailProps {
  order: SupervisorOrderRowInterface | undefined;
  onCancel: () => void;
}

const SupervisorOrderDetail: React.FC<ISupervisorOrderDetailProps> = ({ order, onCancel }) => {
  // states
  const [isMatched, setIsMatched] = useState(true);

  // hooks
  const { t } = useTranslation('supervisorOrderDetail');

  return (
    <div className='rounded-md bg-white  p-6 '>
      <Descriptions
        bordered
        column={2}
        size='middle'
      >
        <Descriptions.Item label={t('nameLabel')}>{order?.supervisorName || '--'}</Descriptions.Item>
        <Descriptions.Item label={t('statusLabel')}>
          <TagTxStatus
            status={
              order?.order.status === MerchantTransactionStatus.BlockchainConfirmed
                ? MerchantTransactionStatus.Completed
                : order?.order.status || 0
            }
          />
        </Descriptions.Item>
        <Descriptions.Item label={t('amountLabel')}>
          {nTot({ value: order?.order.actualAmount || 0, digitsType: 'USDT' })}{' '}
          <TagCryptoType cryptoType={order?.order.cryptoType || 0} />
        </Descriptions.Item>
        <Descriptions.Item label={t('gasLabel')}>{order?.order.gas || 0} TRX</Descriptions.Item>
        <Descriptions.Item label={t('fromLabel')}>{order?.order.from || '--'}</Descriptions.Item>
        <Descriptions.Item label={t('toLabel')}>{order?.order.to || '--'}</Descriptions.Item>
        <Descriptions.Item
          label={t('hashLabel')}
          span={2}
        >
          {order?.order.hash || '--'}
        </Descriptions.Item>
        <Descriptions.Item
          label={t('remarkLabel')}
          span={2}
        >
          {order?.order.remark || '--'}
        </Descriptions.Item>
        <Descriptions.Item label={t('createAtLabel')}>
          {order?.createdAt ? dayjs(order.createdAt).format(dateFormator.accurate) : '--'}
        </Descriptions.Item>
        <Descriptions.Item label={t('confirmAtLabel')}>
          {order?.order.confirmedAt ? dayjs(order.order.confirmedAt).format(dateFormator.accurate) : '--'}
        </Descriptions.Item>
      </Descriptions>

      <Divider />

      <Row
        gutter={16}
        justify='center'
        style={{ marginTop: '20px' }}
      >
        <Col>
          <Button
            type={isMatched ? 'default' : 'primary'}
            icon={<CheckCircleOutlined />}
            onClick={() => setIsMatched(true)}
            style={{
              width: 150,
              background: isMatched ? 'green' : undefined,
              color: isMatched ? 'white' : undefined,
              transition: 'all 0.3s ease-in-out',
            }}
          >
            {isMatched ? t('matchedButtonText') : t('verifyButtonText')}
          </Button>
        </Col>
        <Col>
          <Button
            type='primary'
            ghost
            danger
            onClick={onCancel}
            icon={<ContainerOutlined />}
            style={{ width: 150 }}
          >
            {t('cancelButtonText')}
          </Button>
        </Col>
      </Row>
    </div>
  );
};

export default SupervisorOrderDetail;
export type { SupervisorOrderRowInterface };
