// libs
import { create } from 'zustand';

// api
import type { LoginProps, LoginRes, InfoRes } from '@/api';

// utils
import { forage, storageHelper } from '@/utils';

type UserStoreInit = {
  locale: LocaleTypes;
  soundEnable: boolean;
  isDark: boolean;
  loginProps: LoginProps | null;
  loginRes: LoginRes | null;
  isBackFilledLoginRes: boolean;
  info: InfoRes | null;
  isLoading: boolean;
  isConnected: boolean;
};

interface IUserStoreOptions extends UserStoreInit {
  setLocale: (locale: UserStoreInit['locale']) => void;
  setSoundEnable: (soundEnable: UserStoreInit['soundEnable']) => void;
  setIsDark: (isDark: UserStoreInit['isDark']) => void;
  setLoginProps: (loginProps: UserStoreInit['loginProps']) => void;
  setLoginRes: (loginRes: UserStoreInit['loginRes']) => void;
  setInfo: (info: UserStoreInit['info']) => void;
  setIsLoading: (isLoading: UserStoreInit['isLoading']) => void;
  setIsConnected: (isConnected: boolean) => void;
  reset: () => void;
}

const userStoreInit: UserStoreInit = {
  locale: storageHelper<LocaleTypes>('locale').getItem() ?? 'zh-TW',
  soundEnable: false,
  isDark: false,
  loginProps: null,
  loginRes: null,
  isBackFilledLoginRes: true,
  info: null,
  isLoading: false,
  isConnected: true,
};
const useUserStore = create<IUserStoreOptions>((set) => {
  forage<UserStoreInit['loginProps']>().getItem('loginProps', (_, loginProps) => {
    set({ loginProps });
  });
  set({ isBackFilledLoginRes: false });
  forage<UserStoreInit['loginRes']>()
    .getItem('loginRes', (_, loginRes) => {
      set({ loginRes });
    })
    .finally(() => {
      set({ isBackFilledLoginRes: true });
    });
  forage<UserStoreInit['loginRes']>().getItem('loginRes', (_, loginRes) => {
    set({ loginRes });
  });

  const storageTheme = storageHelper<boolean>('isDark').getItem() ?? false;

  return {
    ...userStoreInit,
    isDark: storageTheme,
    setLocale: (locale) => set({ locale }),
    setSoundEnable: (soundEnable: UserStoreInit['soundEnable']) => {
      set({ soundEnable });
    },
    setIsDark: (isDark: UserStoreInit['isDark']) => {
      set({ isDark });
      storageHelper<boolean>('isDark').setItem(isDark);
    },

    setLoginProps: (loginProps) => {
      forage<UserStoreInit['loginProps']>().setItem('loginProps', loginProps, () => {
        set({ loginProps });
      });
    },
    setLoginRes: (loginRes) => {
      forage<UserStoreInit['loginRes']>().setItem('loginRes', loginRes, () => {
        set({ loginRes });
      });
    },
    setInfo: (info) => {
      forage<UserStoreInit['info']>().setItem('info', info, () => {
        set({ info });
      });
    },
    setIsLoading: (isLoading) => set({ isLoading }),
    setIsConnected: (isConnected) => set({ isConnected }),
    reset: () => set({ ...userStoreInit }),
  };
});

export { useUserStore };
export type { UserStoreInit, IUserStoreOptions };
