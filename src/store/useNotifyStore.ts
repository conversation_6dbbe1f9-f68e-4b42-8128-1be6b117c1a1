// libs
import { create } from 'zustand';

// components
import { type NotifyBaseOptions } from '@/components/NotifyProvider';

// utils
import { logWarn } from '@/utils';

type HintItemOptions = {
  id: number;
  title: string;
  des: string;
  createdAt: string;
  isReaded: boolean;
};

type NotifyStoreInit = {
  basicSuccessQue: Array<NotifyBaseOptions>;
  basicErrorQue: Array<NotifyBaseOptions>;
  hintQue: Array<HintItemOptions>;
};

type NotifyStoreOptions = NotifyStoreInit & {
  setBSQ: (basicSuccessQue: Array<NotifyBaseOptions>) => void;
  pushBSQ: (basicSuccessQue: Array<NotifyBaseOptions>) => void;
  shiftBSQ: (count?: number) => void;
  setBEQ: (basicErrorQue: Array<NotifyBaseOptions>) => void;
  pushBEQ: (basicSuccessQue: Array<NotifyBaseOptions>) => void;
  shiftBEQ: (count?: number) => void;
  setHQ: (hintQue: NotifyStoreInit['hintQue']) => void;
  pushHQ: (hintQue: NotifyStoreInit['hintQue']) => void;
  shiftHQ: (count?: number) => void;
  readHQ: (id: number) => void;
};

const init: NotifyStoreInit = {
  basicSuccessQue: [],
  basicErrorQue: [],
  hintQue: [],
};

const useNotifyStore = create<NotifyStoreOptions>((set) => {
  return {
    ...init,
    setBSQ: (basicSuccessQue) => {
      set({ basicSuccessQue });
    },
    pushBSQ: (newQue) => {
      set((states) => {
        const basicSuccessQue = states.basicSuccessQue.concat(newQue);
        return { ...states, basicSuccessQue };
      });
    },
    shiftBSQ: (count) => {
      const useCount = count || 1;
      set((states) => {
        const JsonQue = states.basicSuccessQue.map((screenData) => JSON.stringify(screenData));
        const setQue = Array.from(new Set(JsonQue));
        const getBSQ = (): Array<NotifyBaseOptions> => {
          try {
            const parseQue = setQue.map((screenData) => JSON.parse(screenData) as NotifyBaseOptions);
            return parseQue;
          } catch (error) {
            logWarn({ Title: 'Parse shift BSQ error', error });
            return [];
          }
        };
        const newBSQ = getBSQ();
        const basicSuccessQue = newBSQ.slice(useCount);
        return { ...states, basicSuccessQue };
      });
    },
    setBEQ: (basicErrorQue) => {
      set({ basicErrorQue });
    },
    pushBEQ: (newQue) => {
      set((states) => {
        const basicErrorQue = states.basicErrorQue.concat(newQue);
        return { ...states, basicErrorQue };
      });
    },
    shiftBEQ: (count) => {
      set((states) => {
        const useCount = count || 1;
        // 使用JSON比對重複物件，刪除時移除
        const JsonQue = states.basicErrorQue.map((screenData) => JSON.stringify(screenData));
        const setQue = Array.from(new Set(JsonQue));
        const getBEC = (): Array<NotifyBaseOptions> => {
          try {
            const parseQue = setQue.map((screenData) => JSON.parse(screenData) as NotifyBaseOptions);
            return parseQue;
          } catch (error) {
            logWarn({ Title: 'Parse shift BEQ error', error });
            return [];
          }
        };
        const newQue = getBEC();
        const basicErrorQue = newQue.slice(useCount);
        return { ...states, basicErrorQue };
      });
    },
    setHQ: (hintQue) => {
      set({ hintQue });
    },
    pushHQ: (newQue) => {
      set((states) => {
        const hintQue = newQue.concat(states.hintQue);
        const hintMap = new Map(hintQue.map((mapH) => [mapH.id, mapH]));
        const unique = Array.from(hintMap.values());
        return { ...states, hintQue: unique };
      });
    },
    shiftHQ: (count = 1) => {
      set((states) => {
        const hintQue = states.hintQue.slice(count);
        return { ...states, hintQue };
      });
    },
    readHQ: (id) => {
      set((states) => {
        const pureStates = { ...states };
        const item = pureStates.hintQue.find((findH) => findH.id === id);
        if (!item) return pureStates;
        item.isReaded = true;
        return { ...pureStates, hintQue: [...pureStates.hintQue] };
      });
    },
  };
});

export { useNotifyStore };
export type { NotifyBaseOptions, HintItemOptions };
