@import 'tailwindcss/base';
@import 'tailwindcss/utilities';
@import 'tailwindcss/components';

body {
  background-color: #bfbfbf;
}

.ant-table-content {
  scrollbar-width: thin !important;
  scrollbar-color: #eaeaea transparent !important;
}

.highlight-row {
  background-color: #fffbe6; 
  animation: highlight-fade 1.5s ease-in-out; 
}

.highlight-row:hover {
  background-color: #fff1b8; 
  box-shadow: 0 0 0 rgba(255, 193, 7, 0);

}

@keyframes highlight-fade {
  0% {
    background-color: #ffe58f; 
    box-shadow: 0 4px 10px rgba(255, 193, 7, 0.6);
  }
  50% {
    background-color: #fff7d1; 
    box-shadow: 0 2px 5px rgba(255, 193, 7, 0.4);
  }
  100% {
    background-color: #fffbe6; 
    box-shadow: 0 0 0 rgba(255, 193, 7, 0);
  }
}
/* Dark Mode */
.highlight-row-dark {
  background: linear-gradient(135deg, #30475e, #3a6073); 
  animation: highlight-fade-dark 1.5s ease-in-out;
  color: #e0f7fa; 
}

.highlight-row-dark:hover {
  background: linear-gradient(135deg, #3a6073, #395e69); 
  box-shadow: 0 0 3px rgba(64, 158, 255, 0.6); 
}

@keyframes highlight-fade-dark {
  0% {
    background: linear-gradient(135deg, #395e69, #30475e);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.8);
  }
  50% {
    background: linear-gradient(135deg, #3a6073, #3a6073);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.5);
  }
  100% {
    background: linear-gradient(135deg, #30475e, #3a6073);
    box-shadow: 0 0 0 rgba(64, 158, 255, 0);
  }
}

.merchant-blocked {
  background-color: rgba(255, 0, 0, 0.1); 
  border-left: 4px solid #ff4d4f;
  color: #a8071a;
  font-weight: bold;
  opacity: 0.8;
  transition: all 0.3s ease-in-out;
}

.merchant-blocked:hover {
  background-color: rgba(255, 0, 0, 0.2);
  opacity: 1; 
}

.merchant-blocked-dark {
  background-color: rgba(255, 77, 79, 0.15); 
  border-left: 4px solid #ff7875; 
  color: #ffa39e; 
  font-weight: bold; 
  opacity: 0.9;
  transition: all 0.3s ease-in-out; 
}

.merchant-blocked-dark:hover {
  background-color: rgba(255, 77, 79, 0.25); 
  opacity: 1;
}