export default {
  title: 'Notifications',
  serialNumber: 'Serial number',
  name: 'Name',
  orderUidLabel: 'Order UID',
  merchantOrderIdLabel: 'Merchant order ID',
  systemOrderIdLabel: 'System order ID',
  amountLabel: 'Amount',
  statusLabel: 'Status',
  createAtLabel: 'Created At',
  merchantLabel: 'Merchant',
  idLabel: 'ID',
  fromLabel: 'From',
  toLabel: 'To',
  actualAmountLabel: 'Actual amount',
  deposit: 'Deposit',
  withdrawal: 'Withdrawal',
  createdStatusMessage: 'was created and is pending processing.',
  blockchainBroadcastStatusMessage: 'was broadcasted to the blockchain.',
  blockchainConfirmedStatusMessage: 'was confirmed on the blockchain.',
  completedStatusMessage: 'was successfully completed.',
  canceledStatusMessage: 'was canceled.',
  blockchainFailedStatusMessage: 'failed due to a blockchain error.',
  callbackFailStatusMessage: 'failed to notify the merchant.',
  timeoutStatusMessage: 'timed out.',
  retryStatusMessage: 'is retying.',
  unknownStatusMessage: 'status is unknown.',
  pendingReviewStatusMessage: 'is pending admin review.',
  approvedStatusMessage: 'was approved and is awaiting execution.',
  inProgressStatusMessage: 'is currently in progress.',
  resourceInsufficientStatusMessage: 'failed due to insufficient resources.',
  otherErrorStatusMessage: 'encountered an unexpected error.',
  of: 'of',
};
