export default {
  undefined: 'Undefined',

  // AnomalyResolutionStatus
  pendingAnomalyResolutionStatus: 'Pending',
  completedAnomalyResolutionStatus: 'Completed',
  inProgressAnomalyResolutionStatus: 'Processing',
  failedAnomalyResolutionStatus: 'Failed',

  // TronWalletFunc
  collectionTronWalletFunc: 'Collection',
  stakeTronWalletFunc: 'Staking',
  merchantPaymentWalletTronWalletFunc: 'Merchant',
  supervisorWalletTronWalletFunc: 'Supervisor',
  energyLeaseWalletTronWalletFunc: 'Energy Lease',

  // TronWalletStatus
  availableTronWalletStatus: 'Available',
  transactionTronWalletStatus: 'Trading',
  cooldownTronWalletStatus: 'Cooldown',

  // ShiftName
  morningShiftName: 'Morning',
  afternoonShiftName: 'Afternoon',
  nightShiftName: 'Night',

  // ClientEnvironment
  formalClientEnvironment: 'Formal',
  testClientEnvironment: 'Test',

  // limitTimeInterval
  thirtyMinsLimitTimeInterval: '30mins',
  oneHLimitTimeInterval: '1 hour',
  twoHLimitTimeInterval: '2 hours',

  // TxStatus
  createdTxStatus: 'Created',
  broadcastedTxStatus: 'Broadcasted',
  confirmedTxStatus: 'Confirmed',
  completedTxStatus: 'Completed',
  canceledTxStatus: 'Canceled',
  timeoutTxStatus: 'Timeout',
  retryTxStatus: 'Retry',
  merchantCallbackFailedTxStatus: 'Callback failed',
  blockchainTransactionFailedTxStatus: 'Blockchain failed',
  waitingForApprovalTxStatus: 'Waiting',

  // MerchantTransactionStatus
  pendingReviewMerchantTransactionStatus: 'Pending',
  approvedMerchantTransactionStatus: 'Approved',
  inProgressMerchantTransactionStatus: 'In progress',
  blockchainBroadcastMerchantTransactionStatus: 'Blockchain Broadcasted',
  blockchainConfirmedMerchantTransactionStatus: 'Blockchain Confirmed',
  canceledMerchantTransactionStatus: 'Canceled',
  resourceInsufficientMerchantTransactionStatus: 'Insufficient resource',
  blockchainFailedMerchantTransactionStatus: 'Blockchain failed',
  otherErrorMerchantTransactionStatus: 'Other',

  // TronMasterStatus
  normalTronMasterStatus: 'Normal',
  depositOnlyTronMasterStatus: 'Deposit only',
  withdrawalOnlyTronMasterStatus: 'Withdrawal only',
  suspendedTronMasterStatus: 'Suspended',

  // LeaseOrderStatus
  pendingLeaseInOrderStatusEnum: 'Pending',
  transferringLeaseInOrderStatusEnum: 'Transferring',
  transferSuccessLeaseInOrderStatusEnum: 'Transfer success',
  energyReceivedLeaseInOrderStatusEnum: 'Energy received',
  transferFailedLeaseInOrderStatusEnum: 'Transfer failed',

  // LeaseOutOrderStatusEnum
  receivedLeaseOutOrderStatusEnum: 'Received',
  delegatingLeaseOutOrderStatusEnum: 'Delegating',
  delegateSuccessLeaseOutOrderStatusEnum: 'Delegate Success',
  delegateFailLeaseOutOrderStatusEnum: 'Delegate Fail',
  reclaimingLeaseOutOrderStatusEnum: 'Reclaiming',
  reclaimSuccessLeaseOutOrderStatusEnum: 'Reclaim Success',
  reclaimFailLeaseOutOrderStatusEnum: 'Reclaim Fail',
  invalidTrxAmountLeaseOutOrderStatusEnum: 'Invalid TRX Amount',
  insufficientEnergyLeaseOutOrderStatusEnum: 'Insufficient Energy',
};
