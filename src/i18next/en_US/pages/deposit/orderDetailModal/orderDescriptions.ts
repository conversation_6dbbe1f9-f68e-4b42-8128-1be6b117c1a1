export default {
  orderType: 'Order type',
  memberOrderCreation: 'Member order creation',
  uuppOrderNumber: 'UXM order number',
  serialNumber: 'Serial number',
  merchantOrderNumber: 'Merchant order number',
  createTime: 'Create time',
  verifiedTime: 'Verified time',
  state: 'State',
  resendCallback: 'Resend callback',
  merchantNumber: 'Merchant number',
  merchantName: 'Merchant name',
  applicant: 'Applicant',
  requireAmount: 'Require amount',
  currency: 'Currency',
  fee: 'Fee',
  protocol: 'Protocol',
  actualAmount: 'Actual amount',
  toAddress: 'Allocate wallet',
  publicChain: 'Public chain details',
  remark: 'Remark',
  order: 'Order',
  callback: 'Callback',
  approver: 'Approver',
  merchantOrderCreation: 'Merchant order creation',
};
