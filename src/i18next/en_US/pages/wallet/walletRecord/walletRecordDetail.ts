export default {
  orderType: 'Order type',
  orderTypeValue:
    'UXM transfer in/UXM transfer out/UXM concentration/Merchant transfer in/Merchant transfer out/In-store transfer/Anomaly transfer',
  orderNumber: 'UXM order number',
  serialNumber: 'Serial number',
  createTime: 'Create time',
  verifyTime: 'Verify time',
  status: 'Status',
  finish: 'Finish',
  merchantNumber: 'Merchant number',
  merchantName: 'Merchant name',
  applicant: 'Applicant',
  from: 'From',
  to: 'To',
  amount: 'Amount',
  currency: 'Crypto',
  fee: 'Fee',
  protocol: 'Protocol',
  actualAmount: 'actualAmount',
  spendMiningFee: 'Spend mining Fee',
  publicChainDetails: 'Public chain details',
  remark: 'Remark',
  log: 'Log',
  verifyButtonText: 'Verify',
  turnDownButtonText: 'Turn down',
};
