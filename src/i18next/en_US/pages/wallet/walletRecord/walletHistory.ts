export default {
  emptyRecordsMessage: 'No records to print',
  undefined: 'Undefined',
  // Wallet history type name
  transferIn: 'UXM transfer in',
  transferOut: 'UXM transfer out',
  concentration: 'UXM concentration',
  merchantTransfer: 'Merchant transfer',
  merchantTransferOut: 'Merchant transfer out',
  instoreTransfer: 'In-store transfer',
  anomalyTransfer: 'Anomaly transfer',
  // Wallet history status name
  waitingForApproval: 'Waiting for approval',
  created: 'Created',
  broadcasted: 'Broadcasted',
  confirmed: 'Confirmed',
  completed: 'Completed',
  canceled: 'Canceled',
  timeout: 'Timeout',
  retry: 'Retry',
  merchantCallbackFailed: 'Callback failed',
  blockchainTransactionFailed: 'Blockchain transaction failed',
  merchantTitleRow: 'Merchant',
  createTimeTitleRow: 'Create time',
  verifyTimeTitleRow: 'Verify time',
  orderNumberTitleRow: 'Order number',
  serialNumberTitleRow: 'Serial number',
  typeTitleRow: 'Type',
  cryptoTitleRow: 'Crypto',
  protocolTitleRow: 'Protocol',
  fromTitleRow: 'From',
  toTitleRow: 'To',
  amountTitleRow: 'Amount',
  feeTitleRow: 'Fee',
  realityAmountTitleRow: 'Reality amount',
  gasTitleRow: 'Gas',
  statusTitleRow: 'Status',
  sheetName: 'Wallet history',
  fileName: 'Wallet history',
};
