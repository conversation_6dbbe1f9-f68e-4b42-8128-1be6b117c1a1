export default {
  minAmountLabel: 'Minimum amount for a single transaction',
  minAmountErrorMessage: 'Please enter',
  coolingTimeLabel: 'Cooling time',
  coolingTimeErrorMessage: 'Please enter',
  energySavingModeLabel: 'Energy saving mode',
  saveModeEnabledTooltip: 'Enabled',
  saveModeDisabledTooltip: 'Disabled',
  thirdPartyEnergyLabel: 'Enable third-party energy leasing',
  loadingLabel: 'Loading',
  editButtonText: 'Edit',
  title: 'TRON',
  edit: 'Edit',
  settingTitle: 'TRON setting',
  submit: 'Submit',
  cancel: 'Cancel',
  toggleEnergyLeaseSuccess: 'Third-party energy leasing toggled successfully.',
  toggleSaveModeSuccess: 'Save mode toggled successfully.',
  statusLabel: 'Master status',
  setStatusSuccess: 'Master status set successfully.',
};
