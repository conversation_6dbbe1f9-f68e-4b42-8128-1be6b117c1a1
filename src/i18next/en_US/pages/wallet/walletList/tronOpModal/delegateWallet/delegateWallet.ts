export default {
  successNotificationDescription: 'Set up successfully',
  bandwidthQuota: 'Bandwidth quota',
  bandwidthLimit: 'Bandwidth limit',
  energyQuota: 'Energy quota',
  energyLimit: 'Energy limit',
  trxBalance: 'TRX balance',
  trxPending: 'TRX pending',
  operationCategoryLabel: 'Operation caterogy',
  operationCategoryErrorMessage: 'Please select the operation category',
  delegateOptionLabel: 'Delegate',
  undelegateOptionLabel: 'Undelegate',
  operationCategoryPlaceholder: 'Please select the operation category',
  resourceLabel: 'Resource',
  resourceErrorMessage: 'Please select a resource',
  energyOptionLabel: 'Energy',
  bandwidthOptionLabel: 'Bandwidth',
  resourcePlaceholder: 'Please select a resource',
  trxAmountLabel: 'TRX amount',
  trxAmountErrorMessage: 'Please enter',
  ownerAddressLabel: 'System wallet address',
  ownerAddressErrorMessage: 'Please enter',
  receiverAddressLabel: 'Delegated address',
  operationPasswordLabel: 'Operation password',
  operationPasswordErrorMessage: 'Please enter',
  submit: 'Submit',
};
