export default {
  merchantColumn: 'Merchant',
  merchantNumber: 'Number',
  merchantName: 'Name',
  typeColumn: 'Type',
  timeColumn: 'Time',
  createTime: 'Created at',
  confirmTime: 'Confirmed at',
  orderUidColumn: 'Order number',
  merchantOrderIdColumn: 'Merchant order number',
  transactionHashColumn: 'Hash',
  addressesColumn: 'Addresses',
  from: 'From',
  to: 'To',
  cryptoTypeColumn: 'Crypto',
  toColumn: 'To',
  amountColumn: 'Amount',
  requireAmount: 'Require',
  actualAmount: 'Actual',
  feeColumn: 'Fee',
  gasColumn: 'Consumed',
  statusColumn: 'Status',
  remarkColumn: 'Remark',
  retryModalTitle: 'Retry',
  systemOrderId: 'Serial number',
  transferAmount: 'Transfer Amount',
};
