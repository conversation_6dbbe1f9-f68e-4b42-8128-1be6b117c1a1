export default {
  merchantColumn: 'Merchant',
  merchantNumber: 'Merchant number',
  merchantName: 'Name',
  applicationNameColumn: 'Applicant',
  approverNameColumn: 'Approver',
  transactionHashColumn: 'Hash',
  addressesColumn: 'Addresses',
  from: 'From',
  to: 'To',
  timeColumn: 'Time',
  createTime: 'Created at',
  confirmTime: 'Approved at',
  gasColumn: 'Consumed',
  feeColumn: 'Fee',
  amountColumn: 'Amount',
  requireAmount: 'Require',
  actualAmount: 'Actual',
  cryptoTypeColumn: 'Crypto',
  toColumn: 'To',
  statusColumn: 'Status',
  remarkColumn: 'Remark',
  retryModalTitle: 'Retry',
  transactionTypeColumn: 'Type',
  transferIn: 'Transfer in',
  transferOut: 'Transfer out',
};
