export default {
  emptyRecordsMessage: 'No records to print',
  homeBreadcrumb: 'Overview',
  currentBreadcrumb: 'Anomaly deposit',
  cryptoLabel: 'Crypto',
  cryptoPlaceholder: 'Crypto',
  statusLabel: 'Status',
  statusPlaceholder: 'Status',
  filterLabel: 'Filter',
  filterPlaceholder: 'Wallet or hash',
  createButtonText: 'Create exception record',
  findPlaceholder: 'Finding a wallet or hash',
  cryptoTitleRow: 'Crypto',
  protocolTitleRow: 'Protocol',
  transferTimeTitleRow: 'Transfer time',
  fromTitleRow: 'From',
  toTitleRow: 'To',
  hashTitleRow: 'Hash',
  resolvedAtTitleRow: 'Resolved at',
  gasTitleRow: 'Gas',
  amountTitleRow: 'Amount',
  statusTitleRow: 'Status',
  sheetName: 'Anomaly deposit',
  fileName: 'Anomaly deposit',
};
