export default {
  deposit: 'Deposit',
  withdraw: 'Withdraw',
  transfer: 'Transfer',
  setting: 'Setting',
  amountLabel: 'Amount',
  amountErrorMessage: 'Please enter',
  submit: 'Same as above',
  trc20FeeLabel: 'USDT-TRC20 Fee',
  trc20FeeErrorMessage: 'Pleas enter',
  trc20FeePlaceholder: 'Please enter',
  erc20FeeLabel: 'USDT-ERC20 Fee',
  erc20FeeErrorMessage: 'Please enter',
  erc20FeePlaceholder: 'Please enter',
  verificationModeLabel: 'Verification mode',
  checkedChildren: 'Automatic',
  unCheckedChildren: 'Manual',
  autoOffsetLabel: 'Auto offset',
  autoOffsetErrorMessage: 'Please enter',
  paymentTimeLabel: 'Payment time',
  paymentTimeErrorMessage: 'Please enter',
  paymentTimePlaceHolder: 'Please select',
  limitTimeLabel: 'Limit time',
  limitTimeErrorMessage: 'Please enter',
  dailyLimitLabel: 'Daily limit',
  dailyLimitErrorMessage: 'Please enter',
  whitelist: 'Whitelist',
  walletAddressPlaceholder: 'Please enter your wallet address',
  addButtonText: 'Add',
};
