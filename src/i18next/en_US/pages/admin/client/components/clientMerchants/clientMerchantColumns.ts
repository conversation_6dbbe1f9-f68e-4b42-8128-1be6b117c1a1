export default {
  clientColumn: 'Client',
  testEnvironmentTooltip: 'Test environment',
  formalEnvironmentTooltip: 'Formal environment',
  merchantCountColumn: 'Number of merchants',
  merchantAccountCountColumn: 'Merchant accounts',
  accountNameColumn: 'Merchant name',
  accountName: 'Name',
  serialNumber: 'Merchant number',
  permissionColumn: 'Permissions',
  feeColumn: 'Fee',
  encryptionType: 'Crypto type',
  fixedFee: 'Member Withdrawal',
  percentageFee: 'Member Deposit',
  withdrawFee: 'Merchant transfer out',
  depositFee: 'Merchant transfer in',
  closeButtonText: 'Close',
  expandButtonText: 'Expand',
  costDetails: 'Cost details',
  masterInfoColumn: 'Merchant Settings',
  tolerance: 'Deposit tolerance',
  timeout: 'Deposit timeout',
  maxDeposit: 'Merchant deposit order limit',
  maxUser: 'Maximum number of merchant accounts',
  orderLimit: 'Order limits',
  deposit: 'Deposit pending',
  stateColumn: 'Payment gateway status',
  walletColumn: 'Balance',
  balance: 'Balance',
  freeze: 'Pending',
  ipAddressColumn: 'IP address',
  enabledColumn: 'Merchant state',
  active: 'Merchant enabled',
  blocked: 'Merchant disabled',
  ipWhitelistColumn: 'IP whitelist',
  enabled: 'Enabled',
  disabled: 'Disabled',
  actionColumn: 'Actions',
  qty: 'Qty',
  maxUserDeposit: 'Member deposit order limit',
};
