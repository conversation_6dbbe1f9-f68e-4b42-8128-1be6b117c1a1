export default {
  successNotificationDescription: '2FA setup successfully',
  title: 'Set up 2FA via Google Authenticator',
  step1Title: '1. Download Google Authenticator',
  android: 'Android',
  goToGoogleStore: 'Go to Google Store',
  ios: 'iOS',
  goToAppStore: 'Go to App Store',
  step2Title:
    '2. After opening Google Authenticator on your phone, click the + sign in the lower right corner to add a new account.',
  remarkLabel: 'Name (for notes)',
  remarkPlaceholder: 'Please enter',
  scanGoogleAuthenticatorMessage: 'Scan this with the Google Authenticator app',
  myKey: 'My key',
  step3Title: '3. Enter the verification code generated by Google Authenticator',
  firstCodeLabel: 'First verification code',
  firstCodeErrorMessage: 'Please enter',
  firstCodePlaceholder: 'Please enter',
  sendCodeLabel: 'Second verification code',
  sendCodeErrorMessage: 'Please enter',
  sendCodePlaceholder: 'Please enter',
  warningMessage:
    'To ensure the accuracy of verification when binding the verfication code, please wait and enter the verification code twice in a row.',
  submit: 'Binding',
};
