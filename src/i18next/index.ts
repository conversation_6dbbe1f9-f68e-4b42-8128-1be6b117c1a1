import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { storageHelper } from '@/utils';
import en_US from './en_US';
import zh_TW from './zh_TW';

const resources = {
  'en-US': en_US,
  'zh-TW': zh_TW,
};

i18n.use(initReactI18next).init({
  resources,
  lng: storageHelper<LocaleTypes>('locale').getItem() || 'zh-TW',
  fallbackLng: false,
  debug: false,
  interpolation: {
    escapeValue: false,
  },
});

export default { i18n, resources };
