export default {
  undefined: '未定義',

  // AnomalyResolutionStatus
  pendingAnomalyResolutionStatus: '未認領',
  completedAnomalyResolutionStatus: '已認領',
  inProgressAnomalyResolutionStatus: '待處理',
  failedAnomalyResolutionStatus: '已失敗',

  // TronWalletFunc
  collectionTronWalletFunc: '收款',
  stakeTronWalletFunc: '質押',
  merchantPaymentWalletTronWalletFunc: '商戶',
  supervisorWalletTronWalletFunc: '股東',
  energyLeaseWalletTronWalletFunc: '能量租賃',

  // TronWalletStatus
  availableTronWalletStatus: '可使用',
  transactionTronWalletStatus: '交易中',
  cooldownTronWalletStatus: '冷卻中',

  // ShiftName
  morningShiftName: '早班',
  afternoonShiftName: '中班',
  nightShiftName: '晚班',

  // ClientEnvironment
  formalClientEnvironment: '正式環境',
  testClientEnvironment: '測試環境',

  // limitTimeInterval
  thirtyMinsLimitTimeInterval: '30分鐘',
  oneHLimitTimeInterval: '一小時',
  twoHLimitTimeInterval: '兩小時',

  // TxStatus
  createdTxStatus: '等待付款',
  broadcastedTxStatus: '區塊鏈廣播',
  confirmedTxStatus: '區塊鏈確認',
  completedTxStatus: '完成',
  canceledTxStatus: '取消',
  timeoutTxStatus: '超時',
  retryTxStatus: '重試',
  merchantCallbackFailedTxStatus: '通知商戶失敗',
  blockchainTransactionFailedTxStatus: '區塊鏈失敗',
  waitingForApprovalTxStatus: '等待批准',

  // MerchantTransactionStatus
  pendingReviewMerchantTransactionStatus: '等待管理員審核',
  approvedMerchantTransactionStatus: '已批准，等待執行',
  inProgressMerchantTransactionStatus: '處理中',
  blockchainBroadcastMerchantTransactionStatus: '區塊鏈廣播',
  blockchainConfirmedMerchantTransactionStatus: '區塊鏈確認',
  canceledMerchantTransactionStatus: '已取消',
  resourceInsufficientMerchantTransactionStatus: '資源不足',
  blockchainFailedMerchantTransactionStatus: '區塊鏈失敗',
  otherErrorMerchantTransactionStatus: '其他錯誤',

  // TronMasterStatus
  normalTronMasterStatus: '正常',
  depositOnlyTronMasterStatus: '僅限入金',
  withdrawalOnlyTronMasterStatus: '僅限出金',
  suspendedTronMasterStatus: '關閉',

  // LeaseInOrderStatusEnum
  pendingLeaseInOrderStatusEnum: '待辦的',
  transferringLeaseInOrderStatusEnum: '轉讓',
  transferSuccessLeaseInOrderStatusEnum: '轉移成功',
  energyReceivedLeaseInOrderStatusEnum: '接收能量',
  transferFailedLeaseInOrderStatusEnum: '傳輸失敗',

  // LeaseOutOrderStatusEnum
  receivedLeaseOutOrderStatusEnum: '已接收',
  delegatingLeaseOutOrderStatusEnum: '委託中',
  delegateSuccessLeaseOutOrderStatusEnum: '委託成功',
  delegateFailLeaseOutOrderStatusEnum: '委託失敗',
  reclaimingLeaseOutOrderStatusEnum: '回收中',
  reclaimSuccessLeaseOutOrderStatusEnum: '回收成功',
  reclaimFailLeaseOutOrderStatusEnum: '回收失敗',
  invalidTrxAmountLeaseOutOrderStatusEnum: 'TRX 金額無效',
  insufficientEnergyLeaseOutOrderStatusEnum: '能源不足',
};
