export default {
  successNotificationDescription: '設定2FA 成功',
  title: '透過Google Authenticator 設定 2FA 驗證',
  step1Title: '1. 下載 Google Authenticator',
  android: '安卓',
  goToGoogleStore: '前往 Google Store',
  ios: 'iOS',
  goToAppStore: '前往 App Store',
  step2Title: '2. 使用手機開啟 Google Authenticator 後，點擊右下角 + 號新增帳戶',
  remarkLabel: '名稱 (備註用)',
  remarkPlaceholder: '請輸入',
  scanGoogleAuthenticatorMessage: '使用 Google Authenticator 應用程式掃描此內容',
  myKey: '我的金鑰',
  step3Title: '3. 輸入Google Authenticator 生成的驗證碼',
  firstCodeLabel: '第一驗證碼',
  firstCodeErrorMessage: '請輸入',
  firstCodePlaceholder: '請輸入',
  sendCodeLabel: '第二驗證碼',
  sendCodeErrorMessage: '請輸入',
  sendCodePlaceholder: '請輸入',
  warningMessage: '綁定驗證碼時為求驗證精確，請等待並輸入接連兩次刷新的驗證碼',
  submit: '綁定',
};
