export default {
  emptyRecordsMessage: '沒有可列印的記錄',
  undefined: '未定義',
  // Wallet history type name
  transferIn: 'UXM轉入',
  transferOut: 'UXM轉出',
  concentration: 'UXM集中',
  merchantTransfer: '商戶轉入',
  merchantTransferOut: '商戶轉出',
  instoreTransfer: '商戶內轉',
  anomalyTransfer: '異常轉入',
  // Wallet history status name
  waitingForApproval: '等待批准',
  created: '等待中',
  broadcasted: '區塊鏈廣播',
  confirmed: '區塊鏈確認',
  completed: '完成',
  canceled: '取消',
  timeout: '超時',
  retry: '重試',
  merchantCallbackFailed: '通知商戶失敗',
  blockchainTransactionFailed: '區塊鏈交易失敗',
  merchantTitleRow: '商戶',
  createTimeTitleRow: '建立時間',
  verifyTimeTitleRow: '審核時間',
  orderNumberTitleRow: '訂單號',
  serialNumberTitleRow: '流水號',
  typeTitleRow: '類型',
  cryptoTitleRow: '幣種',
  protocolTitleRow: '協議',
  fromTitleRow: '從',
  toTitleRow: '至',
  amountTitleRow: '金額',
  feeTitleRow: '手續費',
  realityAmountTitleRow: '實際金額',
  gasTitleRow: '礦工費',
  statusTitleRow: '狀態',
  sheetName: '錢包記錄',
  fileName: '錢包記錄',
};
