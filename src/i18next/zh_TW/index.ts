import {
  useLogin,
  useVfy2Fa,
  useBlockMerchant,
  useDeleteMerchant,
  useSetFeeDetail,
  useSetMasterDetail,
  useAddSupervisorWhitelist,
  useDeleteSupervisorWhitelist,
  useSupervisorWithdraw,
  useUpdateSupervisorWhitelist,
  useWhitelist,
} from './api';
import {
  btnTableFuncs,
  dateFields,
  dateRange,
  dateRangeFields,
  amountRange,
  createAnomalyRecordModal,
  gaVfy,
  gaVfyModal,
  opwVfy,
  opwVfyModal,
  txVfy,
  txVfyModal,
  txVfyPreSelectHash,
  printModal,
  tbApFilterDropdown,
  tagActivation,
  tagMerchantTransactionStatus,
  tagServerStatus,
  tagTxStatus,
  tagTxType,
  tagWalletTxType,
  searchMaster,
} from './components';
import { useTestMaster } from './hooks';
import { balanceIcon, notification, privateHeader, privateLayout } from './layouts';
import {
  abnormalDeposit,
  abDepColumns,
  abDepSummary,
  clientFeesReportModal,
  feeDisplay,
  calReportModal,
  reportCalModalColumns,
  clientRevenueReport,
  depositRevenue,
  depositRevenueColumns,
  outgoingRevenue,
  totalRevenue,
  addClientAddress,
  addMerchantAddress,
  removeClientPaymentAddress,
  removeMerchantPaymentAddress,
  clientWalletMenu,
  setClientModal,
  setMerchantModal,
  clientWalletColumns,
  merchantWalletMenu,
  addMerchantAdminModal,
  addMerchantCsModal,
  blockMerchantModal,
  autoOffset,
  basicFields,
  clientCreateModal,
  dailyLimit,
  limitTimes,
  clientMerchantColumns,
  clientMenu,
  merchantMenu,
  copyClientMerchantsModal,
  createClientMerchantsForm,
  createMerchantModal,
  deleteMerchantModal,
  feeDetail,
  feeDetailModal,
  addIpModal,
  ipWhitelistModalColumns,
  merchantIpWhitelistModal,
  useAddWhitelist,
  useDeleteWhitelist,
  useToggleWhitelist,
  useUpdateWhitelist,
  setCallbackModal,
  setFeeModal,
  setMasterModal,
  setMerchantFeeModal,
  setMerchantCallbackModal,
  client,
  staffAccListColumns,
  admin,
  login,
  stepGa,
  stepLogin,
  depositAnomalyMatchTable,
  orderDescriptions,
  orderDetail,
  orderDetailModal,
  deposit,
  depositColumns,
  depositSummary,
  createPlatformModal,
  energyTiersModal,
  leaseInOrders,
  leaseInOrdersColumns,
  leaseOutOrders,
  leaseOutOrdersColumns,
  leasePlatform,
  leasePlatformColumns,
  lease,
  customersTable,
  merchantTable,
  customerSummaryModal,
  reportCalModal,
  shiftList,
  shiftSummary,
  merchantSummary,
  shift,
  shiftListColumns,
  supervisorOrders,
  supervisorOrderDetail,
  supervisorOrderColumns,
  bindingGA,
  changeOpw,
  changePw,
  setOpwModal,
  user,
  walletMenu,
  walletOverview,
  walletTransfer,
  createWalletModal,
  detailModal,
  tronWalletDetail,
  addSupervisorModal,
  supervisorWhitelistModal,
  supervisorWhitelistModalColumns,
  supervisorWithdrawModal,
  delegateWallet,
  stakingWallet,
  tronOpModal,
  supervisorInModal,
  tronWalletList,
  walletList,
  walletListColumns,
  walletSummary,
  walletRecord,
  walletHistory,
  walletHistoryColumns,
  walletHistorySummary,
  walletRecordDetail,
  wrDetailModal,
  tronSetting,
  walletSetting,
  wallet,
  merchantWithdraw,
  merchantWithdrawColumns,
  operationPasswordModal,
  withdraw,
  withdrawColumns,
  withdrawDetail,
  withdrawDetailModal,
  withdrawSummary,
} from './pages';
import options from './utils/options';

export default {
  useLogin,
  useVfy2Fa,
  useBlockMerchant,
  useDeleteMerchant,
  useSetFeeDetail,
  useSetMasterDetail,
  useAddSupervisorWhitelist,
  useDeleteSupervisorWhitelist,
  useSupervisorWithdraw,
  useUpdateSupervisorWhitelist,
  useWhitelist,

  btnTableFuncs,
  dateFields,
  dateRange,
  dateRangeFields,
  amountRange,
  createAnomalyRecordModal,
  gaVfy,
  gaVfyModal,
  opwVfy,
  opwVfyModal,
  txVfy,
  txVfyModal,
  txVfyPreSelectHash,
  printModal,
  tbApFilterDropdown,
  tagActivation,
  tagMerchantTransactionStatus,
  tagServerStatus,
  tagTxStatus,
  tagTxType,
  tagWalletTxType,
  searchMaster,

  useTestMaster,

  balanceIcon,
  notification,
  privateHeader,
  privateLayout,

  abnormalDeposit,
  abDepColumns,
  abDepSummary,

  clientFeesReportModal,
  feeDisplay,
  calReportModal,
  reportCalModalColumns,
  clientRevenueReport,
  depositRevenue,
  depositRevenueColumns,
  outgoingRevenue,
  totalRevenue,
  addClientAddress,
  addMerchantAddress,
  removeClientPaymentAddress,
  removeMerchantPaymentAddress,
  clientWalletMenu,
  setClientModal,
  setMerchantModal,
  clientWalletColumns,
  merchantWalletMenu,
  addMerchantAdminModal,
  addMerchantCsModal,
  blockMerchantModal,
  autoOffset,
  basicFields,
  clientCreateModal,
  dailyLimit,
  limitTimes,
  clientMerchantColumns,
  clientMenu,
  merchantMenu,
  copyClientMerchantsModal,
  createClientMerchantsForm,
  createMerchantModal,
  deleteMerchantModal,
  feeDetail,
  feeDetailModal,
  addIpModal,
  ipWhitelistModalColumns,
  merchantIpWhitelistModal,
  useAddWhitelist,
  useDeleteWhitelist,
  useToggleWhitelist,
  useUpdateWhitelist,
  setCallbackModal,
  setFeeModal,
  setMasterModal,
  setMerchantFeeModal,
  setMerchantCallbackModal,
  client,
  staffAccListColumns,
  admin,

  login,
  stepGa,
  stepLogin,

  depositAnomalyMatchTable,
  orderDescriptions,
  orderDetail,
  orderDetailModal,
  deposit,
  depositColumns,
  depositSummary,

  createPlatformModal,
  energyTiersModal,
  leaseInOrders,
  leaseInOrdersColumns,
  leaseOutOrders,
  leaseOutOrdersColumns,
  leasePlatform,
  leasePlatformColumns,
  lease,

  customersTable,
  merchantTable,
  customerSummaryModal,

  reportCalModal,
  shiftList,
  shiftSummary,
  merchantSummary,
  shift,
  shiftListColumns,

  supervisorOrders,
  supervisorOrderDetail,
  supervisorOrderColumns,

  bindingGA,
  changeOpw,
  changePw,
  setOpwModal,
  user,

  walletMenu,
  walletOverview,
  walletTransfer,
  createWalletModal,
  detailModal,
  tronWalletDetail,
  addSupervisorModal,
  supervisorWhitelistModal,
  supervisorWhitelistModalColumns,
  supervisorWithdrawModal,
  delegateWallet,
  stakingWallet,
  tronOpModal,
  supervisorInModal,
  tronWalletList,
  walletList,
  walletListColumns,
  walletSummary,
  walletRecord,
  walletHistory,
  walletHistoryColumns,
  walletHistorySummary,
  walletRecordDetail,
  wrDetailModal,
  tronSetting,
  walletSetting,
  wallet,

  merchantWithdraw,
  merchantWithdrawColumns,
  operationPasswordModal,
  withdraw,
  withdrawColumns,
  withdrawDetail,
  withdrawDetailModal,
  withdrawSummary,
  options,
};
