// hooks
import { useTestQuery, UseTestQueryProps } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

// api
import { TronWalletProps, TronWalletRes } from './useTronWallet';

type AllTronWalletsRes = TronWalletRes;
type AllTronWalletsProps = TronWalletProps;
export type Other = {};

const useAllTronWallets = (useProps: UseTestQueryProps<Other, AllTronWalletsProps, AllTronWalletsRes>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<AllTronWalletsRes, AllTronWalletsProps>({
    ...config,
    queryKey: queryKeys.query.allTronWallets(params),
    qf: () => {
      const request = axiosRoot.get('/V1.0/Tron/wallet', { params }).then(({ data }) => data);

      return request;
    },
    gcTime: 1000 * 60 * 5,
    staleTime: 1000 * 5,
  });

  return { ...testQuery };
};

export { useAllTronWallets };
export type { AllTronWalletsRes, AllTronWalletsProps };
