// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

interface TronWalletDetailRes {
  // Total amount of TRX in the wallet, including available and frozen balances.
  totalTrx: number;

  // TRX balance details.
  trx: {
    // Available TRX balance.
    balance: number;
    // TRX amount in unconfirmed transactions.
    pending: number;
  };

  // USDT (Tether) balance details.
  usdt: {
    // Total USDT balance in the wallet.
    balance: number;
    // Available USDT that can be used.
    available: number;
  };

  // Maximum resources that can be delegated by the wallet.
  delegatedMaxSize: {
    // Maximum bandwidth that can be delegated.
    bandwidth: number;
    // Maximum energy that can be delegated.
    energy: number;
  };

  // Resource information for the wallet.
  resource: {
    // Remaining free bandwidth.
    remainingFreeNet: number;
    // Total free bandwidth limit.
    freeNetLimit: number;
    // Total bandwidth limit, including frozen and delegated bandwidth.
    netLimit: number;
    // Remaining frozen bandwidth.
    remainingFrozenNet: number;
    // Remaining energy for contract execution.
    remainingEnergy: number;
    // Total energy limit available.
    energyLimit: number;
  };

  // Details about staking (freezing) and unstaking (unfreezing) TRX.
  staking: {
    // Total frozen TRX for staking or resource allocation.
    frozen: number;
    // TRX scheduled for unfreezing.
    unfrozen: number;
    // Details of frozen TRX allocations.
    frozenDetails: Array<string>;
    // Details of TRX scheduled to be unfrozen.
    unfrozenDetails: Array<string>;
  };

  // Resources delegated by or to the wallet.
  delegateResource: {
    // Bandwidth delegated by this wallet to others.
    bandwidth: number;
    // Energy delegated by this wallet to others.
    energy: number;
  };

  // Delegation relationships.
  delegateIndex: {
    // List of wallets that delegated resources to this wallet.
    from: Array<string>;
    // List of wallets to which this wallet delegated resources.
    to: Array<string>;
  };
}

type TronWalletDetailProps = {
  address: string | undefined;
};

type Other = {};

const useTronWalletsDetail = (useProps: UseTestQueryProps<Other, TronWalletDetailProps, TronWalletDetailRes>) => {
  // props
  const { params, ...config } = useProps;

  const walletDetailMutation = useTestQuery<TronWalletDetailRes, TronWalletDetailProps>({
    ...config,
    queryKey: ['wallet', 'detail', params?.address],
    qf: () => {
      const request = axiosRoot.get(`/V1.0/tron/wallet/${params?.address}`).then(({ data }) => data);
      return request;
    },
    staleTime: 1000 * 60 * 20,
  });

  return walletDetailMutation;
};

export { useTronWalletsDetail };
export type { TronWalletDetailRes, TronWalletDetailProps };
