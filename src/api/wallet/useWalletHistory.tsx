// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';
import { IWhRow } from '@/pages/wallet/WalletRecord/useWhColumns';

// utils
import { CryptoEnum, TxStatusNum } from '@/utils';
import axiosRoot from '@/utils/axiosRoot';

type WalletHistoryRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<IWalletRecordItem>;
};
type WalletHistoryProps = {
  address: string | undefined;
};
type Other = {};

// Order item
interface IWalletRecordItem extends IWhRow {}

const mockData = [
  {
    key: 0,
    merchantNum: '-',
    merchantName: '-',
    createTime: '-',
    verifyTime: '2024-08-13 00:00:00',
    orderNumber: '-',
    serialNumber: 'Transfer20240813000000',
    type: 0,
    crypto: CryptoEnum.TRC20_USDT,
    protocol: 'TRC20',
    fromAddress: 'Te28fe8weff69w8e4f651fe4fw368e4f',
    toAddress: 'T789Aaswjyu98cvh9814ty6d584cv8j498j',
    applyAmount: 80002,
    fee: 0,
    realityAmount: 80000,
    gasFee: 0,
    status: TxStatusNum.BlockchainTransactionFailed,
  },
  {
    key: 1,
    merchantNum: '-',
    merchantName: '-',
    createTime: '2024-08-11 00:00:00',
    verifyTime: '2024-08-11 00:00:00',
    orderNumber: '1089213',
    serialNumber: 'Transfer20240812000000',
    type: 1,
    crypto: CryptoEnum.TRC20_USDT,
    protocol: 'ERC20',
    fromAddress: '0x1w8efEF484s6d84j9y8ui4321659w8454664E',
    toAddress: '0x148wdqw8r84fw8efDFQWO4869484IJKLX486CUfW',
    applyAmount: 30,
    fee: 0,
    realityAmount: 30,
    gasFee: 0.00514,
    status: TxStatusNum.Broadcasted,
  },
  {
    key: 2,
    merchantNum: '-',
    merchantName: '-',
    createTime: '2024-08-11 00:00:00',
    verifyTime: '2024-08-11 00:00:00',
    orderNumber: '-',
    serialNumber: 'Transfer20240811000000',
    type: 2,
    crypto: CryptoEnum.TRC20_USDT,
    protocol: 'ERC20',
    fromAddress: '0x148wdqw8r84fw8efDFQWO4869484IJKLX486CUfW',
    toAddress: '0x879123w8r84IJKLX486CUf84fw8eWfDFQWO48694',
    applyAmount: 498,
    fee: 0,
    realityAmount: 498,
    gasFee: 0.00915,
    status: TxStatusNum.Canceled,
  },
  {
    key: 3,
    merchantNum: '2088',
    merchantName: '商戶名',
    createTime: '-',
    verifyTime: '2024-08-11 00:00:00',
    orderNumber: '-',
    serialNumber: 'Transfer20240811000000',
    type: 3,
    crypto: CryptoEnum.TRC20_USDT,
    protocol: 'ERC20',
    fromAddress: '0x78621efDFQJKLX486CUfWWO4869484IJKLX3sdoijI',
    toAddress: '0x9484IJKLX3486CUfWWO486oijIsd78621efDFQJKLX',
    applyAmount: 10022,
    fee: 0,
    realityAmount: 10000,
    gasFee: 0,
    status: TxStatusNum.Completed,
  },
  {
    key: 4,
    merchantNum: '2088',
    merchantName: '商戶名',
    createTime: '2024-08-11 00:00:00',
    verifyTime: '2024-08-11 00:00:00',
    orderNumber: '1089212',
    serialNumber: 'Transfer20240811000000',
    type: 4,
    crypto: CryptoEnum.TRC20_USDT,
    protocol: 'ERC20',
    fromAddress: '0x879123w8r84IJKLX486CUf84fw8eWfDFQWO48694',
    toAddress: '0x879123w8r84IJKLX486CUf84fw8eWfDFQWO48694',
    applyAmount: 498,
    fee: 2,
    realityAmount: 498,
    gasFee: 0.00757,
    status: TxStatusNum.Confirmed,
  },
  {
    key: 5,
    merchantNum: '2088',
    merchantName: '商戶名',
    createTime: '2024-08-11 00:00:00',
    verifyTime: '2024-08-11 00:00:00',
    orderNumber: '1089211',
    serialNumber: 'Transfer20240811000000',
    type: 5,
    crypto: CryptoEnum.TRC20_USDT,
    protocol: 'ERC20',
    fromAddress: '0x78621efDFQJKLX486CUfWWO4869484IJKLX3sdoijI',
    toAddress: '0x9484IJKLX3486CUfWWO486oijIsd78621efDFQJKLX',
    applyAmount: 10022,
    fee: 2,
    realityAmount: 10000,
    gasFee: 0,
    status: TxStatusNum.Created,
  },
  {
    key: 6,
    merchantNum: '-',
    merchantName: '-',
    createTime: '-',
    verifyTime: '2024-08-11 00:00:00',
    orderNumber: '-',
    serialNumber: 'Investigate20240811000000',
    type: 6,
    crypto: CryptoEnum.TRC20_USDT,
    protocol: 'TRC20',
    fromAddress: 'T486CUfWWO4869484IJKLX3sdoijI',
    toAddress: 'TO486oijIsd786WO4869421efDFQJKLX',
    applyAmount: 1000,
    fee: 0,
    realityAmount: 1000,
    gasFee: 0,
    status: TxStatusNum.MerchantCallbackFailed,
  },
  {
    key: 7,
    merchantNum: '-',
    merchantName: '-',
    createTime: '-',
    verifyTime: '2024-08-11 00:00:00',
    orderNumber: '-',
    serialNumber: 'Investigate20240811000000',
    type: 6,
    crypto: CryptoEnum.TRC20_USDT,
    protocol: 'TRC20',
    fromAddress: 'T486CUfWWO4869484IJKLX3sdoijI',
    toAddress: 'TO486oijIsd786WO4869421efDFQJKLX',
    applyAmount: 1000,
    fee: 0,
    realityAmount: 1000,
    gasFee: 0,
    status: TxStatusNum.Retry,
  },
];

const useWalletHistory = (useProps: UseTestQueryProps<Other, WalletHistoryProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  const testQuery = useTestQuery<WalletHistoryRes, WalletHistoryProps>({
    ...config,
    queryKey: ['wallet', 'history', params?.address],
    qf: () => {
      const request = axiosRoot.get('/V1.0/wallet/history', { params }).then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
    onTest: () => {
      const res: WalletHistoryRes = {
        currentPage: 0,
        totalPages: 0,
        pageSize: 0,
        totalCount: 0,
        items: mockData,
      };

      return Promise.resolve(res);
    },
  });

  return testQuery;
};

export { useWalletHistory };
export type { WalletHistoryRes, WalletHistoryProps, IWalletRecordItem };
