// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type SupervisorWithdrawRes = {};
type SupervisorWithdrawProps = {
  whiteListId: number;
  amount: number;
  remark: string;
  operationPassword: string;
  twoFactorCode: string;
};

type Other = {};

const useSupervisorWithdraw = (
  useProps: UseTestMutationProps<SupervisorWithdrawRes, SupervisorWithdrawProps, Other>,
) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useSupervisorwithdraw');

  const testMutation = useTestMutation<SupervisorWithdrawRes, SupervisorWithdrawProps>({
    ...config,
    mutationFn: ({ whiteListId, amount, remark, operationPassword, twoFactorCode }) => {
      const request = axiosRoot
        .post(`/V1.0/supervisor-order/tron/usdt`, {
          whiteListId,
          amount,
          remark,
          operationPassword,
          twoFactorCode,
        })
        .then(({ data }) => data);
      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useSupervisorWithdraw };
export type { SupervisorWithdrawRes, SupervisorWithdrawProps };
