// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type TronStakeRes = {};
type TronStakeProps = {
  operate: 'stake' | 'unStake';
  source: 'energy' | 'bandwidth';
  trxAmount: number;
  address: string;
  operationPassword: string;
};
type Other = {};

const useTronStake = (useProps: UseTestMutationProps<TronStakeRes, TronStakeProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<TronStakeRes, TronStakeProps>({
    ...config,
    mutationFn: (props) => {
      const { operate, source, ...otherProps } = props;
      if (operate === 'stake' && source === 'bandwidth') {
        const request = axiosRoot.post('/V1.0/tron/stake/bandwidth', otherProps).then(({ data }) => data);

        return request;
      }

      if (operate === 'stake' && source === 'energy') {
        const request = axiosRoot.post('/V1.0/tron/stake/energy', otherProps).then(({ data }) => data);

        return request;
      }

      if (operate === 'unStake' && source === 'bandwidth') {
        const request = axiosRoot.post('/V1.0/tron/unstake/bandwidth', otherProps).then(({ data }) => data);

        return request;
      }

      if (operate === 'unStake' && source === 'energy') {
        const request = axiosRoot.post('/V1.0/tron/unStake/energy', otherProps).then(({ data }) => data);

        return request;
      }
      const request = Promise.resolve({});

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.getAllTronWalletsKeys().forEach((eachKeys) => {
        queryClient.invalidateQueries({ queryKey: eachKeys });
      });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });
  return testMutation;
};

export { useTronStake };
export type { TronStakeRes, TronStakeProps };
