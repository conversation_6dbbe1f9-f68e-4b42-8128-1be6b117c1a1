// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type EstimateEnergyRes = {
  energyPrice: 0;
  error: 'REVERT opcode executed';
  estimatedEnergy: 0;
  result: false;
  trxCost: 0;
};
type EstimateEnergyProps = {
  from: string;
  to: string;
  amount: number;
};
type Other = {};

const useEstimateEnergy = (useProps: UseTestMutationProps<EstimateEnergyRes, EstimateEnergyProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<EstimateEnergyRes, EstimateEnergyProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/tron/estimateEnergy', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useEstimateEnergy };
export type { EstimateEnergyRes, EstimateEnergyProps };
