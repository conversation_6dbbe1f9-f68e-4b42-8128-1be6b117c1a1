// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type SetMasterStatusRes = {};
type SetMasterStatusProps = {
  newStatus: number;
  operationPassword: string;
};
type Other = {};

const useSetMasterStatus = (useProps: UseTestMutationProps<SetMasterStatusRes, SetMasterStatusProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<SetMasterStatusRes, SetMasterStatusProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.put('/V1.0/tron/master/status', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      queryClient.invalidateQueries({ queryKey: queryKeys.query.tronMaster });
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useSetMasterStatus };
export type { SetMasterStatusRes, SetMasterStatusProps };
