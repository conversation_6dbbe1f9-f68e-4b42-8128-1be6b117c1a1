// libs
import { useCallback, useEffect } from 'react';

// hooks
import { useTestMutation, UseTestQueryProps } from '@/hooks';

// utils
import { TronWalletFuncEnum, TronWalletStatusEnum } from '@/utils';
import axiosRoot from '@/utils/axiosRoot';

type TronWalletOptions = {
  id: number; // Wallet ID in the system.
  address: string; // Tron wallet address.
  trxBalance: number; // Current TRX balance.
  trxPending: number; // Pending TRX balance.
  usdtBalance: number; // Current USDT balance.
  usdtPending: number; // Pending USDT balance.
  bandwidth: number; // Available bandwidth points.
  bandwidthLimit: number; // Maximum bandwidth limit.
  energy: number; // Available energy points.
  energyLimit: number; // Maximum energy limit.
  status: TronWalletStatusEnum; // Wallet status (e.g., active or inactive).
  function: TronWalletFuncEnum; // Wallet function type.
  cooldownUntil: string | null; // Cooldown expiration time (ISO 8601) or null.
  createdAt: string; // Wallet creation time (ISO 8601).
  updatedAt: string; // Last update time (ISO 8601).
};

type TronWalletRes = {
  currentPage: number;
  items: Array<TronWalletOptions>;
  pageSize: number;
  totalCount: number;
  totalPages: number;
};
type TronWalletProps = {
  PageSize?: number | null;
  PageNumber?: number | null;
  OrderBy?: 'TrxBalance' | 'UsdtBalance' | 'Bandwidth' | 'Energy' | 'Id' | null;
  OrderByDescending?: boolean | null;
  Address?: string | null;
  Status?: TronWalletStatusEnum | null; // enum
  Function?: TronWalletFuncEnum | null;
  AvailableMerchantPaymentAddress?: boolean | null;
};
type Other = {
  isActive: boolean | undefined;
  pageSize: number;
  currentPage: number;
} & Omit<TronWalletProps, 'PageSize' | 'PageNumber'>;

const useTronWallet = (useProps: UseTestQueryProps<Other, TronWalletProps, TronWalletRes>) => {
  // props
  const { isActive, pageSize, currentPage, OrderBy, OrderByDescending, Address, Status, Function, ...config } =
    useProps;

  const testMutation = useTestMutation<TronWalletRes, TronWalletProps>({
    ...config,
    mutationFn: (params) => {
      const request = axiosRoot.get('/V1.0/Tron/wallet', { params }).then(({ data }) => data);

      return request;
    },
  });
  const { mutate } = testMutation;

  //   handlers
  const handleSearch = useCallback(() => {
    mutate({
      PageNumber: currentPage,
      PageSize: pageSize,
      OrderBy,
      OrderByDescending,
      Address,
      Status,
      Function,
    });
  }, [Address, Function, OrderBy, OrderByDescending, Status, currentPage, mutate, pageSize]);

  //   init
  useEffect(() => {
    if (!isActive) return;
    handleSearch();
  }, [handleSearch, isActive]);

  return { ...testMutation, handleSearch };
};

export { useTronWallet };
export type { TronWalletOptions, TronWalletRes, TronWalletProps };
