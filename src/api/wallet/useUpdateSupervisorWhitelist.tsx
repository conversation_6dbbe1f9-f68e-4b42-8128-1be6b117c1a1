// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type UpdateSupervisorWhitelistRes = {};
type UpdateSupervisorWhitelistProps = {
  id: number;
  name: string;
  address: string;
  description: string;
};
type Other = {};

const useUpdateSupervisorWhitelist = (
  useProps: UseTestMutationProps<UpdateSupervisorWhitelistRes, UpdateSupervisorWhitelistProps, Other>,
) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useUpdateSupervisorWhitelist');

  const testMutation = useTestMutation<UpdateSupervisorWhitelistRes, UpdateSupervisorWhitelistProps>({
    ...config,
    mutationFn: ({ id, name, description }) => {
      const request = axiosRoot
        .put(`/V1.0/supervisor/whiteList/${id}`, {
          name,
          description,
        })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useUpdateSupervisorWhitelist };
export type { UpdateSupervisorWhitelistRes, UpdateSupervisorWhitelistProps };
