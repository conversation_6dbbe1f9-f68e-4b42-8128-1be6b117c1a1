// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type SyncWalletRes = {};
type SyncWalletProps = {
  address: string | undefined | null;
};
type Other = {};

const useSyncWallet = (useProps: UseTestMutationProps<SyncWalletRes, SyncWalletProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<SyncWalletRes, SyncWalletProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post(`/V1.0/tron/wallet/sync/${props.address}`, props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useSyncWallet };
export type { SyncWalletRes, SyncWalletProps };
