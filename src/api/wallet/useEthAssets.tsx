import { UseTestQueryProps, useTestQuery } from '@/hooks';
import { assetsItemFactory } from '@/utils/myFactory';

type EthAssetsRes = {
  ethBalance: number;
  ethTotal: number;
  ethPending: number;
  assets: Array<IAssetOptions>;
};
type EthAssetsProps = {};
type Other = {};

interface IAssetOptions {
  assetName: string;
  total: number;
  pending: number;
  balance: number;
}

const useEthAssets = (useProps: UseTestQueryProps<Other, EthAssetsProps>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<EthAssetsRes, EthAssetsProps>({
    ...config,
    queryKey: ['eth', 'assets', ...Object.values(params || {})],
    qf: () => {
      const request = Promise.resolve(undefined as unknown as EthAssetsRes);
      return request;
    },
    staleTime: 1000 * 60 * 10,
    skipLogger: true,
    onTest: () => {
      const assets = ['USDT', 'USDC'].map((mapA) => assetsItemFactory(mapA));
      const { total: ethTotal, balance: ethBalance, pending: ethPending } = assetsItemFactory('ETH');
      return Promise.resolve({ assets, ethTotal, ethBalance, ethPending });
    },
  });

  return testQuery;
};

export { useEthAssets };
export type { EthAssetsRes, EthAssetsProps, IAssetOptions };
