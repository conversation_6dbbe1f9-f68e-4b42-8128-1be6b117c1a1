// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type WalletAssetsRes = {
  // The total available TRX balance in the system.
  trxBalance: number;

  // The total pending TRX amount (e.g., unconfirmed transactions).
  trxPending: number;

  // The total USDT balance available in the system.
  usdtBalance: number;
};
type WalletAssetsProps = {};
type Other = {};

const useWalletAssets = (useProps: UseTestQueryProps<Other, WalletAssetsProps>) => {
  // props
  const { ...config } = useProps;

  const testQuery = useTestQuery<WalletAssetsRes, WalletAssetsProps>({
    ...config,
    queryKey: ['wallet', 'assets'],
    qf: (params) => {
      const request = axiosRoot.get('/V1.0/tron/assets', { params }).then(({ data }) => data);

      return request;
    },
    staleTime: 1000 * 60 * 10,
    skipLogger: true,
  });

  return testQuery;
};

export { useWalletAssets };
export type { WalletAssetsRes, WalletAssetsProps };
