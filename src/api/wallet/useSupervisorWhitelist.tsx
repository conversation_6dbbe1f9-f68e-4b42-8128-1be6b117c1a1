// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type SupervisorWhitelistRes = Array<{
  id: number;
  address: string;
  name: string;
  description: string;
}>;

type Other = {};

const useFetchSupervisorWhitelist = (useProps: UseTestQueryProps<{}, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  const testQuery = useTestQuery<SupervisorWhitelistRes, {}>({
    ...config,
    queryKey: ['supervisorWhitelist'],
    qf: () => {
      const request = axiosRoot.get(`/V1.0/supervisor/whiteList`).then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useFetchSupervisorWhitelist };
export type { SupervisorWhitelistRes };
