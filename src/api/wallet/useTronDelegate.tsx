// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type TronDelegateRes = {};
type TronDelegateProps = {
  operate: 'delegate' | 'unDelegate';
  ownerAddress: string;
  receiverAddress: string;
  resource: 'ENERGY' | 'BANDWIDTH';
  trxAmount: number;
  operationPassword: string;
};
type Other = {};

const useTronDelegate = (useProps: UseTestMutationProps<TronDelegateRes, TronDelegateProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<TronDelegateRes, TronDelegateProps>({
    ...config,
    mutationFn: (props) => {
      const { operate, ...otherProps } = props;
      if (operate === 'delegate') {
        const request = axiosRoot.post('/V1.0/tron/delegate', otherProps).then(({ data }) => data);

        return request;
      }

      if (operate === 'unDelegate') {
        const request = axiosRoot.post('/V1.0/tron/unDelegate', otherProps).then(({ data }) => data);

        return request;
      }

      const request = Promise.resolve({});

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.getAllTronWalletsKeys().forEach((eachKeys) => {
        queryClient.invalidateQueries({ queryKey: eachKeys });
      });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useTronDelegate };
export type { TronDelegateRes, TronDelegateProps };
