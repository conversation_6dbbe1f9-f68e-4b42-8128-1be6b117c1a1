// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type ToggleSaveModeRes = {};
type ToggleSaveModeProps = {
  operationPassword: string;
};
type Other = {};

const useToggleSaveMode = (useProps: UseTestMutationProps<ToggleSaveModeRes, ToggleSaveModeProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<ToggleSaveModeRes, ToggleSaveModeProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.put('/V1.0/tron/master/save-mode/toggle', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      queryClient.invalidateQueries({ queryKey: queryKeys.query.tronMaster });
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useToggleSaveMode };
export type { ToggleSaveModeRes, ToggleSaveModeProps };
