// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type SetTronMasterRes = {};
type SetTronMasterProps = {
  minUsdtBalanceForAggregation: number;
  walletColingInMinute: number;
  operationPassword: string;
};
type Other = {};

const useSetTronMaster = (useProps: UseTestMutationProps<SetTronMasterRes, SetTronMasterProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<SetTronMasterRes, SetTronMasterProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/tron/master', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      queryClient.invalidateQueries({ queryKey: queryKeys.query.tronMaster });
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useSetTronMaster };
export type { SetTronMasterRes, SetTronMasterProps };
