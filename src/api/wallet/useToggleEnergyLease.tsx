// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type ToggleEnergyLeaseRes = {};
type ToggleEnergyLeaseProps = {
  operationPassword: string;
};
type Other = {};

const useToggleEnergyLease = (useProps: UseTestMutationProps<ToggleEnergyLeaseRes, ToggleEnergyLeaseProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<ToggleEnergyLeaseRes, ToggleEnergyLeaseProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.put('/V1.0/tron/master/energy-lease/toggle', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      queryClient.invalidateQueries({ queryKey: queryKeys.query.tronMaster });
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useToggleEnergyLease };
export type { ToggleEnergyLeaseRes, ToggleEnergyLeaseProps };
