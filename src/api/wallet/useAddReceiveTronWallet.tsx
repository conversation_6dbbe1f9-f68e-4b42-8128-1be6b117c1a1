// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type AddReceiveTronWalletRes = {};
type AddReceiveTronWalletProps = {
  merchantId: number;
  address: string;
  operationPassword: string;
};
type Other = {};

const useAddReceiveTronWallet = (
  useProps: UseTestMutationProps<AddReceiveTronWalletRes, AddReceiveTronWalletProps, Other>,
) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<AddReceiveTronWalletRes, AddReceiveTronWalletProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant/trc20/receiveAddress', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.getAllMerchantListKeys().forEach((eachKeys) => {
        queryClient.invalidateQueries({ queryKey: eachKeys });
      });
      queryKeys.query.getAllTronWalletsKeys().forEach((eachKeys) => {
        queryClient.invalidateQueries({ queryKey: eachKeys });
      });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useAddReceiveTronWallet };
export type { AddReceiveTronWalletRes, AddReceiveTronWalletProps };
