// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import { TronWalletFuncEnum } from '@/utils';
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type CreateWalletRes = {
  address: string;
};
type CreateWalletsProps = {
  walletFunction: TronWalletFuncEnum;
  operationPassword: string;
};
type Other = {};
const useCreateWallets = (useProps: UseTestMutationProps<CreateWalletRes, CreateWalletsProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMuation = useTestMutation<CreateWalletRes, CreateWalletsProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/tron/wallet/create', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.getAllTronWalletsKeys().forEach((eachKey) => {
        queryClient.invalidateQueries({ queryKey: eachKey });
      });

      if (onSuccess) onSuccess(res, params);
    },
  });

  return testMuation;
};

export { useCreateWallets };
export type { CreateWalletRes, CreateWalletsProps };
