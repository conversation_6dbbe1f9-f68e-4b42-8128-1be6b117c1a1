// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type TronMasterRes = {
  /**
   * The minimum USDT balance required to trigger aggregation.
   * Typically used to ensure sufficient funds before performing certain operations.
   */
  minUsdtBalanceForAggregation: number;

  /**
   * The cooling period for the wallet, in minutes.
   * This indicates the time during which certain wallet operations are paused or limited.
   */
  walletColingInMinute: number;

  /**
   * Whether the save mode is enabled.
   * Save mode may restrict certain high-risk or resource-intensive operations.
   */
  saveModeEnabled: boolean;

  /**
   * Whether third-party energy leasing is enabled.
   * This could allow the wallet to participate in external energy leasing programs.
   */
  isThirdPartyEnergyLeasingEnabled: boolean;
  transactionStatus: number;
};
type TronMasterProps = {};
type Other = {};

const useTronMaster = (useProps: UseTestQueryProps<Other, TronMasterProps>) => {
  // props
  const { onSuccess, onError, ...config } = useProps;

  const testQuery = useTestQuery<TronMasterRes, TronMasterProps>({
    ...config,
    queryKey: queryKeys.query.tronMaster,
    qf: (params) => {
      const request = axiosRoot.get('/V1.0/tron/master', { params }).then(({ data }) => data);

      return request;
    },
    delay: 1000,
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res);
    },
    onError: (error) => {
      if (onError) onError(error);
    },
    staleTime: Infinity,
  });

  return testQuery;
};

export { useTronMaster };
export type { TronMasterRes, TronMasterProps };
