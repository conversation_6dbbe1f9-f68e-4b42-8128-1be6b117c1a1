// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type DeleteSupervisorWhitelistRes = {};
type DeleteSupervisorWhitelistProps = {
  id: number;
  operationPassword: string;
};
type Other = {};

const useDeleteSupervisorWhitelist = (
  useProps: UseTestMutationProps<DeleteSupervisorWhitelistRes, DeleteSupervisorWhitelistProps, Other>,
) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useDeleteSupervisorWhitelist');

  const testMutation = useTestMutation<DeleteSupervisorWhitelistRes, DeleteSupervisorWhitelistProps>({
    ...config,
    mutationFn: ({ id, operationPassword }) => {
      const request = axiosRoot
        .delete(`/V1.0/supervisor/whiteList/${id}`, {
          data: { operationPassword },
        })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useDeleteSupervisorWhitelist };
export type { DeleteSupervisorWhitelistRes, DeleteSupervisorWhitelistProps };
