// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type TransSvInRes = {};
type TransSvInProps = {
  from: string;
  usdtAmount: number;
  operationPassword: string;
};
type Other = {};

const useTransSvIn = (useProps: UseTestMutationProps<TransSvInRes, TransSvInProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<TransSvInRes, TransSvInProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/cs', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.getAllTronWalletsKeys().forEach((eachKeys) => {
        queryClient.invalidateQueries({ queryKey: eachKeys });
      });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useTransSvIn };
export type { TransSvInRes, TransSvInProps };
