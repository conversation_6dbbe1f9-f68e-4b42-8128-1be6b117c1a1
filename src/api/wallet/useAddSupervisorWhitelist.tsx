// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type AddSupervisorWhitelistRes = {};
type AddSupervisorWhitelistProps = {
  name: string;
  description: string;
  address: string;
  operationPassword: string;
  twoFactorCode: string;
};
type Other = {};

const useAddSupervisorWhitelist = (
  useProps: UseTestMutationProps<AddSupervisorWhitelistRes, AddSupervisorWhitelistProps, Other>,
) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useAddSupervisorWhitelist');

  const testMutation = useTestMutation<AddSupervisorWhitelistRes, AddSupervisorWhitelistProps>({
    ...config,
    mutationFn: ({ name, description, address, operationPassword, twoFactorCode }) => {
      const request = axiosRoot
        .post(`/V1.0/supervisor/whiteList`, {
          name,
          description,
          address,
          operationPassword,
          twoFactorCode,
        })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useAddSupervisorWhitelist };
export type { AddSupervisorWhitelistRes, AddSupervisorWhitelistProps };
