// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type AddPayTronWalletRes = {};
type AddPayTronWalletProps = {
  merchantId: number;
  operationPassword: string;
};
type Other = {};

const useAddPayTronWallet = (useProps: UseTestMutationProps<AddPayTronWalletRes, AddPayTronWalletProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<AddPayTronWalletRes, AddPayTronWalletProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant/trc20/paymentAddress', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.getAllMerchantListKeys().forEach((eachKeys) => {
        queryClient.invalidateQueries({ queryKey: eachKeys });
      });
      queryKeys.query.getAllTronWalletsKeys().forEach((eachKeys) => {
        queryClient.invalidateQueries({ queryKey: eachKeys });
      });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useAddPayTronWallet };
export type { AddPayTronWalletRes, AddPayTronWalletProps };
