// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type SupervisorOrderRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<SupervisorOrderItem>;
};

type SupervisorOrderProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: 'Id' | 'ActualAmount' | 'Gas';
  OrderByDescending?: boolean;
  Hash?: string;
  Status?: number;
  ConfirmedAtStart?: string;
  ConfirmedAtEnd?: string;
  CreatedAtStart?: string;
  CreatedAtEnd?: string;
  SupervisorName?: string;
};

interface SupervisorOrderItem {
  id: number;
  supervisorName: string;
  order: {
    requireAmount: number;
    actualAmount: number;
    transactionType: number;
    cryptoType: number;
    status: number;
    remark: string;
    hash: string | null;
    to: string;
    from: string;
    gas: number;
    fee: number;
    confirmedAt: string | null;
  };
  monitor: {
    id: number;
    status: number;
    isUsdtEnough: boolean;
    usdtStatus: number;
    isTrxEnough: boolean;
    trxStatus: number;
    isEnergyEnough: true;
    energyStatus: number;
    usdtTransferredAmount: number;
    usdtToBeTransferred: number;
  } | null;
  createdAt: string;
}

const useSupervisorOrders = (useProps: UseTestQueryProps<{}, SupervisorOrderProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  const testQuery = useTestQuery<SupervisorOrderRes, SupervisorOrderProps>({
    ...config,
    queryKey: ['supervisorOrders', params],
    qf: () => {
      const request = axiosRoot
        .get(`/V1.0/supervisor-order`, { params })
        .then(({ data }) => data)
        .catch(() => []);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useSupervisorOrders };
export type { SupervisorOrderRes, SupervisorOrderProps, SupervisorOrderItem };
