// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type ActivePlatformRes = {};
type ActivePlatformProps = {
  platformId: number;
  operationPassword: string;
};
type Other = {};

const useActivePlatform = (useProps: UseTestMutationProps<ActivePlatformRes, ActivePlatformProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<ActivePlatformRes, ActivePlatformProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/lease-platform/activate', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useActivePlatform };
export type { ActivePlatformRes, ActivePlatformProps };
