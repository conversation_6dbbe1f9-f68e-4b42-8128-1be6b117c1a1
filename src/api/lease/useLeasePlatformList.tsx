// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type LeasePlatformListRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<LeasePlatformItemInterface>;
};
type LeasePlatformListProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: 'id';
  OrderByDescending?: boolean;
  IsActive?: boolean;
  Name?: string;
  Address?: string;
};
type Other = {};

interface EnergyTierItem {
  leaseCount: number;
  energyConsumption: number;
  requiredTrxAmount: number;
}

interface LeasePlatformItemInterface {
  id: number;
  name: string;
  address: string;
  isActive: boolean;
  energyTiers: Array<EnergyTierItem>;
}

const useLeasePlatformList = (useProps: UseTestQueryProps<Other, LeasePlatformListProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  // compute
  const testQuery = useTestQuery<LeasePlatformListRes, LeasePlatformListProps>({
    ...config,
    queryKey: ['lease', 'platform', 'list', ...Object.values(params || {})],
    qf: () => {
      const request = axiosRoot.get('/V1.0/lease-platform', { params }).then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useLeasePlatformList };
export type { LeasePlatformListRes, LeasePlatformListProps, LeasePlatformItemInterface, EnergyTierItem };
