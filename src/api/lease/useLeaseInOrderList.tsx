// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';
import { LeaseInOrderStatusEnum } from '@/utils';

// utils
import axiosRoot from '@/utils/axiosRoot';

interface LeaseInOrderItemInterface {
  id: number;
  leaseAmount: number;
  paidTrx: number;
  paymentHash: string;
  status: LeaseInOrderStatusEnum;
  platform: {
    id: number;
    name: string;
  };
}
interface LeaseInOrderListRes {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<LeaseInOrderItemInterface>;
}
interface LeaseInOrderListProps {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: 'Id' | 'LeaseAmount' | 'PaidTrx' | 'PaymentHash' | 'Status';
  OrderByDescending?: boolean;
  Status?: LeaseInOrderStatusEnum;
  PaymentHash?: string;
  PlatformId?: string;
  PlatformName?: string;
}
interface Other {}

const useLeaseInOrderList = (useProps: UseTestQueryProps<Other, LeaseInOrderListProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  // compute
  const testQuery = useTestQuery<LeaseInOrderListRes, LeaseInOrderListProps>({
    ...config,
    queryKey: ['lease', 'in', 'order', 'list', ...Object.values(params || {})],
    qf: () => {
      const request = axiosRoot.get('/V1.0/lease-order', { params }).then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useLeaseInOrderList };
export type { LeaseInOrderItemInterface, LeaseInOrderListRes, LeaseInOrderListProps };
