import { UseTestQueryProps, useTestQuery } from '@/hooks';
import axiosRoot from '@/utils/axiosRoot';
import { LeaseOutOrderStatusEnum } from '@/utils';

interface LeaseOutOrderItemInterface {
  id: number;
  walletAddress: string;
  receivingWalletAddress: string;
  trxAmount: number;
  energyAmount: number;
  receivedTxHash: string;
  leaseTxHash: string | null;
  reclaimTxHash: string | null;
  leaseStartTime: string;
  leaseEndTime: string | null;
  leaseDurationInMinutes: number;
  status: LeaseOutOrderStatusEnum;
  statusDescription: string;
  createdAt: string;
}
interface LeaseOutOrderListRes {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<LeaseOutOrderItemInterface>;
}
interface LeaseOutOrderListProps {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: 'Id';
  OrderByDescending?: boolean;
  StartTime?: string;
  EndTime?: string;
  Status?: Array<LeaseOutOrderStatusEnum>;
}
interface Other {}

const useLeaseOutOrderList = (useProps: UseTestQueryProps<Other, LeaseOutOrderListProps>) => {
  const { params, onSuccess, ...config } = useProps;

  const statusQuery = params?.Status?.map((status) => `status=${status}`).join('&') || '';
  const testQuery = useTestQuery<LeaseOutOrderListRes, LeaseOutOrderListProps>({
    ...config,
    queryKey: ['lease', 'out', 'order', 'list', ...Object.values(params || {})],
    qf: () => {
      const request = axiosRoot
        .get(`/V1.0/energy-lease-plan/order?${statusQuery}`, { params: { ...params, Status: undefined } })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useLeaseOutOrderList };
export type { LeaseOutOrderItemInterface, LeaseOutOrderListRes, LeaseOutOrderListProps };
