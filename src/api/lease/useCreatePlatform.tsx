// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type CreatePlatformRes = {};
type CreatePlatformProps = {
  name: string;
  address: string;
  operationPassword: string;
};
type Other = {};

const useCreatePlatform = (useProps: UseTestMutationProps<CreatePlatformRes, CreatePlatformProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<CreatePlatformRes, CreatePlatformProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/lease-platform', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreatePlatform };
export type { CreatePlatformRes, CreatePlatformProps };
