// libs
import { UseQueryResult, useQueries } from '@tanstack/react-query';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

// api
import { MerchantListRes } from '../merchant';

type ClientsMerchantsProps = {
  customerNames: Array<string>;
};

type ClientsMerchantsRes = Array<{
  clientName: string;
  merchantsInfo: MerchantListRes;
}>;

const useClientsMerchants = (useProps: ClientsMerchantsProps) => {
  // props
  const { customerNames, ...config } = useProps;

  const results = useQueries<ClientsMerchantsRes>({
    queries: customerNames.map((mapName) => ({
      queryKey: queryKeys.query.cliensMerchants(mapName),
      queryFn: () => {
        const request = axiosRoot
          .get<MerchantListRes>('/V1.0/merchant', { params: { CustomerName: mapName } })
          .then(({ data }) => {
            return {
              clientName: mapName,
              merchantsInfo: data,
            };
          });

        return request;
      },
      staleTime: 1000 * 60 * 30,
    })),
    ...config,
  });

  return results;
};

export { useClientsMerchants };
export type { ClientsMerchantsProps, ClientsMerchantsRes };
export type ClientMerchantsResult = UseQueryResult<ClientsMerchantsRes['0']>;
