// libs
import { UseQueryResult, useQueries } from '@tanstack/react-query';

// utils
import axiosRoot from '@/utils/axiosRoot';

type ClientDetailsProps = {
  ids: Array<number>;
};

type ClientDetailsRes = Array<{
  clientID: number;
  details: Array<ClientDetailInterface>;
}>;

interface ClientDetailInterface {
  memberDepositAmount: number; // The total deposit amount made by members
  memberDepositCount: number; // The number of deposit transactions made by members
  memberDepositFee: number; // The fee incurred from member deposit transactions
  memberDepositGas: number; // The gas fees associated with member deposit transactions
  memberWithdrawalAmount: number; // The total withdrawal amount made by members
  memberWithdrawalCount: number; // The number of withdrawal transactions made by members
  memberWithdrawalFee: number; // The fee incurred from member withdrawal transactions
  memberWithdrawalGas: number; // The gas fees associated with member withdrawal transactions
  merchantDepositAmount: number; // The total deposit amount made by the merchant
  merchantDepositCount: number; // The number of deposit transactions made by the merchant
  merchantDepositFee: number; // The fee incurred from merchant deposit transactions
  merchantDepositGas: number; // The gas fees associated with merchant deposit transactions
  merchantId: number; // The unique identifier for the merchant
  merchantName: string; // The name of the merchant
  merchantNumber: string; // The unique merchant number (e.g., account or reference number)
  merchantWithdrawalAmount: number; // The total withdrawal amount made by the merchant
  merchantWithdrawalCount: number; // The number of withdrawal transactions made by the merchant
  merchantWithdrawalFee: number; // The fee incurred from merchant withdrawal transactions
  merchantWithdrawalGas: number; // The gas fees associated with merchant withdrawal transactions
}

const useClientDetails = (useProps: ClientDetailsProps) => {
  // props
  const { ids, ...config } = useProps;

  const results = useQueries<ClientDetailsRes>({
    queries: ids.map((mapID) => ({
      queryKey: ['client', 'detail', mapID],
      queryFn: () => {
        const request = axiosRoot
          .get<Array<ClientDetailInterface>>('/V1.0/customer/summary/details', {
            params: { customerId: mapID },
          })
          .then(({ data }) => {
            return { details: data, clientID: mapID };
          });

        return request;
      },
      staleTime: 1000 * 60 * 30,
    })),
    ...config,
  });

  return results;
};

export { useClientDetails };
export type { ClientDetailsProps, ClientDetailsRes, ClientDetailInterface };
export type ClientDetailResult = UseQueryResult<ClientDetailsRes['0']>;
