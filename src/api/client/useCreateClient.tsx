// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type CreateClientRes = {
  customerId: number;
};
type CreateClientProps = {
  name: string;
  operationPassword: string;
};
type Other = {};

const useCreateClient = (useProps: UseTestMutationProps<CreateClientRes, CreateClientProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<CreateClientRes, CreateClientProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/customer', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreateClient };
export type { CreateClientRes, CreateClientProps };
