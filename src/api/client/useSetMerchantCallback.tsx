// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// store
import { useNotifyStore } from '@/store';

// utils
import axiosRoot from '@/utils/axiosRoot';

type SetMerchantCallbackRes = {};
type SetMerchantCallbackProps = {
  merchantId: number;
  callbackUrl: string;
  operationPassword: string;
};
type Other = {};

const useSetMerchantCallback = (
  useProps: UseTestMutationProps<SetMerchantCallbackRes, SetMerchantCallbackProps, Other>,
) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { pushBSQ } = useNotifyStore();

  const testMutation = useTestMutation<SetMerchantCallbackRes, SetMerchantCallbackProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant/set/callback', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      pushBSQ([{ title: 'UXM Settlement Corp', des: '設定回調成功' }]);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useSetMerchantCallback };
export type { SetMerchantCallbackRes, SetMerchantCallbackProps };
