// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type ClientListRes = Array<ClientInterface>;
type ClientListProps = {};
type Other = {};

interface ClientInterface {
  createdAt: string;
  customerName: string;
  balance: number;
  customerId: number;
  lockedBalance: number;
  memberDepositAmount: number;
  memberDepositCount: number;
  memberDepositFee: number;
  memberDepositGas: number;
  memberWithdrawalAmount: number;
  memberWithdrawalCount: number;
  memberWithdrawalFee: number;
  memberWithdrawalGas: number;
  merchantCount: number;
  merchantDepositAmount: number;
  merchantDepositCount: number;
  merchantDepositFee: number;
  merchantDepositGas: number;
  merchantWithdrawalAmount: number;
  merchantWithdrawalCount: number;
  merchantWithdrawalFee: number;
  merchantWithdrawalGas: number;
}

const useClientList = (useProps: UseTestQueryProps<Other, ClientListProps>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<ClientListRes, ClientListProps>({
    ...config,
    queryKey: ['client', 'list', ...Object.values(params || {})],
    qf: () => {
      const request = axiosRoot.get('/V1.0/customer/summary', { params }).then(({ data }) => data);

      return request;
    },
    staleTime: Infinity,
  });

  return testQuery;
};

export { useClientList };
export type { ClientListRes, ClientListProps, ClientInterface };
