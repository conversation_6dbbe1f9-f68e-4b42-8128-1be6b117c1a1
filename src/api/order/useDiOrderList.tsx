// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import { CryptoEnum, TxStatusNum } from '@/utils';
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type DiOrderListRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<DiOrderItemInterface>;
};

type DiOrderListProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderByDescending?: boolean;
  OrderUid?: string;
  MerchantOrderId?: string;
  MerchantNumber?: number;
  CreatedAtStart?: string;
  CreatedAtEnd?: string;
};

type Other = {};

// DI Order item interface based on the API response
interface DiOrderItemInterface {
  merchantNumber: string; // "857907"
  orderUid: string; // P-B-**************-5CUBV2
  merchantOrderId: string; // d01ssa37-8da5-3caa-a237-6esa33e31e82
  memberId: string;
  payerBankAccountName: string;
  entryCode: string; // UvWfALsFmhcNqd5HCKLoWeVuBhTiGt7W
  transactionType: number; // 1
  cryptoType: CryptoEnum; // 1
  cryptoAmount: number; // 1502.05
  fiatType: string; // CNY
  fiatAmount: number; // 10965
  status: TxStatusNum; // 3
  createdAt: string; // 2025-05-27T10:57:05.88964Z
  isMerchantNotified: boolean;
}

const useDiOrderList = (useProps: UseTestQueryProps<Other, DiOrderListProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  const testQuery = useTestQuery<DiOrderListRes, DiOrderListProps>({
    ...config,
    queryKey: queryKeys.query.diOrderList(params),
    qf: () => {
      const request = axiosRoot
        .get('/V1.0/order/payment-di', { params })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useDiOrderList };
export type { DiOrderListRes, DiOrderListProps, DiOrderItemInterface };
