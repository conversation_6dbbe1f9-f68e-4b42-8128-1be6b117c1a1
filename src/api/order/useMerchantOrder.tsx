// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import { CryptoEnum, MerchantTransactionStatus, TxCategoryNum, TxStatusNum } from '@/utils';
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type MerchantOrderListRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<MerchantOrderItemInterface>;
};
type MerchantOrderListProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: number;
  OrderByDescending?: number;
  MerchantNumber?: number;
  ApplicantName?: string;
  ApproverName?: string;
  Hash?: string;
  TransactionType?: TxCategoryNum;
  CryptoType?: CryptoEnum;
  Status: Array<MerchantTransactionStatus>;
  ConfirmedAtStart?: string;
  ConfirmedAtEnd?: string;
  CreatedAtStart?: string;
  CreatedAtEnd?: string;
};
type Other = {};

// Merchant order item
interface MerchantOrderItemInterface {
  id: number; // Unique ID of the order
  merchantNumber: string; // Merchant number
  merchantName: string; // Merchant name
  applicantName: string; // Name of the applicant
  approverName: string | null; // Name of the approver (if any)
  order: {
    requireAmount: number; // Requested amount
    actualAmount: number; // Actual amount
    transactionType: number; // Transaction type
    cryptoType: number; // Crypto type (e.g., 1 = USDT)
    status: TxStatusNum; // Transaction status
    remark: string | null; // Remarks
    hash: string | null; // Transaction hash
    to: string; // Destination address
    from: string | null; // Source address
    gas: number; // Gas fee
    fee: number; // Transaction fee
    confirmedAt: string | null; // Confirmation time
  };
  monitor: {
    id: number;
    status: number;
    isUsdtEnough: boolean;
    usdtStatus: number;
    isTrxEnough: boolean;
    trxStatus: number;
    isEnergyEnough: boolean;
    energyStatus: number;
    usdtTransferredAmount: number;
    usdtToBeTransferred: number;
  } | null;
  createdAt: string; // Creation time
}

const useMerchantOrderList = (useProps: UseTestQueryProps<Other, MerchantOrderListProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;
  const { Status, ...rest } = params!;

  // compute
  const status = Status.map((s) => `Status=${s}`).join('&');

  const testQuery = useTestQuery<MerchantOrderListRes, MerchantOrderListProps>({
    ...config,
    queryKey: queryKeys.query.merchantOrderList(params),
    qf: () => {
      const request = axiosRoot.get(`/V1.0/merchant-order?${status}`, { params: { ...rest } }).then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useMerchantOrderList };
export type { MerchantOrderListRes, MerchantOrderListProps, MerchantOrderItemInterface };
