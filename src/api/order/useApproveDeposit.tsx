// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type AppropveDepositRes = {};
type AppropveDepositProps = {
  transactionHash: string;
  operationPassword: string;
  merchantOrderId?: string | undefined;
  remark?: string | undefined;
};
type Other = {
  isTimeOut?: boolean;
};

const useAppropveDeposit = (useProps: UseTestMutationProps<AppropveDepositRes, AppropveDepositProps, Other>) => {
  // props
  const { onSuccess, isTimeOut, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<AppropveDepositRes, AppropveDepositProps>({
    ...config,
    mutationFn: (props) => {
      if (isTimeOut) {
        const { transactionHash, ...rest } = props;
        const request = axiosRoot
          .post('/V1.0/order/tron/usdt/deposit/overtime', { hash: transactionHash, ...rest })
          .then(({ data }) => data);

        return request;
      }
      const { transactionHash, remark, ...rest } = props;

      const request = axiosRoot
        .post('/V1.0/depositAnomaly/approve', { hash: transactionHash, ...rest })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allOrderList().forEach((eachQueryKey) => {
        queryClient.invalidateQueries({ queryKey: eachQueryKey });
      });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useAppropveDeposit };
export type { AppropveDepositRes, AppropveDepositProps };
