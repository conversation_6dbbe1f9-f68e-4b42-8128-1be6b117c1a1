// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type CancelOrderRes = {};
type CancelOrderProps = {
  orderUid: string;
  operationPassword: string;
};
type Other = {};

const useCancelOrder = (useProps: UseTestMutationProps<CancelOrderRes, CancelOrderProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<CancelOrderRes, CancelOrderProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.patch('/V1.0/tron/order/cancel', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allOrderList().forEach((eachQueryKey) => {
        queryClient.invalidateQueries({ queryKey: eachQueryKey });
      });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCancelOrder };
export type { CancelOrderRes, CancelOrderProps };
