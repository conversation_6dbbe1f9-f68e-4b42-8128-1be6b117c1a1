// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type ReCallbackRes = {};
type ReCallbackProps = {
  orderUid: string;
  operationPassword: string;
};
type Other = {};

const useReCallback = (useProps: UseTestMutationProps<ReCallbackRes, ReCallbackProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<ReCallbackRes, ReCallbackProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/order/callback/resend', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allOrderList().forEach((eachQueryKey) => {
        queryClient.invalidateQueries({ queryKey: eachQueryKey });
      });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useReCallback };
export type { ReCallbackRes, ReCallbackProps };
