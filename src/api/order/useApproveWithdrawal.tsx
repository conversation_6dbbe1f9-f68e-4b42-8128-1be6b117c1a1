// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// store
import { useNotifyStore } from '@/store';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type ApproveWithdrawalRes = {};
type ApproveWithdrawalProps = {
  merchantTransactionId: number;
  operationPassword: string;
};
type Other = {};

const useApproveWithdrawal = (useProps: UseTestMutationProps<ApproveWithdrawalRes, ApproveWithdrawalProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();
  const { pushBSQ } = useNotifyStore();

  const testMutation = useTestMutation<ApproveWithdrawalRes, ApproveWithdrawalProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant-order/withdrawal/approve', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      pushBSQ([{ title: 'UXM Settlement Corp', des: `Order #${params?.merchantTransactionId} approved succesfully` }]);
      queryKeys.query.allOrderList().forEach((eachQueryKey) => {
        queryClient.invalidateQueries({ queryKey: eachQueryKey });
      });
      if (onSuccess) {
        onSuccess(res, params);
      }
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useApproveWithdrawal };
export type { ApproveWithdrawalRes, ApproveWithdrawalProps };
