// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import { CryptoEnum, TxCategoryNum, TxStatusNum } from '@/utils';
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type OrderListRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<OrderItemInterface>;
};
type OrderListProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: 'ActualAmount' | 'Gas' | 'Fee' | 'Id';
  OrderByDescending?: boolean;
  OrderUid?: string;
  MerchantOrderId?: string;
  MerchantId?: string;
  SystemOrderId?: string;
  MerchantNumber?: number;
  Hash?: string;
  CryptoType?: CryptoEnum;
  TransactionType?: TxCategoryNum;
  Status?: Array<TxStatusNum>;
  ConfirmedAtStart?: string;
  ConfirmedAtEnd?: string;
  CreatedAtStart?: string;
  CreatedAtEnd?: string;
  IsManualOrder?: number;
  IsWithdrawalFail?: number;
};
type Other = {};

// Order item
interface OrderItemInterface {
  systemOrderId: string; // System-generated order ID
  monitor: {
    id: number;
    status: number;
    isUsdtEnough: boolean;
    usdtStatus: number;
    isTrxEnough: boolean;
    trxStatus: number;
    isEnergyEnough: true;
    energyStatus: number;
    usdtTransferredAmount: number;
    usdtToBeTrasnferred: number;
  } | null;
  orderUid: string; // Unique ID for the order
  systemUserName: string | null; // System username associated with the order (null if not provided)
  createdAt: string; // Timestamp when the order was created
  merchantOrderId: string; // Unique ID for the merchant's internal order
  merchantId: number; // Unique ID for the merchant processing the order
  merchantNumber: string; // Merchant's identification number
  merchantName: string; // Name of the merchant
  merchantUserName: string | null; // Merchant's username (null if not provided)
  order: {
    requireAmount: number; // The required amount to complete the order
    actualAmount: number; // The actual amount of the transaction
    transactionType: TxCategoryNum;
    cryptoType: CryptoEnum;
    status: TxStatusNum;
    remark: string; // Any remarks or notes related to the order
    hash: string | null; // The transaction hash (null if not yet processed)
    to: string; // The destination address for the transaction
    from: string | null; // The source address for the transaction (null if not provided)
    gas: number; // Gas fee for the transaction (for blockchain transactions)
    fee: number; // Transaction fee associated with the order
    confirmedAt: string | null; // Timestamp when the order was confirmed (null if not confirmed)
  };
}

const useOrderList = (useProps: UseTestQueryProps<Other, OrderListProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  // compute
  const statusQuery = params?.Status?.map((mapS) => `Status=${mapS}`).join('&') || '';

  const testQuery = useTestQuery<OrderListRes, OrderListProps>({
    ...config,
    queryKey: queryKeys.query.orderList(params),
    qf: () => {
      const request = axiosRoot
        .get(`/V1.0/order?${statusQuery}`, { params: { ...params, Status: undefined } })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useOrderList };
export type { OrderListRes, OrderListProps, OrderItemInterface };
