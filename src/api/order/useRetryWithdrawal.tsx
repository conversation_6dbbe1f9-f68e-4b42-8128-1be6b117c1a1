// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type RetryWithdrawalRes = {};
type RetryWithdrawalProps = {
  monitorId: number;
  operationPassword: string;
};
type Other = {};

const useRetryWithdrawal = (useProps: UseTestMutationProps<RetryWithdrawalRes, RetryWithdrawalProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<RetryWithdrawalRes, RetryWithdrawalProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/order/tron/usdt/withdrawal/retry', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allOrderList().forEach((eachQueryKey) => {
        queryClient.invalidateQueries({ queryKey: eachQueryKey });
      });
      if (onSuccess) {
        onSuccess(res, params);
      }
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useRetryWithdrawal };
export type { RetryWithdrawalRes, RetryWithdrawalProps };
