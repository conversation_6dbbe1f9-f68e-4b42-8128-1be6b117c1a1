import { UseTestQueryProps, useTestQuery } from '@/hooks';
import { AnomalyResolutionStatusEnum, AnomalyStatusEnum, CryptoEnum } from '@/utils';
import axiosRoot from '@/utils/axiosRoot';

type DepositAnomalyRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<DepositAnomalyItemInterface>;
};

type DepositAnomalyProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: number;
  OrderByDescending?: number;
  TransactionHash?: string | null;
  CryptoType?: CryptoEnum;
  ResolutionStatus?: AnomalyResolutionStatusEnum;
  Status?: AnomalyStatusEnum;
  To?: string;
};
type Other = {
  staleTime?: number;
};

// Order item
interface DepositAnomalyItemInterface {
  amount: number; // The amount of the transaction, typically a numeric value.
  createdAt: string; // The timestamp when the order was created, formatted in ISO 8601.
  cryptoType: number; // The type of cryptocurrency used, represented as a numeric identifier.
  from: string; // The address from which the transaction originated.
  gas: number; // The amount of gas used in the transaction (often for blockchain transactions).
  hash: string; // The hash of the transaction on the blockchain or related ledger.
  remark: string | null; // Any remarks or comments associated with the order, or null if not provided.
  resolutionStatus: number; // The resolution status of the transaction, where different numbers may indicate various statuses (e.g., resolved, pending).
  resolvedAt: string | null; // The timestamp when the order was resolved, or null if it hasn't been resolved yet.
  status: number; // The status of the order (e.g., 1 for active, 3 for completed, etc.).
  to: string; // The destination address for the transaction.
}

const useDepositAnomaly = (useProps: UseTestQueryProps<Other, DepositAnomalyProps>) => {
  // props
  const { params, staleTime, ...config } = useProps;
  const queryKey = ['deposit', 'anomaly', ...Object.values(params || {})];

  const testQuery = useTestQuery<DepositAnomalyRes, DepositAnomalyProps>({
    ...config,
    queryKey,
    qf: () => {
      const request = axiosRoot.get('/V1.0/depositAnomaly', { params }).then(({ data }) => data);

      return request;
    },
    staleTime: staleTime || 1000 * 60 * 5,
  });

  return { ...testQuery, queryKey };
};

export { useDepositAnomaly };
export type { DepositAnomalyRes, DepositAnomalyProps, DepositAnomalyItemInterface };
