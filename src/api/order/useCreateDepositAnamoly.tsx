// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type CreateAnomalyDepositRes = {};
type CreateAnomalyDepositProps = {
  hash: string;
  to: string;
  from: string;
  amount: number;
  gas: number;
  remark: string;
  operationPassword: string;
};
type Other = {};

const useCreateAnomalyDeposit = (
  useProps: UseTestMutationProps<CreateAnomalyDepositRes, CreateAnomalyDepositProps, Other>,
) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<CreateAnomalyDepositRes, CreateAnomalyDepositProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/depositAnomaly/record', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allOrderList().forEach((eachQueryKey) => {
        queryClient.invalidateQueries({ queryKey: eachQueryKey });
      });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreateAnomalyDeposit };
export type { CreateAnomalyDepositRes, CreateAnomalyDepositProps };
