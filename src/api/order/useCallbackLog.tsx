import { UseTestQueryProps, useTestQuery } from '@/hooks';
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type Props = {
  OrderUid: string | undefined;
};

type CallbackLogRes = {
  attempt: number;
  totalAttempts: number;
  lastAttemptTime: string;
  message: string;
};
type Other = {};
const useCallbackLog = (useProps: UseTestQueryProps<Other, Props>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<CallbackLogRes, Props>({
    ...config,
    queryKey: queryKeys.query.callbackLog(params?.OrderUid),
    qf: () => {
      const request = axiosRoot.get(`/V1.0/order/callback/${params?.OrderUid}`, { params }).then(({ data }) => data);

      return request;
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useCallbackLog };
export type { CallbackLogRes };
