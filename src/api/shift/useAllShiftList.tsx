// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import { ShiftNameEnum } from '@/utils';
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

export type MerchantStatistics = {
  key: number;
  shiftId?: number;
  merchantNumber: number;
  merchantName: string;
  customerName: string;
  balance: number;
  lockedBalance: number;
  totalFees: number;
  shiftName?: ShiftNameEnum;
  startTime: string;
  endTime: string;
  totalGasPay: number;
  summary: {
    memberDepositCount: number;
    memberDepositAmount: number;
    memberDepositFee: number;
    memberDepositGas: number;
    memberWithdrawalCount: number;
    memberWithdrawalAmount: number;
    memberWithdrawalFee: number;
    memberWithdrawalGas: number;
    merchantDepositCount: number;
    merchantDepositAmount: number;
    merchantDepositFee: number;
    merchantDepositGas: number;
    merchantWithdrawalCount: number;
    merchantWithdrawalAmount: number;
    merchantWithdrawalFee: number;
    merchantWithdrawalGas: number;
  };
};

type ListShiftOption = {
  id: number;
  shiftName: ShiftNameEnum;
  startTime: string;
  endTime: string;
  trx: number;
  usdt: number;
  totalFees: number;
  totalGasPay: number;
  merchantStatistics: Array<MerchantStatistics>;
};

type ShiftListRes = {
  currentPage: number;
  items: Array<ListShiftOption>;
  pageSize: number;
  totalCount: number;
  totalPages: number;
};

type ShiftListProps = {
  PageSize?: number;
  PageNumber?: number;
  OrderBy?: 'Id';
  OrderByDescending?: boolean;
  ShiftName?: ShiftNameEnum | null;
  StartDate?: string;
  EndDate?: string;
};

type Other = {};
const useAllShiftList = (useProps: UseTestQueryProps<Other, ShiftListProps, ShiftListRes>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<ShiftListRes, ShiftListProps>({
    ...config,
    queryKey: queryKeys.query.allShiftList(params),
    qf: () => {
      const request = axiosRoot.get('/V1.0/shift', { params }).then(({ data }) => data);

      return request;
    },
  });

  return { ...testQuery };
};

export { useAllShiftList };
export type { ListShiftOption, ShiftListRes, ShiftListProps };
