// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type WhitelistRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<WhitelistIpInterface>;
};

type WhitelistIpProps = {
  merchantId?: string;
  PageNumber?: number;
  PageSize?: number;
};

interface WhitelistIpInterface {
  ipAddress: string;
  description: string;
  createdAt: string;
}

const useWhitelistIps = (useProps: UseTestQueryProps<{}, WhitelistIpProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useWhitelist');

  const testQuery = useTestQuery<WhitelistRes, WhitelistIpProps>({
    ...config,
    queryKey: ['whitelistIps', params?.merchantId],
    qf: () => {
      const { merchantId, PageNumber = 1, PageSize = 10 } = params || {};
      if (!merchantId) {
        throw new Error(t('errorMessage'));
      }
      const request = axiosRoot
        .get(`/V1.0/ip/whitelist/admin/merchants/${merchantId}`, {
          params: { PageNumber, PageSize },
        })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useWhitelistIps };
export type { WhitelistRes, WhitelistIpProps, WhitelistIpInterface };
