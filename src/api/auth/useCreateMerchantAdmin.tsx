// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type CreateMerchantAdminRes = {};
type CreateMerchantAdminProps = {
  userName: string;
  password: string;
  nickName: string;
  merchantId: number;
};
type Other = {};

const useCreateMerchantAdmin = (
  useProps: UseTestMutationProps<CreateMerchantAdminRes, CreateMerchantAdminProps, Other>,
) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<CreateMerchantAdminRes, CreateMerchantAdminProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/merchant/admin', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreateMerchantAdmin };
export type { CreateMerchantAdminRes, CreateMerchantAdminProps };
