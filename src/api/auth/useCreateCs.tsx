// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type CreateCsRes = {};
type CreateCsProps = {
  userName: string;
  password: string;
  nickName: string;
};
type Other = {};

const useCreateCs = (useProps: UseTestMutationProps<CreateCsRes, CreateCsProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<CreateCsRes, CreateCsProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/cs', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreateCs };
export type { CreateCsRes, CreateCsProps };
