// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// store
import { useUserStore } from '@/store';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type SetOpwRes = {};
type SetOpwProps = {
  loginPassword: string;
  operationPassword: string;
};
type Other = {};

const useSetOpw = (useProps: UseTestMutationProps<SetOpwRes, SetOpwProps, Other>) => {
  const queryClient = useQueryClient();
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { loginRes } = useUserStore();

  const testMutation = useTestMutation<SetOpwRes, SetOpwProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/v1/Account/operation-password/set', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      queryClient.invalidateQueries({ queryKey: queryKeys.query.info(loginRes?.token) });
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useSetOpw };
export type { SetOpwRes, SetOpwProps };
