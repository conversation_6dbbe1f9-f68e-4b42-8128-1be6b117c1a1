// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type CreateSupervisorRes = {};
type CreateSupervisorProps = {
  userName: string;
  password: string;
  nickName: string;
};
type Other = {};

const useCreateSupervisor = (useProps: UseTestMutationProps<CreateSupervisorRes, CreateSupervisorProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<CreateSupervisorRes, CreateSupervisorProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/supervisor', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreateSupervisor };
export type { CreateSupervisorRes, CreateSupervisorProps };
