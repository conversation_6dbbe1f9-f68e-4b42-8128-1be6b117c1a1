// libs
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// store
import { useNotifyStore, useUserStore } from '@/store';

// utils
import axiosRoot from '@/utils/axiosRoot';

type LoginRes = {
  id: string;
  requiredTwoFactor: boolean;
  userName: string;
  token: string;
  roles: Array<RoleTypes>; // .d.ts
};
type LoginProps = {
  username: string;
  password: string;
};
type Other = {};

const useLogin = (useProps: UseTestMutationProps<LoginRes, LoginProps, Other>) => {
  // props
  const { onError, onSuccess, ...config } = useProps;

  // hooks
  const navigate = useNavigate();
  const { setLoginProps, setLoginRes } = useUserStore();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('useLogin');

  const testMutation = useTestMutation<LoginRes, LoginProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot
        .post('/V1.0/auth/login', { ...props, username: props.username.trim() })
        .then(({ data }) => data);

      return request;
    },
    onMutate: (props) => {
      setLoginProps(props);
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      if (res.requiredTwoFactor) {
        pushBSQ([{ title: 'UXM Settlement Corp', des: t('requireDescription') }]);

        return;
      }
      setLoginRes(res);
      pushBSQ([{ title: 'UXM Settlement Corp', des: t('successDescription') }]);
      setTimeout(() => {
        navigate('/');
      }, 100);
    },
    onError: (error) => {
      if (onError) onError(error);
      setLoginProps(null);
      setLoginRes(null);
    },
    delay: 1000,
  });

  return testMutation;
};

export { useLogin };
export type { LoginRes, LoginProps };
