// libs
import { useCallback, useEffect } from 'react';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type Use2FQrcodeRes = {
  key: string;
  qrCodeUrl: string;
};
type Use2FQrcodeProps = {};
type Other = {
  isActive?: boolean;
};

const use2FQrcode = (useProps: UseTestMutationProps<Use2FQrcodeRes, Use2FQrcodeProps, Other>) => {
  // props
  const { isActive, ...config } = useProps;

  const testMutation = useTestMutation<Use2FQrcodeRes, Use2FQrcodeProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.get('/V1.0/TwoFactor', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });
  const { mutate } = testMutation;

  // handlers
  const handleSearch = useCallback(() => {
    mutate({});
  }, [mutate]);

  useEffect(() => {
    if (!isActive) return;
    handleSearch();
  }, [handleSearch, isActive]);

  return { ...testMutation, handleSearch };
};

export { use2FQrcode };
export type { Use2FQrcodeRes, Use2FQrcodeProps };
