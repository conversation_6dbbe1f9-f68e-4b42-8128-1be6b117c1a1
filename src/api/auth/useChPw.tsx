// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// store
import { useUserStore } from '@/store';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type ChPwRes = {};
type ChPwProps = {
  currentPassword: string;
  newPassword: string;
};
type Other = {};

const useChPw = (useProps: UseTestMutationProps<ChPwRes, ChPwProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();
  const { loginRes } = useUserStore();

  const testMutation = useTestMutation<ChPwRes, ChPwProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/login-password/change', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.query.info(loginRes?.token) });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useChPw };
export type { ChPwRes, ChPwProps };
