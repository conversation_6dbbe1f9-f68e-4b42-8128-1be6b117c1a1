// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type SetMasterDetailRes = {};
type SetMasterDetailProps = {
  merchantId: number;
  operationPassword: string;
  allowedDepositTolerance: number;
  depositTimeoutMinutes: number;
  maxMerchantUsers: number;
  maxPendingDepositOrders: number;
  maxUserPendingDepositOrders: number;
};
type Other = {};

const useSetMasterDetail = (useProps: UseTestMutationProps<SetMasterDetailRes, SetMasterDetailProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useSetMasterDetail');

  const testMutation = useTestMutation<SetMasterDetailRes, SetMasterDetailProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant/set/master', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useSetMasterDetail };
export type { SetMasterDetailRes, SetMasterDetailProps };
