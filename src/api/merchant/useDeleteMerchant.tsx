// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type DeleteMerchantRes = {};
type DeleteMerchantProps = {
  merchantId: number;
  operationPassword: string;
};
type Other = {};

const useDeleteMerchant = (useProps: UseTestMutationProps<DeleteMerchantRes, DeleteMerchantProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useDeleteMerchant');

  const testMutation = useTestMutation<DeleteMerchantRes, DeleteMerchantProps>({
    ...config,
    mutationFn: ({ merchantId, operationPassword }) => {
      const request = axiosRoot
        .delete(`/V1.0/merchant/${merchantId}`, {
          data: { operationPassword },
        })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useDeleteMerchant };
export type { DeleteMerchantRes, DeleteMerchantProps };
