// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type CreateMerchantRes = {
  merchantId: number;
};
type CreateMerchantProps = {
  customerId: number;
  merchantName: string;
  operationPassword: string;
};
type Other = {};

const useCreateMerchant = (useProps: UseTestMutationProps<CreateMerchantRes, CreateMerchantProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<CreateMerchantRes, CreateMerchantProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreateMerchant };
export type { CreateMerchantRes, CreateMerchantProps };
