// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type AddMerchantCsRes = {};
type AddMerchantCsProps = {
  userName: string;
  password: string;
  nickName: string;
  merchantId: number;
};
type Other = {};

const useAddMerchantCs = (useProps: UseTestMutationProps<AddMerchantCsRes, AddMerchantCsProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<AddMerchantCsRes, AddMerchantCsProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/merchant/admin', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useAddMerchantCs };
export type { AddMerchantCsRes, AddMerchantCsProps };
