// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type SetMerchantStatusRes = {
  merchantId: number;
  transactionStatus: number;
};
type SetMerchantStatusProps = {
  merchantId: number;
  newStatus: number;
  operationPassword: string;
};
type Other = {};

const useSetMerchantStatus = (useProps: UseTestMutationProps<SetMerchantStatusRes, SetMerchantStatusProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<SetMerchantStatusRes, SetMerchantStatusProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant/set/status', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      queryClient.invalidateQueries({ queryKey: queryKeys.query.tronMaster });
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useSetMerchantStatus };
export type { SetMerchantStatusRes, SetMerchantStatusProps };
