// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type SetFeeDetailRes = {};
type SetFeeDetailProps = {
  merchantId: number;
  feeDetails: {
    fixedFee: number;
    percentageFee: number;
    depositFee: number;
    withdrawalFee: number;
  };
  operationPassword: string;
};
type Other = {};

const useSetFeeDetail = (useProps: UseTestMutationProps<SetFeeDetailRes, SetFeeDetailProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useSetFeeDetail');

  const testMutation = useTestMutation<SetFeeDetailRes, SetFeeDetailProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant/set/fee', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useSetFeeDetail };
export type { SetFeeDetailRes, SetFeeDetailProps };
