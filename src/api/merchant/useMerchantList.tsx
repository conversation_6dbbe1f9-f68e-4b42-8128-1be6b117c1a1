// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import { CryptoEnum } from '@/utils';
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type MerchantListRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<MerchantInfo>;
};
type MerchantListProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: number;
  OrderByDescending?: number;
  merchantNumber?: number;
  CustomerName?: string;
  HasPaymentAddress?: number;
  HasReceiveAddress?: number;
};
type Other = {};

// 商戶資訊介面
interface MerchantInfo {
  id: number; // 商戶唯一標識符
  merchantNumber: number; // 商戶號，可能是用於外部識別的唯一編號
  merchantName: string; // 商戶名稱
  customerName: string; // 客戶名稱，可能與商戶相關聯的客戶識別名稱
  enabled: boolean;
  transactionStatus: number;
  isOrderLimitReached: boolean; // 是否達到訂單限制
  pendingDepositCount: number; // 待處理的入金訂單數量
  userCount: number; // 關聯的用戶數量
  ipWhitelist: MerchantIpWhiteListInfo;
  wallet: MerchantWalletInfo; // 商戶的錢包資訊
  master: MerchantMasterInfo;
  fee: MerchantFeeInfo[]; // 費用設定，可能涉及多種加密貨幣的費率配置
  paymentAddress: MerchantPaymentAddressInfo[]; // 商戶用於付款的地址資訊
  receiveAddress: MerchantReceiveAddressInfo[]; // 商戶用於接收資金的地址資訊，目前為空
  createdAt: string; // 資料建立的時間戳（ISO 8601 格式）
  updatedAt: string; // 資料最後更新的時間戳（ISO 8601 格式）
}

// 錢包資訊介面
interface MerchantWalletInfo {
  balance: number; // 可用餘額
  lockedBalance: number; // 鎖定餘額，可能是正在處理的交易金額
}

interface MerchantMasterInfo {
  allowedDepositTolerance: number;
  depositTimeoutMinutes: number;
  maxMerchantUsers: number; // 最大允許的待處理入金訂單數量
  maxPendingDepositOrders: number; // 最大允許的待處理入金訂單數量
  maxUserPendingDepositOrders: number;
}

interface MerchantIpWhiteListInfo {
  enableIpWhitelist: boolean;
  ipWhitelistCount: number;
}
// 費用資訊介面
interface MerchantFeeInfo {
  cryptoType: CryptoEnum; // 加密貨幣類型，數字可能對應不同的幣種（例如，1 表示 Bitcoin）
  fixedFee: number; // 會員出金
  percentageFee: number; // 手續費百分比（例如，0.003 表示 0.3%）
  depositFee: number; // 入金手續費
  withdrawalFee: number; // 提款手續費
}

// 收款地址資訊介面
interface MerchantReceiveAddressInfo {
  networkType: number;
  addresses: string; // 錢包地址
}

// 付款地址資訊介面
interface MerchantPaymentAddressInfo {
  networkType: number;
  addresses: string; // 錢包地址
}

const useMerchantList = (useProps: UseTestQueryProps<Other, MerchantListProps>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<MerchantListRes, MerchantListProps>({
    ...config,
    queryKey: queryKeys.query.merchantList(params),
    qf: () => {
      const request = axiosRoot.get<MerchantListRes>('/V1.0/merchant', { params }).then(({ data }) => data);

      return request;
    },
    staleTime: Infinity,
  });

  return testQuery;
};

export { useMerchantList };
export type {
  MerchantReceiveAddressInfo,
  MerchantListRes,
  MerchantListProps,
  MerchantInfo,
  MerchantWalletInfo,
  MerchantFeeInfo,
  MerchantPaymentAddressInfo,
  MerchantMasterInfo,
  MerchantIpWhiteListInfo,
};
