// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type AddMerchantAdminRes = {};
type AddMerchantAdminProps = {
  userName: string;
  password: string;
  nickName: string;
  merchantId: number;
};
type Other = {};

const useAddMerchantAdmin = (useProps: UseTestMutationProps<AddMerchantAdminRes, AddMerchantAdminProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<AddMerchantAdminRes, AddMerchantAdminProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/merchant/admin', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useAddMerchantAdmin };
export type { AddMerchantAdminRes, AddMerchantAdminProps };
