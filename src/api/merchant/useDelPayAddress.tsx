// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type DelPayAddressRes = {};
type DelPayAddressProps = {
  address: string;
  operationPassword: string;
};
type Other = {};

const useDelPayAddress = (useProps: UseTestMutationProps<DelPayAddressRes, DelPayAddressProps, Other>) => {
  const queryClient = useQueryClient();
  // props
  const { onSuccess, ...config } = useProps;

  const testMutation = useTestMutation<DelPayAddressRes, DelPayAddressProps>({
    ...config,
    mutationFn: (params) => {
      const request = axiosRoot.delete('/V1.0/merchant/trc20/receiveAddress', { params }).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.getAllMerchantListKeys().forEach((eachMcxKeys) => {
        queryClient.invalidateQueries({ queryKey: eachMcxKeys });
      });

      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useDelPayAddress };
export type { DelPayAddressRes, DelPayAddressProps };
