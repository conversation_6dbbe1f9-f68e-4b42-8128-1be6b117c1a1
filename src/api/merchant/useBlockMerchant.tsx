// libs
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type BlockMerchantRes = {};
type BlockMerchantProps = {
  merchantId: number;
  operationPassword: string;
};
type Other = {};

const useBlockMerchant = (useProps: UseTestMutationProps<BlockMerchantRes, BlockMerchantProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { t } = useTranslation('useBlockMerchant');

  const testMutation = useTestMutation<BlockMerchantRes, BlockMerchantProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant/block/toggle', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
    },
    successNotify: { title: 'UXM Settlement Corp', des: t('successDescription') },
    skipLogger: true,
  });

  return testMutation;
};

export { useBlockMerchant };
export type { BlockMerchantRes, BlockMerchantProps };
