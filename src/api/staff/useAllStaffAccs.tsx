// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type StaffAccOptions = {
  appUserId: string;
  id: number;
  userName: string;
  nickName: string;
  operationPasswordEnabled: boolean;
  twoFactorEnabled: boolean;
  roles: string[];
  createdAt: string;
};

type AllStaffAccsRes = {
  currentPage: number;
  items: Array<StaffAccOptions>;
  pageSize: number;
  totalCount: number;
  totalPage: number;
};

type AllStaffAccsProps = {
  PageSize?: number | null;
  PageNumber?: number | null;
  OrderBy?: 'Id' | null;
  OrderByDescending?: boolean | null;
  AppUserId: string | null | undefined;
  NickName: string | null | undefined;
  UserName: string | null | undefined;
};

type Other = {};

const useAllStaffAccs = (useProps: UseTestQueryProps<Other, AllStaffAccsProps, AllStaffAccsRes>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<AllStaffAccsRes, AllStaffAccsProps>({
    ...config,
    queryKey: queryKeys.query.allStaffAccs(params),
    qf: () => {
      const request = axiosRoot.get('/V1.0/account/system/all', { params }).then(({ data }) => data);

      return request;
    },
    gcTime: 1000 * 60 * 5,
    staleTime: 1000 * 5,
  });

  return { ...testQuery };
};

export { useAllStaffAccs };
export type { AllStaffAccsRes, AllStaffAccsProps, StaffAccOptions };
