// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type StaffListRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<StaffInterface>;
};
type StaffListProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: number;
  OrderByDescending?: number;
};
type Other = {};

interface StaffInterface {}

const useStaffList = (useProps: UseTestQueryProps<Other, StaffListProps>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<StaffListRes, StaffListProps>({
    ...config,
    queryKey: ['staff', 'list', ...Object.values(params || {})],
    qf: () => {
      const request = axiosRoot.get('/V1.0/staff', { params }).then(({ data }) => data);

      return request;
    },
    staleTime: Infinity,
    onTest: () => {
      const res: StaffListRes = {
        currentPage: 0,
        totalPages: 0,
        pageSize: 0,
        totalCount: 0,
        items: [],
      };

      return Promise.resolve(res);
    },
  });

  return testQuery;
};

export { useStaffList };
export type { StaffListRes, StaffListProps, StaffInterface };
