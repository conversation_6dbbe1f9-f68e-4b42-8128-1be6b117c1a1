// hooks
import { UseTestMutationProps, useTestMutation } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type CreateAdminRes = {};
type CreateAdminProps = {
  userName: string;
  password: string;
  nickName: string;
};
type Other = {};

const useCreateAdmin = (useProps: UseTestMutationProps<CreateAdminRes, CreateAdminProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<CreateAdminRes, CreateAdminProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/Account/admin', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreateAdmin };
export type { CreateAdminRes, CreateAdminProps };
