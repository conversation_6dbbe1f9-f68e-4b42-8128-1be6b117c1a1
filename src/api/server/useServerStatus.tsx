// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import { ServerStatusEnum, myFactory } from '@/utils';

interface IServerStatusRow {
  key: number;
  description: string;
  block: string;
  delta: string;
  status: ServerStatusEnum;
}
type ServerStatusRes = Array<IServerStatusRow>;
type ServerStatusProps = {};
type Other = {};

const useServerStatus = (useProps: UseTestQueryProps<Other, ServerStatusProps>) => {
  // props
  const { ...config } = useProps;

  const testQuery = useTestQuery<ServerStatusRes, ServerStatusProps>({
    ...config,
    queryKey: ['server', 'status'],
    qf: (params) => {
      const request = axiosRoot.get('/V1.0/server/status', { params }).then(({ data }) => data);

      return request;
    },
    delay: 1000,
    staleTime: 1000 * 60 * 60,
    onTest: () => {
      const res: ServerStatusRes = [
        {
          key: 0,
          description: '2024-08-13 00:00:00 Main Net',
          block: '66812345',
          delta: '-11',
          status: myFactory.helpers.arrayElement([
            ServerStatusEnum.Delay,
            ServerStatusEnum.Disconnected,
            ServerStatusEnum.Synchronous,
          ]),
        },
        {
          key: 1,
          description: 'Synced',
          block: '66812456',
          delta: '0',
          status: myFactory.helpers.arrayElement([
            ServerStatusEnum.Delay,
            ServerStatusEnum.Disconnected,
            ServerStatusEnum.Synchronous,
          ]),
        },
        {
          key: 2,
          description: 'Server #1',
          block: '66811281',
          delta: '0',
          status: myFactory.helpers.arrayElement([
            ServerStatusEnum.Delay,
            ServerStatusEnum.Disconnected,
            ServerStatusEnum.Synchronous,
          ]),
        },
        {
          key: 3,
          description: 'Server #2',
          block: '-',
          delta: '-6681237',
          status: myFactory.helpers.arrayElement([
            ServerStatusEnum.Delay,
            ServerStatusEnum.Disconnected,
            ServerStatusEnum.Synchronous,
          ]),
        },
      ];

      return Promise.resolve(res);
    },
  });

  return testQuery;
};

export { useServerStatus };
export type { ServerStatusRes, ServerStatusProps };
