// hooks
import { useTestQuery, UseTestQueryProps } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type Props = {
  transactionHash: string | undefined;
};

type TransactionDetailRes = {
  isConfirm: boolean;
  receipt: {
    timeStamp: number;
    confirmTime: string;
    confirmTimeUtc: string;
    cryptoType: string;
    hash: string;
    error: string | null;
    from: string;
    to: string;
    amount: string;
    gas: string;
    energy: number;
    bandwidth: number;
    result: boolean;
    contractType: string;
    isBandwidthInsufficient: boolean;
    isEnergyInsufficient: boolean;
  };
};

const useTransactionDetail = (useProps: UseTestQueryProps<{}, Props>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<TransactionDetailRes, Props>({
    ...config,
    queryKey: queryKeys.query.transactionDetail(params?.transactionHash),
    qf: () => {
      const request = axiosRoot
        .get(`/V1.0/tron/transaction/${params?.transactionHash}`, { params })
        .then(({ data }) => data);

      return request;
    },
    staleTime: 1000 * 60 * 5,
  });

  return testQuery;
};

export { useTransactionDetail };
export type { TransactionDetailRes };
