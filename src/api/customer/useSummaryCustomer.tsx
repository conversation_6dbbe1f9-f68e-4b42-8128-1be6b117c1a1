// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

type CustomerDataProps = {
  startDate?: string;
  endDate?: string;
  currency?: string;
  merchant?: string;
};

interface CustomerData {
  customerId: number;
  customerName: string;
  merchantCount: number;
  balance: number;
  lockedBalance: number;
  createdAt: string;
  memberDepositCount: number;
  memberDepositAmount: number;
  memberDepositFee: number;
  memberDepositGas: number;
  memberWithdrawalCount: number;
  memberWithdrawalAmount: number;
  memberWithdrawalFee: number;
  memberWithdrawalGas: number;
  merchantDepositCount: number;
  merchantDepositAmount: number;
  merchantDepositFee: number;
  merchantDepositGas: number;
  merchantWithdrawalCount: number;
  merchantWithdrawalAmount: number;
  merchantWithdrawalFee: number;
  merchantWithdrawalGas: number;
}

type Other = {};

const useSummaryCustomer = (useProps: UseTestQueryProps<Other, CustomerDataProps>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<CustomerData[], CustomerDataProps>({
    ...config,
    queryKey: ['summary', 'customer', ...Object.values(params || {})],
    qf: () => {
      const request = axiosRoot.get<CustomerData[]>('/V1.0/customer/summary', { params }).then(({ data }) => data);

      return request;
    },
    staleTime: Infinity,
    enabled: !!params?.startDate && !!params.endDate && !!params.currency,
  });

  return testQuery;
};

export { useSummaryCustomer };
export type { CustomerData, CustomerDataProps };
