// hooks
import { UseTestQueryProps, useTestQuery } from '@/hooks';

// utils
import axiosRoot from '@/utils/axiosRoot';

interface MerchantDetail {
  merchantId: number;
  merchantName: string;
  merchantNumber: string;
  memberDepositCount: number;
  memberDepositAmount: number;
  memberDepositFee: number;
  memberDepositGas: number;
  memberWithdrawalCount: number;
  memberWithdrawalAmount: number;
  memberWithdrawalFee: number;
  memberWithdrawalGas: number;
  merchantDepositCount: number;
  merchantDepositAmount: number;
  merchantDepositFee: number;
  merchantDepositGas: number;
  merchantWithdrawalCount: number;
  merchantWithdrawalAmount: number;
  merchantWithdrawalFee: number;
  merchantWithdrawalGas: number;
  transferOutFee?: number;
  abnormalTransferCount?: number;
  outflowTransactionCount?: number;
  internalTransferCount?: number;
}

type CustomerDetailsProps = {
  customerId: number;
};

const useCustomerDetails = (useProps: UseTestQueryProps<unknown, CustomerDetailsProps>) => {
  // props
  const { enabled, params, ...config } = useProps;

  const testQuery = useTestQuery<MerchantDetail[], CustomerDetailsProps>({
    ...config,
    queryKey: ['summary', 'customer', 'details', ...Object.values(params || {})],
    qf: () => {
      if (!params?.customerId) {
        throw new Error('customerId is required');
      }

      const request = axiosRoot
        .get<MerchantDetail[]>('/V1.0/customer/summary/details', { params })
        .then(({ data }) => data);

      return request;
    },
    staleTime: Infinity,
    enabled: enabled !== false && !!params?.customerId,
  });

  return testQuery;
};

export { useCustomerDetails };
export type { MerchantDetail, CustomerDetailsProps };
