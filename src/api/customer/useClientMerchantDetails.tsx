import { UseQueryResult, useQueries } from '@tanstack/react-query';
import axiosRoot from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';
import { MerchantDetail } from './useSummaryCustomerDetail';

type ClientsMerchantDetailsProps = {
  customerIds: Array<number>;
};

type ClientsMerchantDetailsRes = Array<{
  clientId: number;
  merchantDetails: MerchantDetail[];
}>;

const useClientMerchantDetails = (useProps: ClientsMerchantDetailsProps) => {
  // props
  const { customerIds, ...config } = useProps;

  const results = useQueries<ClientsMerchantDetailsRes>({
    queries: customerIds.map((id) => ({
      queryKey: queryKeys.query.clientMerchantDetails(id),
      queryFn: () => {
        const request = axiosRoot
          .get<MerchantDetail>('/V1.0/customer/summary/details', { params: { customerId: id } })
          .then(({ data }) => {
            return {
              clientId: id,
              merchantDetails: data,
            };
          });

        return request;
      },
      staleTime: 1000 * 60 * 30,
    })),
    ...config,
  });

  return results;
};

export { useClientMerchantDetails };
export type { ClientsMerchantDetailsProps, ClientsMerchantDetailsRes };
export type ClientMerchantDetailsResult = UseQueryResult<ClientsMerchantDetailsRes['0']>;
