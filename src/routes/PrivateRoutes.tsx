// libs
import { lazy } from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';

// pages
import SupervisorOrders from '@/pages/supervisor/SupervisorOrders';
import MerchantWithdraw from '@/pages/withdraw/MerchantWithdraw';

const PrivateLayout = lazy(() => import('../layouts/PrivateLayout'));
const Overview = lazy(() => import('../pages/overview/Overview'));
const Deposit = lazy(() => import('../pages/deposit/Deposit'));
const Withdraw = lazy(() => import('../pages/withdraw/Withdraw'));
const AbnormalDeposit = lazy(() => import('../pages/ab_dep/AbnormalDeposit'));
const Admin = lazy(() => import('../pages/admin/Admin'));
const User = lazy(() => import('../pages/user/User'));
const Shift = lazy(() => import('../pages/shift/Shift'));
const WalletRoutes = lazy(() => import('../pages/wallet/WalletRoutes'));
const Lease = lazy(() => import('../pages/lease/Lease'));

const isTestDev = true;
const devTestNav = 'private/wallet';

interface IPrivateRoutesProps {}

const PrivateRoutes: React.FunctionComponent<IPrivateRoutesProps> = (props) => {
  // props
  const {} = props || {};

  return (
    <Routes>
      {/* Private */}
      <Route
        path='/private'
        element={<PrivateLayout />}
      >
        <Route
          index
          element={<Overview />}
        />
        <Route
          path='deposit'
          element={<Deposit />}
        />

        <Route
          path='withdraw'
          element={<Withdraw />}
        />
        <Route
          path='supervisor'
          element={<SupervisorOrders />}
        />
        <Route
          path='merchantOrder'
          element={<MerchantWithdraw />}
        />
        <Route
          path='wallet/*'
          element={<WalletRoutes />}
        />
        <Route
          path='abDep'
          element={<AbnormalDeposit />}
        />

        <Route
          path='admin'
          element={<Admin />}
        />
        <Route
          path='merchant'
          element={<MerchantWithdraw />}
        />
        <Route
          path='user'
          element={<User />}
        />
        <Route
          path='shift'
          element={<Shift />}
        />
        <Route
          path='lease'
          element={<Lease />}
        />
      </Route>

      {/* Capture */}
      <Route
        path='*'
        element={<Navigate to={import.meta.env.DEV && isTestDev ? devTestNav : '/private'} />}
      />
    </Routes>
  );
};

export default PrivateRoutes;
