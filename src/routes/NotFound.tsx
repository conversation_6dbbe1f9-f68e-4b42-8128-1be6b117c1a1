// libs
import { But<PERSON> } from 'antd';
import { useNavigate } from 'react-router-dom';

interface INotFoundProps {}

const NotFound: React.FunctionComponent<INotFoundProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const navigator = useNavigate();

  return (
    <>
      <h3>Not found this page</h3>
      <hr />
      <Button onClick={() => navigator('/')}>返回總覽</Button>
    </>
  );
};

export default NotFound;
