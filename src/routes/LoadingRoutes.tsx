// libs
import { Skeleton } from 'antd';
import { Routes, Route } from 'react-router-dom';

// store
import { useUserStore } from '@/store';

interface ILoadingRoutesProps {}

const LoadingRoutes: React.FunctionComponent<ILoadingRoutesProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { isDark } = useUserStore();

  return (
    <Routes>
      <Route
        path='*'
        element={
          <div
            className={`
              flex h-screen w-full flex-col justify-center space-y-8 p-16
              ${isDark ? 'bg-neutral-800' : 'bg-green-200'}
            `}
          >
            <Skeleton active />
            <Skeleton active />
            <Skeleton active />
            <Skeleton active />
          </div>
        }
      />
    </Routes>
  );
};

export default LoadingRoutes;
