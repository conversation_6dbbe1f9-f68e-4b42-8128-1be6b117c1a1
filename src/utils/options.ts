// prettier-ignore
import {
  AnomalyResolutionStatusEnum,
  ApplyTypeEnum, ClientEnvironmentEnum, CountryKeyNum, CryptoCurrencyEnum, CryptoEnum, CryptoProtocolEnum, CurrencyNum, eNumEntities, IDNeNum, IDTypeNum, InstrumentTradeStatus, LeaseInOrderStatusEnum, LeaseOutOrderStatusEnum, LevelEnum, LimitTimeInterval, MerchantTransactionStatus, OvTypeEnum, PlatformEnum, ProductCategoryEnum, ReviewStatusNum, SaleStatusEnum, ShiftNameEnum, TronMasterStatusEnum, TronWalletFuncEnum, TronWalletStatusEnum, TxCategoryNum, TxStatusNum, VerifyStatusNum
} from './enums';

type OptionValue = React.Key;
type OptionBase = { label: string; value: OptionValue };
type EnumObjBase<T extends Record<string, string | number>, Other = object> = Record<
  keyof T,
  { label: string; value: ValueOf<T> } & Other
>;

// 驗證狀態
const verifyStatusOptions = eNumEntities<typeof VerifyStatusNum>(VerifyStatusNum).values.map((enumValue) => {
  const result = { value: enumValue, label: '未定義', textColor: '' };
  if (enumValue === VerifyStatusNum.NeedReUpload) {
    result.label = '需要重新上傳';
    result.textColor = 'text-orange-600';
  }
  if (enumValue === VerifyStatusNum.UnSet) {
    result.label = '尚未上傳';
    result.textColor = 'text-orange-600';
  }
  if (enumValue === VerifyStatusNum.Verifing) {
    result.label = '驗證中';
    result.textColor = 'text-orange-600';
  } else if (enumValue === VerifyStatusNum.Pass) {
    result.label = '驗證通過';
    result.textColor = 'text-green-600';
  } else if (enumValue === VerifyStatusNum.Fail) {
    result.label = '驗證失敗';
    result.textColor = 'text-red-600';
  }
  return result;
});

// 客人環境選項
const clientEnvObj: EnumObjBase<typeof ClientEnvironmentEnum, {}> = {
  Formal: {
    label: 'formalClientEnvironment',
    value: ClientEnvironmentEnum.Formal,
  },
  Test: {
    label: 'testClientEnvironment',
    value: ClientEnvironmentEnum.Test,
  },
};
const clientEnvOptions = eNumEntities<typeof ClientEnvironmentEnum>(ClientEnvironmentEnum).keys.map((enumKey) => {
  const { value = ClientEnvironmentEnum[enumKey], label = 'undefined' } = clientEnvObj[enumKey];
  return { value, label };
});

// shift Name enum options
const shiftNameObj: EnumObjBase<typeof ShiftNameEnum, {}> = {
  Morning: {
    label: 'morningShiftName',
    value: ShiftNameEnum.Morning,
  },
  Afternoon: {
    label: 'afternoonShiftName',
    value: ShiftNameEnum.Afternoon,
  },
  Night: {
    label: 'nightShiftName',
    value: ShiftNameEnum.Night,
  },
};

const ShiftNameOptions = eNumEntities<typeof ShiftNameEnum>(ShiftNameEnum).keys.map((enumKey) => {
  const { value = ShiftNameEnum[enumKey], label = 'undefined' } = shiftNameObj[enumKey];
  return { value, label };
});

// Tron Master status enum options
const tronMasterStatusObj: EnumObjBase<typeof TronMasterStatusEnum, {}> = {
  Normal: {
    label: 'normalTronMasterStatus',
    value: TronMasterStatusEnum.Normal,
  },
  DepositOnly: {
    label: 'depositOnlyTronMasterStatus',
    value: TronMasterStatusEnum.DepositOnly,
  },
  WithdrawalOnly: {
    label: 'withdrawalOnlyTronMasterStatus',
    value: TronMasterStatusEnum.WithdrawalOnly,
  },
  Suspended: {
    label: 'suspendedTronMasterStatus',
    value: TronMasterStatusEnum.Suspended,
  },
};
const tronMasterStatusOptions = eNumEntities<typeof TronMasterStatusEnum>(TronMasterStatusEnum).keys.map((enumKey) => {
  const { value = TronMasterStatusEnum[enumKey], label = 'undefined' } = tronMasterStatusObj[enumKey];
  return { value, label };
});

// Tron Wallet status enum options
const tronWalletStatusObj: EnumObjBase<typeof TronWalletStatusEnum, {}> = {
  Available: {
    label: 'availableTronWalletStatus',
    value: TronWalletStatusEnum.Available,
  },
  Transaction: {
    label: 'transactionTronWalletStatus',
    value: TronWalletStatusEnum.Transaction,
  },
  Cooldown: {
    label: 'cooldownTronWalletStatus',
    value: TronWalletStatusEnum.Cooldown,
  },
};
const tronWalletStatusOptions = eNumEntities<typeof TronWalletStatusEnum>(TronWalletStatusEnum).keys.map((enumKey) => {
  const { value = TronWalletStatusEnum[enumKey], label = 'undefined' } = tronWalletStatusObj[enumKey];
  return { value, label };
});

// Tron Wallet function type enum options
const tronWalletFuncObj: EnumObjBase<typeof TronWalletFuncEnum, {}> = {
  Collection: {
    label: 'collectionTronWalletFunc',
    value: TronWalletFuncEnum.Collection,
  },
  Stake: {
    label: 'stakeTronWalletFunc',
    value: TronWalletFuncEnum.Stake,
  },
  MerchantPaymentWallet: {
    label: 'merchantPaymentWalletTronWalletFunc',
    value: TronWalletFuncEnum.MerchantPaymentWallet,
  },
  SupervisorWallet: {
    label: 'supervisorWalletTronWalletFunc',
    value: TronWalletFuncEnum.SupervisorWallet,
  },
  EnergyLease: {
    label: 'energyLeaseWalletTronWalletFunc',
    value: TronWalletFuncEnum.EnergyLease,
  },
};
const tronWalletFuncOptions = eNumEntities<typeof TronWalletFuncEnum>(TronWalletFuncEnum).keys.map((enumKey) => {
  const { value = TronWalletFuncEnum[enumKey], label = 'undefined' } = tronWalletFuncObj[enumKey];
  return { value, label };
});

// 限制時間區間選項
const limittiObj: EnumObjBase<typeof LimitTimeInterval, {}> = {
  ThirtyMins: {
    label: 'thirtyMinsLimitTimeInterval',
    value: LimitTimeInterval.ThirtyMins,
  },
  OneH: {
    label: 'oneHLimitTimeInterval',
    value: LimitTimeInterval.OneH,
  },
  TwoH: {
    label: 'twoHLimitTimeInterval',
    value: LimitTimeInterval.TwoH,
  },
};
const limittiOptions = eNumEntities<typeof LimitTimeInterval>(LimitTimeInterval).keys.map((enumKey) => {
  const { value = LimitTimeInterval[enumKey], label = 'undefined' } = limittiObj[enumKey];
  return { value, label };
});

// 國家名稱
const countryEnumObj: EnumObjBase<typeof CountryKeyNum, { key: string }> = {
  Taiwan: {
    label: '台灣',
    value: CountryKeyNum.Taiwan,
    key: 'tw',
  },
  Honkon: {
    label: '香港',
    value: CountryKeyNum.Honkon,
    key: 'hk',
  },
};
const countryOptions = eNumEntities<typeof CountryKeyNum>(CountryKeyNum).keys.map((enumKey) => {
  const { value = CountryKeyNum[enumKey], label = '未定義', key = '' } = countryEnumObj[enumKey];
  return { value, label, key };
});

const leaseInOrderStatusEnumObj: EnumObjBase<typeof LeaseInOrderStatusEnum, {}> = {
  Pending: {
    label: 'pendingLeaseInOrderStatusEnum',
    value: LeaseInOrderStatusEnum.Pending,
  },
  Transferring: {
    label: 'transferringLeaseInOrderStatusEnum',
    value: LeaseInOrderStatusEnum.Transferring,
  },
  TransferSuccess: {
    label: 'transferSuccessLeaseInOrderStatusEnum',
    value: LeaseInOrderStatusEnum.TransferSuccess,
  },
  EnergyReceived: {
    label: 'energyReceivedLeaseInOrderStatusEnum',
    value: LeaseInOrderStatusEnum.EnergyReceived,
  },
  TransferFailed: {
    label: 'transferFailedLeaseInOrderStatusEnum',
    value: LeaseInOrderStatusEnum.TransferFailed,
  },
};
const leaseInOrderStatusOptions = eNumEntities<typeof LeaseInOrderStatusEnum>(LeaseInOrderStatusEnum).keys.map(
  (enumKey) => {
    const { value = LeaseInOrderStatusEnum[enumKey], label = 'undefined' } = leaseInOrderStatusEnumObj[enumKey];
    return { value, label };
  },
);

const leaseOutOrderStatusEnumObj: EnumObjBase<typeof LeaseOutOrderStatusEnum, {}> = {
  Received: {
    label: 'receivedLeaseOutOrderStatusEnum',
    value: LeaseOutOrderStatusEnum.Received,
  },
  Delegating: {
    label: 'delegatingLeaseOutOrderStatusEnum',
    value: LeaseOutOrderStatusEnum.Delegating,
  },
  DelegateSuccess: {
    label: 'delegateSuccessLeaseOutOrderStatusEnum',
    value: LeaseOutOrderStatusEnum.DelegateSuccess,
  },
  DelegateFail: {
    label: 'delegateFailLeaseOutOrderStatusEnum',
    value: LeaseOutOrderStatusEnum.DelegateFail,
  },
  Reclaiming: {
    label: 'reclaimingLeaseOutOrderStatusEnum',
    value: LeaseOutOrderStatusEnum.Reclaiming,
  },
  ReclaimSuccess: {
    label: 'reclaimSuccessLeaseOutOrderStatusEnum',
    value: LeaseOutOrderStatusEnum.ReclaimSuccess,
  },
  ReclaimFail: {
    label: 'reclaimFailLeaseOutOrderStatusEnum',
    value: LeaseOutOrderStatusEnum.ReclaimFail,
  },
  InvalidTrxAmount: {
    label: 'invalidTrxAmountLeaseOutOrderStatusEnum',
    value: LeaseOutOrderStatusEnum.InvalidTrxAmount,
  },
  InsufficientEnergy: {
    label: 'insufficientEnergyLeaseOutOrderStatusEnum',
    value: LeaseOutOrderStatusEnum.InsufficientEnergy,
  },
};
const leaseOutOrderStatusOptions = eNumEntities<typeof LeaseOutOrderStatusEnum>(LeaseOutOrderStatusEnum).keys.map(
  (enumKey) => {
    const { value = LeaseOutOrderStatusEnum[enumKey], label = 'undefined' } = leaseOutOrderStatusEnumObj[enumKey];
    return { value, label };
  },
);

// 訂單狀態
const txStatusEnumObj: EnumObjBase<typeof TxStatusNum, {}> = {
  Created: {
    label: 'createdTxStatus',
    value: TxStatusNum.Created,
  },
  Broadcasted: {
    label: 'broadcastedTxStatus',
    value: TxStatusNum.Broadcasted,
  },
  Confirmed: {
    label: 'confirmedTxStatus',
    value: TxStatusNum.Confirmed,
  },
  Completed: {
    label: 'completedTxStatus',
    value: TxStatusNum.Completed,
  },
  Canceled: {
    label: 'canceledTxStatus',
    value: TxStatusNum.Canceled,
  },
  Timeout: {
    label: 'timeoutTxStatus',
    value: TxStatusNum.Timeout,
  },
  Retry: {
    label: 'retryTxStatus',
    value: TxStatusNum.Retry,
  },
  MerchantCallbackFailed: {
    label: 'merchantCallbackFailedTxStatus',
    value: TxStatusNum.MerchantCallbackFailed,
  },
  BlockchainTransactionFailed: {
    label: 'blockchainTransactionFailedTxStatus',
    value: TxStatusNum.BlockchainTransactionFailed,
  },
  WaitingForApproval: {
    label: 'waitingForApprovalTxStatus',
    value: TxStatusNum.WaitingForApproval,
  },
};

const txStatusOptions = eNumEntities<typeof TxStatusNum>(TxStatusNum).keys.map((enumKey) => {
  const { value = TxStatusNum[enumKey], label = 'undefined' } = txStatusEnumObj[enumKey];
  return { value, label };
});

const merchantTxStatusEnumObj: EnumObjBase<typeof MerchantTransactionStatus, {}> = {
  Retry: {
    label: 'retryReviewMerchantTransactionStatus',
    value: MerchantTransactionStatus.Retry,
  },
  Created: {
    label: 'createdMerchantTransactionStatus',
    value: MerchantTransactionStatus.Created,
  },
  BlockchainBroadcast: {
    label: 'blockchainBroadcastMerchantTransactionStatus',
    value: MerchantTransactionStatus.BlockchainBroadcast,
  },
  BlockchainConfirmed: {
    label: 'blockchainConfirmedMerchantTransactionStatus',
    value: MerchantTransactionStatus.BlockchainConfirmed,
  },
  Completed: {
    label: 'completedMerchantTransactionStatus',
    value: MerchantTransactionStatus.Completed,
  },
  Canceled: {
    label: 'canceledMerchantTransactionStatus',
    value: MerchantTransactionStatus.Canceled,
  },
  Timeout: {
    label: 'timeoutMerchantTransactionStatus',
    value: MerchantTransactionStatus.Timeout,
  },
  CallbackFailed: {
    label: 'callbackMerchantTransactionStatus',
    value: MerchantTransactionStatus.CallbackFailed,
  },
  BlockchainFailed: {
    label: 'blockchainFailedMerchantTransactionStatus',
    value: MerchantTransactionStatus.BlockchainFailed,
  },
  OtherError: {
    label: 'otherErrorMerchantTransactionStatus',
    value: MerchantTransactionStatus.OtherError,
  },
};

const merchantTxStatusOptions = eNumEntities<typeof MerchantTransactionStatus>(MerchantTransactionStatus).keys.map(
  (enumKey) => {
    const { value = MerchantTransactionStatus[enumKey], label = 'undefined' } = merchantTxStatusEnumObj[enumKey];
    return { value, label };
  },
);

// 銷量狀態
const salesVolumeStatusEnumObj: EnumObjBase<typeof SaleStatusEnum, {}> = {
  Pending: {
    label: '待定',
    value: SaleStatusEnum.Pending,
  },
  Settled: {
    label: '已結算',
    value: SaleStatusEnum.Settled,
  },
  Objection: {
    label: '有異議',
    value: SaleStatusEnum.Objection,
  },
};
const salesVolumeStatusOptions = eNumEntities<typeof SaleStatusEnum>(SaleStatusEnum).keys.map((enumKey) => {
  const { value = SaleStatusEnum[enumKey], label = '未定義' } = salesVolumeStatusEnumObj[enumKey];
  return { value, label };
});

// 虛擬貨幣類別
const cryptoTypesOptions = ['USDT', 'TRX', 'ETH'].map((mapC) => ({ label: mapC, value: mapC }));
const cryptoEnumObj: EnumObjBase<typeof CryptoEnum, { cryptoCurrency: CryptoTypes }> = {
  TRC20_USDT: {
    label: 'TRC20 USDT',
    value: CryptoEnum.TRC20_USDT,
    cryptoCurrency: 'USDT',
  },
  ERC20_USDT: {
    label: 'ERC20 USDT',
    value: CryptoEnum.ERC20_USDT,
    cryptoCurrency: 'USDT',
  },
};
const cryptoEnumOptions = eNumEntities<typeof CryptoEnum>(CryptoEnum).keys.map((mapKey) => {
  const { value = CryptoEnum[mapKey], label = '未定義', cryptoCurrency } = cryptoEnumObj[mapKey];
  return { value, label, cryptoCurrency };
});

const cryptoProtocolObj: EnumObjBase<typeof CryptoProtocolEnum, { currencies: Array<CryptoCurrencyEnum> }> = {
  TRC20: {
    label: 'TRC20',
    value: CryptoProtocolEnum.TRC20,
    currencies: [CryptoCurrencyEnum.USDT],
  },
};
const cryptoProtocolOptions = eNumEntities<typeof CryptoProtocolEnum>(CryptoProtocolEnum).keys.map((mapKey) => {
  const { value = CryptoProtocolEnum[mapKey], label = '未定義', currencies } = cryptoProtocolObj[mapKey];
  return { value, label, currencies };
});

const cryptoCurrencyObj: EnumObjBase<typeof CryptoCurrencyEnum, {}> = {
  USDT: {
    label: 'USDT',
    value: CryptoCurrencyEnum.USDT,
  },
};
const cryptoCurrencyOptions = eNumEntities<typeof CryptoCurrencyEnum>(CryptoCurrencyEnum).keys.map((mapKey) => {
  const { value = CryptoCurrencyEnum[mapKey], label = '未定義' } = cryptoCurrencyObj[mapKey];
  return { value, label };
});

// Anomaly resolution status option
const anomalyResolutionEnumObj: EnumObjBase<typeof AnomalyResolutionStatusEnum, {}> = {
  Pending: {
    label: 'pendingAnomalyResolutionStatus', // used for multi-languages; detailed in i18next/[locale]/options.ts
    value: AnomalyResolutionStatusEnum.Pending,
  },
  Completed: {
    label: 'completedAnomalyResolutionStatus', // used for multi-languages; detailed in i18next/[locale]/options.ts
    value: AnomalyResolutionStatusEnum.Completed,
  },
  InProgress: {
    label: 'inProgressAnomalyResolutionStatus', // used for multi-languages; detailed in i18next/[locale]/options.ts
    value: AnomalyResolutionStatusEnum.InProgress,
  },
  Failed: {
    label: 'failedAnomalyResolutionStatus', // used for multi-languages; detailed in i18next/[locale]/options.ts
    value: AnomalyResolutionStatusEnum.Failed,
  },
};
const anomalyResolutionOptions = eNumEntities<typeof AnomalyResolutionStatusEnum>(AnomalyResolutionStatusEnum).keys.map(
  (mapKey) => {
    const { value = AnomalyResolutionStatusEnum[mapKey], label = 'undefined' } = anomalyResolutionEnumObj[mapKey];
    return { value, label };
  },
);

// 交易類別
const txOptions = eNumEntities<typeof TxCategoryNum>(TxCategoryNum).values.map((enumValue) => {
  if (enumValue === TxCategoryNum.Deposit) return { value: enumValue, label: '入金' };
  if (enumValue === TxCategoryNum.Withdraw) return { value: enumValue, label: '出金' };
  if (enumValue === TxCategoryNum.Transfer) return { value: enumValue, label: '轉帳' };
  if (enumValue === TxCategoryNum.Commission) return { value: enumValue, label: '佣金' };
  return { value: enumValue, label: '未定義' };
});

// 幣別
const currencyOptions = eNumEntities<typeof CurrencyNum>(CurrencyNum).values.map((enumValue) => {
  if (enumValue === CurrencyNum.CNY) return { value: enumValue, label: 'CNY' };
  if (enumValue === CurrencyNum.TWD) return { value: enumValue, label: 'TWD' };
  if (enumValue === CurrencyNum.USD) return { value: enumValue, label: 'USD' };
  if (enumValue === CurrencyNum.USDT) return { value: enumValue, label: 'USDTF' };
  return { value: enumValue, label: '未定義' };
});

// 國碼
const idnEnumObj: EnumObjBase<
  typeof IDNeNum,
  { CNA: string; mask: string; countryCode: CountryKeyTypes; format: string }
> = {
  'US/CA': {
    value: IDNeNum['US/CA'],
    label: ` US/CA +${IDNeNum['US/CA']}`,
    CNA: 'US/CA',
    mask: '#'.repeat(10),
    format: '[(]###[)]###[-]####',
    countryCode: 'TW',
  },
  CN: {
    value: IDNeNum.CN,
    label: `CN +${IDNeNum.CN}`,
    CNA: 'US/CA',
    mask: '#'.repeat(10),
    format: '[(]###[)]###[-]####',
    countryCode: 'TW',
  },
  TW: {
    value: IDNeNum.TW,
    label: `TW +${IDNeNum.TW}`,
    CNA: 'US/CA',
    mask: '#'.repeat(11),
    format: '[(]###[)]########',
    countryCode: 'TW',
  },
  JP: {
    value: IDNeNum.JP,
    label: `JP +${IDNeNum.JP}`,
    CNA: 'US/CA',
    mask: '#'.repeat(10),
    format: '[(]###[)]###[-]####',
    countryCode: 'TW',
  },
  GB: {
    value: IDNeNum.GB,
    label: `GB +${IDNeNum.GB}`,
    CNA: 'US/CA',
    mask: '#'.repeat(10),
    format: '[(]###[)]###[-]####',
    countryCode: 'TW',
  },
  DE: {
    value: IDNeNum.DE,
    label: `DE +${IDNeNum.DE}`,
    CNA: 'US/CA',
    mask: '#'.repeat(10),
    format: '[(]###[)]###[-]####',
    countryCode: 'TW',
  },
  VN: {
    value: IDNeNum.VN,
    label: `VN +${IDNeNum.VN}`,
    CNA: 'US/CA',
    mask: '#'.repeat(10),
    format: '[(]###[)]###[-]####',
    countryCode: 'TW',
  },
};

const IDNOptions = eNumEntities<typeof IDNeNum>(IDNeNum).keys.map((enumKey) => {
  const { value = IDNeNum[enumKey], label = '未定義', ...other } = idnEnumObj[enumKey];
  return { value, label, ...other };
});
// 平台選項
const platformOptions = eNumEntities<typeof PlatformEnum>(PlatformEnum).values.map((enumValue) => {
  if (enumValue === PlatformEnum.K100U) return { value: enumValue, label: 'K100U' };
  if (enumValue === PlatformEnum.PT) return { value: enumValue, label: 'PT' };
  return { value: enumValue, label: '未定義' };
});

// 審核狀態
const reviewStatus = eNumEntities<typeof ReviewStatusNum>(ReviewStatusNum).values.map((enumValue) => {
  const result = { value: enumValue, label: '未定義' };
  if (enumValue === ReviewStatusNum.Pending) result.label = '待定';
  else if (enumValue === ReviewStatusNum.Finish) result.label = '完成';
  else if (enumValue === ReviewStatusNum.TurnDown) result.label = '駁回';
  return result;
});

// 總攬類別
const ovTypeOptions = eNumEntities<typeof OvTypeEnum>(OvTypeEnum).values.map((enumValue) => {
  const result = { value: enumValue, label: '未定義' };
  if (enumValue === OvTypeEnum.OnlineTrading) result.label = '線上交易';
  else if (enumValue === OvTypeEnum.AccountsNumber) result.label = '帳戶數量';
  else if (enumValue === OvTypeEnum.AccountBalance) result.label = '帳戶餘額';
  else if (enumValue === OvTypeEnum.UsersNationality) result.label = '用戶國籍';
  else if (enumValue === OvTypeEnum.UsersLevels) result.label = '層級人數';
  return result;
});

// 產品類別
const productOptions = eNumEntities<typeof ProductCategoryEnum>(ProductCategoryEnum).values.map((enumValue) => {
  const result = { value: enumValue, label: 'MT4' };
  if (enumValue === ProductCategoryEnum.MT5) result.label = 'MT5';
  else if (enumValue === ProductCategoryEnum.IB4) result.label = 'IB-MT4';
  else if (enumValue === ProductCategoryEnum.IB5) result.label = 'IB-MT5';
  else if (enumValue === ProductCategoryEnum.PT) result.label = 'PT';
  return result;
});

// 身分證件類型
const idTypeOptions = eNumEntities<typeof IDTypeNum>(IDTypeNum).values.map((enumValue) => {
  const result = { value: enumValue, label: '未定義' };
  if (enumValue === IDTypeNum.ResidentID) result.label = '居民身分證';
  else if (enumValue === IDTypeNum.BirthCert) result.label = '出生證';
  else if (enumValue === IDTypeNum.Passport) result.label = '護照';
  return result;
});

// 帳號等級類別
const levelOptions = eNumEntities<typeof LevelEnum>(LevelEnum).values.map((enumValue) => {
  const result = { value: enumValue, label: '未定義' };
  if (enumValue === LevelEnum.Client1) result.label = '客戶Lv1';
  else if (enumValue === LevelEnum.Client2) result.label = '客戶Lv2';
  else if (enumValue === LevelEnum.Client3) result.label = '客戶Lv3';
  else if (enumValue === LevelEnum.Client4) result.label = '客戶Lv4';
  else if (enumValue === LevelEnum.Client5) result.label = '客戶Lv5';
  else if (enumValue === LevelEnum.IB1) result.label = 'IB Lv1';
  else if (enumValue === LevelEnum.IB2) result.label = 'IB Lv2';
  else if (enumValue === LevelEnum.IB3) result.label = 'IB Lv3';
  return result;
});

// 申請項目
const applyEnumObj: EnumObjBase<typeof ApplyTypeEnum, { color: string }> = {
  KYC: {
    label: '實名驗證',
    value: ApplyTypeEnum.KYC,
    color: '#0066ff',
  },
  ChangeBank: {
    label: '變更銀行',
    value: ApplyTypeEnum.ChangeBank,
    color: '#9900cc',
  },
  IB: {
    label: '申請IB',
    value: ApplyTypeEnum.IB,
    color: '#ff0000',
  },
  NoOvntInterest: {
    label: '無隔夜利息',
    value: ApplyTypeEnum.NoOvntInterest,
    color: '#008000',
  },
};
const applyOptions = eNumEntities<typeof ApplyTypeEnum>(ApplyTypeEnum).keys.map((enumKey) => {
  const { value = ApplyTypeEnum[enumKey], label = '未定義', color = 'grey' } = applyEnumObj[enumKey] || {};
  return { value, label, color };
});

// 項目交易狀態
const itTradeEnumObj: EnumObjBase<typeof InstrumentTradeStatus> = {
  NoTrade: {
    label: '不能交易',
    value: InstrumentTradeStatus.NoTrade,
  },
  OnlyClose: {
    label: '只能平倉',
    value: InstrumentTradeStatus.OnlyClose,
  },
  AllAllow: {
    label: '沒有限制',
    value: InstrumentTradeStatus.AllAllow,
  },
};
const itTradeOptions = eNumEntities<typeof InstrumentTradeStatus>(InstrumentTradeStatus).keys.map((enumKey) => {
  const { value = InstrumentTradeStatus[enumKey], label = '未定義' } = itTradeEnumObj[enumKey] || {};
  return { value, label };
});

// 獲取國家銀行資訊
const getCountryBankOptions = (countyKey: CountryKeyNum | undefined) => {
  if (countyKey === CountryKeyNum.Taiwan)
    return [
      { value: '163', label: '臺灣銀行 Bank of Taiwan (163)' },
      { value: '149', label: '臺灣土地銀行 Land Bank of Taiwan（149）' },
      {
        value: '269',
        label: '合作金庫商業銀行 Taiwan Cooperative Bank（269）',
      },
      { value: '186', label: '第一商業銀行 First Commercial Bank（186）' },
      {
        value: '185',
        label: '華南商業銀行 Hua Nan Commercial Bank, Ltd.（185）',
      },
    ];
  return [];
};

// 使用 IDN format
const applyMaskFormat = (str: string | NOU, formator: string | NOU): { fStr: string | NOU; offset: number } => {
  if (!str || !formator) return { fStr: str, offset: str?.length ?? 0 };
  const valiedStr = str.replace(/\D/g, '');
  const fArr = formator.split('');
  const pureFArr = fArr.filter((fStr) => !['[', ']'].includes(fStr));
  const maksLength = pureFArr.filter((fStr) => fStr === '#').length;
  const completeStr = (valiedStr + '_'.repeat(maksLength)).slice(0, maksLength);
  let strIndex = 0;
  const resultArr = pureFArr.reduce((newStrArr, fStr) => {
    if (fStr === '#') {
      const combineStr = [...newStrArr, completeStr.slice(strIndex, strIndex + 1)];
      strIndex += 1;
      return combineStr;
    }
    return [...newStrArr, fStr];
  }, [] as Array<string>);
  const placeholderArr = resultArr.filter((rStr) => rStr === '_');
  return { fStr: resultArr.join(''), offset: placeholderArr.length };
};

// 將選項轉換為filter使用選項
const optionToFilter = (options: Array<Object & { label: string; value: OptionValue }>) => {
  return options.map((option) => ({ ...option, text: option.label }));
};

// 將選項的值做成一個陣列
const optionValues = <V = string>(options: Array<object & { label: string; value: OptionValue }>) => {
  return options.map((option) => option.value) as Array<V>;
};

// 將值陣列轉為選項陣列
const valuesToOptions = <V = string>(values: Array<V>, options: Array<{ label: React.ReactNode; value: V }>) => {
  return values.map((value) => {
    return options.find((findOption) => findOption.value === value);
  });
};

// 將值陣列轉為選項陣列
const valuesToFilter = <V = string>(values: Array<V>, options: Array<{ label: React.ReactNode; value: V }>) => {
  return values.map((value) => {
    const option = options.find((findOption) => findOption.value === value);
    return { text: option?.label, value };
  });
};

// 值從列表中轉為字串
const valueToLabel = <V = string>(value: V, options: Array<{ label: React.ReactNode; value: V }>) => {
  return options.find((findOptions) => findOptions.value === value)?.label;
};

// 標籤直接用於 Filter 的 Value
const labelsToFilters = <Label = string>(labels: Array<Label>) => {
  return labels.map((mapL) => ({ text: mapL, value: mapL }));
};

export {
  cryptoProtocolOptions,
  cryptoCurrencyOptions,
  anomalyResolutionEnumObj,
  anomalyResolutionOptions,
  shiftNameObj,
  ShiftNameOptions,
  tronWalletFuncObj,
  tronWalletFuncOptions,
  tronMasterStatusOptions,
  tronWalletStatusObj,
  tronWalletStatusOptions,
  txStatusEnumObj,
  txStatusOptions,
  merchantTxStatusEnumObj,
  merchantTxStatusOptions,
  labelsToFilters,
  limittiObj,
  limittiOptions,
  clientEnvObj,
  clientEnvOptions,
  cryptoTypesOptions,
  cryptoEnumOptions,
  cryptoEnumObj,
  salesVolumeStatusEnumObj,
  salesVolumeStatusOptions,
  itTradeEnumObj,
  itTradeOptions,
  valueToLabel,
  valuesToOptions,
  valuesToFilter,
  levelOptions,
  idTypeOptions,
  optionValues,
  optionToFilter,
  applyMaskFormat,
  getCountryBankOptions,
  productOptions,
  ovTypeOptions,
  reviewStatus,
  platformOptions,
  verifyStatusOptions,
  countryEnumObj,
  countryOptions,
  txOptions,
  currencyOptions,
  IDNOptions,
  applyEnumObj,
  applyOptions,
  leaseInOrderStatusOptions,
  leaseOutOrderStatusOptions,
};
export type { OptionValue, OptionBase };
