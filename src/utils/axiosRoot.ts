// libs
import axios from 'axios';

// api
import type { LoginRes } from '@/api/auth/useLogin';

// utils
import { forage } from './foragePkg';

const axiosRoot = axios.create({
  baseURL: `${import.meta.env.VITE_AXIOS_ROOT}`,
});

axiosRoot.interceptors.request.use(async (config) => {
  const auth = await forage<LoginRes>().getItem('loginRes');
  if (!auth) return config;
  const pureConfig = { ...config };
  pureConfig.headers.Authorization = `Bearer ${auth.token}`;
  return pureConfig;
});

export default axiosRoot;
