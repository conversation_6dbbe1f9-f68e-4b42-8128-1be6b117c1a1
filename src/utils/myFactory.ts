import { type IAssetOptions } from '@/api/wallet/useEthAssets';
import { en_US, en_HK, zh_TW, zh_CN, Faker, en, fr_CA } from '@faker-js/faker';

const faker = new Faker({
  locale: [zh_TW, en_US, zh_CN, en_HK, en, fr_CA],
});

const assetsItemFactory = (assetName: string): IAssetOptions => {
  const total = faker.number.float();
  const pending = faker.number.float({ min: total });
  const balance = total - pending;
  return { assetName, total, pending, balance };
};

export default faker;
export { assetsItemFactory };
