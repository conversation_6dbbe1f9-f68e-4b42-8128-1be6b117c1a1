// api
import type { AllStaffAccsProps, AllTronWalletsProps } from '@/api';
import type { MerchantListProps } from '@/api/merchant';
import type { OrderListProps } from '@/api/order';
import type { MerchantOrderListProps } from '@/api/order/useMerchantOrder';
import type { ShiftListProps } from '@/api/shift/useAllShiftList';

export default {
  query: {
    info: (token: string | undefined) => ['info', token],
    clientMerchantDetails: (customerId: number | undefined) => ['client', 'merchant', 'details', customerId],
    cliensMerchants: (customerName: string | undefined) => ['client', 'merchants', customerName],
    // Order list query
    orderList: (orderListProps?: OrderListProps) => {
      const pureProps = { ...(orderListProps || {}), Status: orderListProps?.Status?.join(', ') };
      const result = ['order', 'list', ...Object.values(pureProps || {})];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.orderListKeys) {
          const isAlreadyHave = window.myGlobalVariable.orderListKeys.some((everyKeys) => {
            return everyKeys.every((everyKey, index) => {
              const isSame = !everyKey || everyKey === result.at(index);
              return isSame;
            });
          });
          if (!isAlreadyHave) window.myGlobalVariable.orderListKeys.push(result);
        } else window.myGlobalVariable.orderListKeys = [result];
      } else window.myGlobalVariable = { orderListKeys: [result] };
      return result;
    },
    allOrderList: () => {
      if (window.myGlobalVariable) return window.myGlobalVariable.orderListKeys || [];
      return [];
    },
    merchantOrderList: (params?: MerchantOrderListProps) => [
      'merchant',
      'order',
      'list',
      ...(params ? Object.values(params) : []),
    ],
    diOrderList: (params?: any) => [
      'di',
      'order',
      'list',
      ...(params ? Object.values(params) : []),
    ],
    // All Tron wallet Query; useTronWallet not query use mutation
    allTronWallets: (params: AllTronWalletsProps | undefined) => {
      const result = ['all', 'tron', 'wallets', ...Object.values(params || {})];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.allTronWalletsKeys) {
          const isAlreadyHave = window.myGlobalVariable.allTronWalletsKeys.some((everyKeys) => {
            return everyKeys.every((everyKey, index) => !everyKey || everyKey === result.at(index));
          });
          if (!isAlreadyHave) window.myGlobalVariable.allTronWalletsKeys.push(result);
        } else window.myGlobalVariable.allTronWalletsKeys = [result];
      } else window.myGlobalVariable = { allTronWalletsKeys: [result] };
      return result;
    },
    callbackLog: (orderUid: string | undefined) => ['callbackLog', orderUid],
    getAllTronWalletsKeys: () => {
      if (window.myGlobalVariable) return window.myGlobalVariable.allTronWalletsKeys || [];
      return [];
    },

    tronWalletDetails: (address: string) => ['tron', 'wallet', address],
    transactionDetail: (transactionHash: string | undefined) => ['transaction', 'detail', transactionHash],
    // Merchant list query
    merchantList: (params: MerchantListProps | undefined) => {
      const result = ['merchant', 'list', ...Object.values(params || {})];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.merchantListKeys) {
          const isAlreadyHave = window.myGlobalVariable.merchantListKeys.some((everyKeys) => {
            return everyKeys.every((everyKey, index) => !everyKey || everyKey === result.at(index));
          });
          if (!isAlreadyHave) window.myGlobalVariable.merchantListKeys.push(result);
        } else window.myGlobalVariable.merchantListKeys = [result];
      } else window.myGlobalVariable = { merchantListKeys: [result] };
      return result;
    },
    getAllMerchantListKeys: () => {
      if (window.myGlobalVariable) return window.myGlobalVariable.merchantListKeys || [];
      return [];
    },

    //  AllShiftList query
    allShiftList: (params: ShiftListProps | undefined) => {
      const result = ['all', 'shift', 'list', ...Object.values(params || {})];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.allShiftListkeys) {
          const isAlreadyHave = window.myGlobalVariable.allShiftListkeys.some((everyKeys) => {
            return everyKeys.every((everyKey, index) => !everyKey || everyKey === result.at(index));
          });
          if (!isAlreadyHave) window.myGlobalVariable.allShiftListkeys.push(result);
        } else window.myGlobalVariable.allShiftListkeys = [result];
      } else window.myGlobalVariable = { allShiftListkeys: [result] };
      return result;
    },
    getAllShiftListKeys: () => {
      if (window.myGlobalVariable) return window.myGlobalVariable.allShiftListkeys || [];
      return [];
    },
    allStaffAccs: (params: AllStaffAccsProps | undefined) => {
      const result = ['all', 'system', 'account', ...Object.values(params || {})];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.allStaffAccsKeys) {
          const isAlreadyHave = window.myGlobalVariable.allStaffAccsKeys.some((everyKeys) => {
            return everyKeys.every((everyKey, index) => !everyKey || everyKey === result.at(index));
          });
          if (!isAlreadyHave) window.myGlobalVariable.allStaffAccsKeys.push(result);
        } else window.myGlobalVariable.allStaffAccsKeys = [result];
      } else window.myGlobalVariable = { allStaffAccsKeys: [result] };
      return result;
    },
    getAllSystemAccsKeys: () => {
      if (window.myGlobalVariable) return window.myGlobalVariable.allStaffAccsKeys || [];
      return [];
    },
    tronMaster: ['tron', 'master'],
  },
};
