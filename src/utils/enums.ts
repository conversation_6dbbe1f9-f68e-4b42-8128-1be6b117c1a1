// Key 與 Value 不要使用完全相同的值，會導致錯誤
enum PlatformEnum {
  K100U = 1,
  PT = 2,
}
enum ShiftNameEnum {
  Morning = 1,
  Afternoon = 2,
  Night = 3,
}
enum TronWalletStatus {
  Available = 1,
  Transaction = 2,
  CoolDown = 3,
}

enum TronWalletFuncEnum {
  // Payment = 0,
  Collection = 1,
  Stake = 2,
  MerchantPaymentWallet = 3,
  SupervisorWallet = 4,
  EnergyLease = 5,
}

enum InstrumentTradeStatus {
  NoTrade = 0,
  OnlyClose = 1,
  AllAllow = 2,
}

enum ServerStatusEnum {
  Synchronous = 0,
  Delay = 1,
  Disconnected = 2,
}

enum AnomalyResolutionStatusEnum {
  Pending = 1,
  InProgress = 2,
  Completed = 3,
  Failed = 4,
}

enum AnomalyStatusEnum {
  Created = 1, // created
  Broadcasted = 2, // 區塊鏈廣播
  Confirmed = 3, // 區塊鏈確認
  Completed = 4, // 完成
}

enum ClientEnvironmentEnum {
  Formal,
  Test,
}

enum LimitTimeInterval {
  ThirtyMins,
  OneH,
  TwoH,
}

enum KycDetailType {
  Text = 1,
  Img = 2,
}

enum SaleStatusEnum {
  Pending = 1,
  Settled = 2,
  Objection = 3,
}

enum InstrumentTypeEnum {
  Unknown = 1,
}

enum IDTypeNum {
  ResidentID = 1,
  BirthCert = 2,
  Passport = 3,
}

enum ApplyTypeEnum {
  KYC = 1,
  ChangeBank = 2,
  IB = 3,
  NoOvntInterest = 4,
}

enum ProductCategoryEnum {
  MT4 = 1,
  MT5 = 2,
  PT = 3,
  IB4 = 4,
  IB5 = 5,
}

enum CurrencyNum {
  USD = 1,
  TWD = 2,
  CNY = 3,
  USDT = 4,
}

enum Mt4TxTypeNum {
  Buy = 1,
  Sell = 2,
}

enum TxCategoryNum {
  Deposit = 1,
  Withdraw = 2,
  Transfer = 3,
  Commission = 4,
}

enum TxStatusNum {
  Retry = -1, // 重試
  Created = 1, // created
  Broadcasted = 2, // 區塊鏈廣播
  Confirmed = 3, // 區塊鏈確認
  Completed = 4, // 完成
  Canceled = 100, // 取消
  Timeout = 101, // 超時
  MerchantCallbackFailed = 1001, // 通知商戶的回調失敗
  BlockchainTransactionFailed = 1002, // 區塊鏈交易失敗
  WaitingForApproval = 1345,
}
enum MerchantTransactionStatus {
  Retry = -1,
  Created = 1,
  BlockchainBroadcast = 2,
  BlockchainConfirmed = 3,
  Completed = 4,
  Canceled = 100,
  Timeout = 101,
  CallbackFailed = 1001,
  BlockchainFailed = 1002,
  OtherError = 1003,
}
enum TronMasterStatusEnum {
  Normal = 0,
  DepositOnly = 1,
  WithdrawalOnly = 2,
  Suspended = 3,
}
enum TronWalletStatusEnum {
  Available = 1,
  Transaction = 2,
  Cooldown = 3,
}

enum CryptoProtocolEnum {
  TRC20 = 1,
}
enum CryptoCurrencyEnum {
  USDT = 1,
}

enum CryptoEnum {
  TRC20_USDT = 1,
  ERC20_USDT = 2,
}

enum VerifyStatusNum {
  UnSet = 1,
  Verifing = 2,
  Pass = 3,
  Fail = 4,
  NeedReUpload = 5,
}

enum ReviewStatusNum {
  Pending = 1,
  Finish = 2,
  TurnDown = 3,
}

enum CountryKeyNum {
  Taiwan = 1,
  Honkon = 2,
}

enum IDNeNum {
  'US/CA' = 1,
  CN = 86,
  TW = 886,
  JP = 81,
  GB = 44,
  DE = 49,
  VN = 84,
}

enum UserRoles {
  IB = 1,
  Role2 = 'admin',
}

enum InvestmentsEnum {
  GOLD = 1,
  SILVER = 2,
  SLV = 3,
  FSM = 4,
  BTC = 5,
}

enum OvTypeEnum {
  OnlineTrading = 1,
  AccountsNumber = 2,
  AccountBalance = 3,
  UsersNationality = 4,
  UsersLevels = 5,
}

enum LevelEnum {
  Client1 = 1,
  Client2 = 2,
  Client3 = 3,
  Client4 = 4,
  Client5 = 5,
  IB1 = 6,
  IB2 = 7,
  IB3 = 8,
}

enum LeaseInOrderStatusEnum {
  Pending = 1,
  Transferring = 2,
  TransferSuccess = 3,
  EnergyReceived = 4,
  TransferFailed = 99,
}

enum LeaseOutOrderStatusEnum {
  Received = 0,
  Delegating = 1,
  DelegateSuccess = 2,
  DelegateFail = 3,
  Reclaiming = 4,
  ReclaimSuccess = 5,
  ReclaimFail = 6,
  InvalidTrxAmount = 7,
  InsufficientEnergy = 8,
}

const eNumEntities = <T extends object>(originEnum: T) => {
  const keyofEnums = Object.keys(originEnum);
  const vailedKeys = keyofEnums.filter((filterK) => Number.isNaN(Number(filterK)));

  const result = vailedKeys.reduce(
    (arr, key) => {
      const enumKey = key as keyof typeof originEnum;
      const valueOfObj = originEnum[enumKey];
      if (!arr.keys.includes(enumKey) && valueOfObj !== undefined)
        return {
          keys: [...arr.keys, enumKey],
          values: [...arr.values, valueOfObj],
        };
      if (keyofEnums.includes('NoTrade')) {
        // logInfo({ Title: 'Unvalid', enumKey, valueOfObj });
      }
      return arr;
    },
    { keys: [] as Array<keyof typeof originEnum>, values: [] as Array<ValueOf<typeof originEnum>> },
  );

  if (keyofEnums.includes('NoTrade')) {
    // logInfo({ keyofEnums, keysLength, lengthAvg, vailedKeys, result });
  }

  return result;
};

export {
  CryptoProtocolEnum,
  CryptoCurrencyEnum,
  TronWalletFuncEnum,
  TronWalletStatus,
  AnomalyResolutionStatusEnum,
  AnomalyStatusEnum,
  ServerStatusEnum,
  TronMasterStatusEnum,
  TronWalletStatusEnum,
  CryptoEnum,
  LimitTimeInterval,
  ClientEnvironmentEnum,
  Mt4TxTypeNum,
  KycDetailType,
  TxStatusNum,
  InstrumentTypeEnum,
  InstrumentTradeStatus,
  ApplyTypeEnum,
  IDTypeNum,
  InvestmentsEnum,
  OvTypeEnum,
  ProductCategoryEnum,
  eNumEntities,
  ReviewStatusNum,
  PlatformEnum,
  UserRoles,
  VerifyStatusNum,
  CountryKeyNum,
  TxCategoryNum,
  CurrencyNum,
  LevelEnum,
  IDNeNum,
  SaleStatusEnum,
  ShiftNameEnum,
  MerchantTransactionStatus,
  LeaseInOrderStatusEnum,
  LeaseOutOrderStatusEnum,
};
