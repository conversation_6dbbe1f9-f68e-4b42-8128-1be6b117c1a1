// utils
import { logInfo } from './logger';
import { nTot } from './numbro';
/**
 * TODO:
 * 1. 壓縮比例應該調整成最高大小以及最少壓縮
 * 2. 浮水印改為整版面斜向
 *  -- 完成了基底還需要微調整。
 */

// === Blob || Canvas 轉 File ===
const bTof = async (from: HTMLCanvasElement | Blob, quality?: number): Promise<File> => {
  if (from instanceof Blob) {
    const file = new File([from], 'img.jpg', { type: 'image/jpeg' });
    return file;
  }
  const compressedBlob = await new Promise<Blob>((resolve) => {
    from.toBlob(
      (blob) => {
        if (blob) resolve(blob);
      },
      'image/jpeg',
      quality ?? 0.8,
    );
  });
  const file = new File([compressedBlob], 'img.jpg', { type: 'image/jpeg' });
  return file;
};

// === 產生畫布 ===
const createCx = async (file: File) => {
  const maxWidth = 800;
  const maxHeight = 800;
  const img = new Image();
  img.src = URL.createObjectURL(file);

  await new Promise<void>((resolve, reject) => {
    img.onload = () => {
      URL.revokeObjectURL(img.src);
      resolve();
    };
    img.onerror = reject;
  });
  const { width, height } = img;
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  if (canvas.width > maxWidth) {
    canvas.width = maxWidth;
    canvas.height = width ? (maxWidth * height) / width : maxHeight;
  }
  if (height > maxHeight) {
    canvas.height = maxHeight;
    canvas.width = height ? (maxHeight * width) / height : maxWidth;
  }
  const ctx = canvas.getContext('2d');
  if (!ctx) return false;
  ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
  return { ctx, canvas, width, height };
};

// === 浮水印 ===
type DrawRes = {
  wImg: string;
  wFile: File;
};
const drawWaterMark = async (file: File, mark?: string, sizeLog?: boolean): Promise<DrawRes | false> => {
  const { ctx, canvas } = (await createCx(file)) || {};
  if (!ctx || !canvas) return false;
  // #: 文字樣式 <大小> <字體>
  const text = mark ?? 'PT client';
  const avgFontSize = canvas.width / 20;
  const minFontSize = 20;
  const maxFontSize = 40;
  let fontSize = avgFontSize;
  ctx.font = `${avgFontSize}px Arial`;
  if (fontSize < minFontSize) fontSize = minFontSize;
  if (fontSize > maxFontSize) fontSize = maxFontSize;
  ctx.font = `${fontSize}px Arial`;
  ctx.textBaseline = 'middle';

  // #: 繪製
  // 取得文字 高 寬
  const tempDiv = document.createElement('div');
  tempDiv.style.visibility = 'hidden';
  tempDiv.style.fontSize = `${fontSize}px`;
  tempDiv.style.fontFamily = 'Arial';
  tempDiv.style.textAlign = 'middle';
  tempDiv.style.width = 'fit-content';
  tempDiv.style.padding = '1px 8px';
  tempDiv.innerText = text;
  document.body.appendChild(tempDiv);
  const textWidth = tempDiv.clientWidth;
  const textHeight = tempDiv.clientHeight;
  document.body.removeChild(tempDiv);

  //
  ctx.rotate(Math.PI / 5);
  ctx.translate(-canvas.width / 2, -(canvas.height / 2));
  const startPoint = -10;
  let left = startPoint;
  let top = startPoint;
  const dx = canvas.width / 2;
  const dy = canvas.height / 3;
  while (top < canvas.height * 2) {
    left = startPoint;
    while (left < canvas.width * 2) {
      // 文字底框
      ctx.translate(-8, -textHeight / 2 + 2);
      ctx.fillStyle = 'rgba(220, 220, 220, 0.7)';
      ctx.fillRect(left, top, textWidth, textHeight);
      ctx.translate(8, textHeight / 2 + 2);
      // 寫上文字
      ctx.fillStyle = 'rgba(15, 72, 122, 0.5)';
      ctx.fillText(text.trim(), left, top);
      left += dx;
    }
    top += dy;
  }

  // #: 轉換格式返回
  const wImg = canvas.toDataURL('image/jpeg', 1);
  const wFile = await bTof(canvas);
  // #: Size compare log
  if (import.meta.env.DEV && sizeLog && wFile) {
    logInfo(`Origin file size: ${nTot({ value: file.size })}`);
    logInfo(`Drawed file size: ${nTot({ value: wFile.size })}`);
  }
  return { wImg, wFile };
};

// === 壓縮 ===
type CompressionOptions = {
  file: File | undefined | null;
  sizeLog?: boolean;
};

type CompressionRes = Promise<
  | false
  | {
      beanImg: string;
      beanFile: File;
    }
>;
const compression = async (props: CompressionOptions): CompressionRes => {
  const { file, sizeLog } = props;
  if (!file) return Promise.resolve(false);
  const { ctx, canvas } = (await createCx(file)) || {};

  // #: 壓縮
  if (!ctx || !canvas) return Promise.resolve(false);
  const beanImg = canvas.toDataURL('image/jpeg', 0.5);
  const beanFile = await bTof(canvas);

  // #: Size compare log
  if (import.meta.env.DEV && sizeLog && beanFile) {
    logInfo(`Origin file size: ${nTot({ value: file.size })}`);
    logInfo(`Compressed file size: ${nTot({ value: beanFile.size })}`);
  }
  return { beanImg, beanFile };
};

const blobToBase64 = (
  blob: unknown,
): Promise<{ base64data: string; base64String: string; base64Png: string; base64Jpg: string }> => {
  if (blob instanceof Blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64data = reader.result as string;
        const base64String = base64data.split(',')[1]; // 移除前綴的 data URL 部分
        const base64Png = `data:image/png;base64,${base64String}`;
        const base64Jpg = `data:image/jpg;base64,${base64String}`;
        resolve({ base64data, base64String, base64Png, base64Jpg });
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  return Promise.reject(Error('Not blob'));
};

const arrayBufferToBase64 = (buffer: unknown): string => {
  if (buffer instanceof ArrayBuffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i += 1) {
      binary += String.fromCharCode(bytes[i]);
    }
    const result = window.btoa(binary);
    return result;
  }

  return '';
};

export { compression, drawWaterMark, createCx, bTof, blobToBase64, arrayBufferToBase64 };
