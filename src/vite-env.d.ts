/// <reference types="vite/client" />

// env
interface ImportMetaEnv {
  readonly VITE_IS_DEMO: string;
  readonly VITE_AXIOS_ROOT: string;
  readonly VITE_HUBS_URL: string;
  readonly VITE_VERSION: string;
}

interface Window {
  myGlobalVariable?: {
    dxRq?: Array<{
      temp: Object;
      key: Array<string>; // userId, pageSize, depth
    }>;
    chatHubConnection?: Object | null;
    adminHubConnection?: Object | null;
    orderListKeys?: Array<Array<string | number | null | undefined | boolean>>;
    allTronWalletsKeys?: Array<Array<string | number | boolean | null>>;
    merchantListKeys?: Array<Array<string | number>>;
    allShiftListkeys?: Array<Array<string | number | boolean | null>>;
    allStaffAccsKeys?: Array<Array<string | number | boolean | null | undefined>>;
    audio?: Partial<Record<'deposit' | 'withdrawal' | 'anamoly', HTMLAudioElement>>;
    eventHandlers?: {
      [eventName: string]: ((...args: any[]) => void)[];
    };
    eventHandlersObj?: {
      [eventName: string]: {
        [handlerName: string]: (message: any) => void;
      };
    };
  };
}

enum MikeErrorCodeNum {
  'TestCode' = -1,
  'VerifyError' = 0,
  'Nothing' = 1,
  'CantSms' = 4,
}

interface IMikeRes<DATA = unknown> {
  status: number;
  data: DATA;
  msg: string;
}

interface IMikeError {
  errorCode: MikeErrorCodeNum;
  errors: { [key: string]: Array<string> };
  status: number;
  title: string;
  type: string;
}

type RequestError = IMikeError;

//
type ValueOf<T> = T[keyof T];
type NOU = null | undefined;
type DevProps = {
  isTest?: boolean;
};
type ReactSet<T> = React.Dispatch<React.SetStateAction<T>>;
type TabObjOptions<T> = {
  [Key in T]: {
    closeIcon?: React.ReactNode;
    destroyInactiveTabPane?: boolean;
    disabled?: boolean;
    forceRender?: boolean;
    key: Key;
    label: React.ReactNode;
    icon?: React.ReactNode;
    children?: React.ReactNode;
    closable?: boolean;
    panel?: React.ReactNode;
  };
};
type PeriodKeys = '24h' | '7d' | '30d' | 'manual';
type KycFieldTypes =
  | 'FullName'
  | 'IDNumber'
  | 'SecondaryFullName'
  | 'SecondaryIDNumber'
  | 'DateOfBirth'
  | 'IDAddress'
  | 'ResidentialAddress'
  | 'PhoneNumber'
  | 'IDFront'
  | 'IDBack'
  | 'SecondaryIDFront'
  | 'SecondaryIDBack'
  | 'SelfieWithID'
  | 'AddressProof';
type KycFeildDetail<FN extends KycFeildTypes> = {
  fieldName: FN;
  fieldValue: string;
  id: number;
  isVerified: boolean;
  remark: null | string;
  type: 1 | 2;
};

type SymbolTypes = 'USDCAD';
type RoleTypes = 'SystemCS' | 'SystemAdmin' | 'SystemSupervisor';
type CountryKeyTypes = 'TW' | 'CN' | 'JP' | 'GB' | 'DE' | 'VN' | 'US/CA';
type LocaleTypes = 'zh-TW' | 'zh-CN' | 'en-US' | 'vi-VN';
type CryptoTypes = 'USDT' | 'BandWidth';
type ProtoColTypes = 'TRC-20' | 'ERC-20';
type shiftType = 'UUPP' | 'UUCC';
