// libs
import React, { Suspense, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import ErrorBoundary from 'antd/es/alert/ErrorBoundary';
import { ConfigProvider, theme } from 'antd';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useIsDemo } from '@/hooks';

// styles
import './index.css';

// components
import FallbackLoading from './components/FallbackLoading';
import NotifyProvider from './components/NotifyProvider';
import Mask from './components/Mask';
import SpinMaster from './components/SpinMaster';

// i18next
import './i18next';

// routes
import AppRouter from './routes/AppRouter';

// store
import { useUserStore, useNotifyStore } from './store';

const App: React.FunctionComponent = () => {
  // hooks
  const { isDark, isLoading } = useUserStore();
  const { basicErrorQue, shiftBEQ, basicSuccessQue, shiftBSQ } = useNotifyStore();
  const isDemo = useIsDemo();
  // compute
  const algorithm = isDark ? theme.darkAlgorithm : theme.defaultAlgorithm;

  const deposit = new Audio('/sounds/deposit.mp3');
  const anamoly = new Audio('/sounds/anomaly.mp3');
  const withdrawal = new Audio('/sounds/withdrawal.mp3');

  useEffect(() => {
    window.myGlobalVariable = {
      ...window.myGlobalVariable,
      audio: {
        deposit,
        withdrawal,
        anamoly,
      },
    };
  });

  return (
    <>
      <ConfigProvider
        theme={{
          algorithm,
          token: { colorPrimary: '#389e0d' },
          components: {
            Layout: {
              siderBg: '#1f2326',
              // eslint-disable-next-line no-nested-ternary
              headerBg: isDemo ? '#389e0d' : isDark ? '#1f2326' : '#fff',
              headerPadding: '0 16px',
              triggerBg: '#101519',
            },
            Menu: {
              darkItemBg: '#1f2326',
            },
            Card: {
              colorBgContainer: isDark ? '#232526' : '#fff',
            },
            Tabs: {
              titleFontSizeLG: 80,
            },
          },
        }}
      >
        <Suspense fallback={<FallbackLoading />}>
          <AppRouter />
        </Suspense>
      </ConfigProvider>

      {/* Dialogues */}
      <Mask open={isLoading}>
        <SpinMaster />
      </Mask>
      <NotifyProvider
        basicQue={basicErrorQue}
        type='error'
        shiftBsicQue={shiftBEQ}
      />
      <NotifyProvider
        basicQue={basicSuccessQue}
        shiftBsicQue={shiftBSQ}
        type='success'
      />

      <ReactQueryDevtools />
    </>
  );
};

const Main: React.FunctionComponent = () => {
  // hooks
  const queryClient = new QueryClient();

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <App />
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Main />
  </React.StrictMode>,
);
