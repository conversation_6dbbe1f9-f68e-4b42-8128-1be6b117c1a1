// libs
import { useEffect, useRef, useCallback } from 'react';
import { HubConnection, HubConnectionBuilder, HubConnectionState } from '@microsoft/signalr';

// store
import { useUserStore } from '@/store';

// utils
import { logInfo, logWarn } from '@/utils';

const adminHubUrl = `${import.meta.env.VITE_HUBS_ROOT}/adminHub`;

type UseAdminSignalRConnectionProps = {};
const useAdminSignalRConnection = (useProps: UseAdminSignalRConnectionProps) => {
  // props
  const {} = useProps;

  // refs
  const connectionRef = useRef<HubConnection>();

  // hooks
  const { loginRes: adminLoginRes } = useUserStore(); // Admin authentication state

  if (window.myGlobalVariable && !window.myGlobalVariable.eventHandlers) window.myGlobalVariable.eventHandlers = {};

  // handlers
  const registerHandler = useCallback((eventName: string, handler: (...args: any[]) => void) => {
    const existingAdminConnection = window.myGlobalVariable?.adminHubConnection as HubConnection | undefined;
    if (existingAdminConnection) {
      if (existingAdminConnection.state === HubConnectionState.Disconnected) {
        existingAdminConnection.start().then(() => {
          logInfo('Admin hub: Reconnected successfully');
        });
      }
      connectionRef.current = existingAdminConnection;
    } else return;

    const eventHandlers = window.myGlobalVariable?.eventHandlers;
    if (!eventHandlers) return;

    if (!eventHandlers[eventName]) {
      eventHandlers[eventName] = [];
    }
    if (eventHandlers[eventName].includes(handler)) return;
    eventHandlers[eventName].push(handler);

    if (connectionRef.current) {
      // Remove all associated events before register a new one
      connectionRef.current.off(eventName);
      connectionRef.current.on(eventName, (...args) => {
        eventHandlers[eventName].forEach((h) => h(...args));
      });
    }
  }, []);

  const unregisterHandler = useCallback((eventName: string, handler: (...args: any[]) => void) => {
    const existingAdminConnection = window.myGlobalVariable?.adminHubConnection as HubConnection | undefined;
    if (existingAdminConnection) {
      if (existingAdminConnection.state === HubConnectionState.Disconnected) {
        existingAdminConnection.start().then(() => {
          logInfo('Admin hub: Reconnected successfully');
        });
      }
      connectionRef.current = existingAdminConnection;
    } else return;
    if (!window.myGlobalVariable) window.myGlobalVariable = {};
    if (!window.myGlobalVariable.eventHandlers) window.myGlobalVariable.eventHandlers = {};

    const { eventHandlers } = window.myGlobalVariable;

    if (!eventHandlers[eventName]) return;

    eventHandlers[eventName] = eventHandlers[eventName].filter((h) => h !== handler);
    if (connectionRef.current) {
      connectionRef.current.off(eventName);
      connectionRef.current.on(eventName, (...args) => {
        eventHandlers[eventName].forEach((h) => h(...args));
      });
    }
    if (eventHandlers[eventName].length === 0) {
      delete eventHandlers[eventName]; // Cleanup the empty entry
      connectionRef.current?.off(eventName); // Remove the SignalR listener
    }
  }, []);

  // init
  useEffect(() => {
    const existingAdminConnection = window.myGlobalVariable?.adminHubConnection as HubConnection | undefined;
    const eventHandlers = window.myGlobalVariable?.eventHandlers;

    if (!adminLoginRes) {
      if (existingAdminConnection) {
        logInfo('Admin hub: Preparing to disconnect from the Server');
        existingAdminConnection
          ?.stop()
          .then(() => {
            logInfo('Admin hub: Disconnection success');
          })
          .catch((error) => {
            logWarn({ Title: 'Admin hub disconnection error', error });
          });
      }
      return () => {};
    }

    // Restart the connection if it exists but is disconnected
    if (existingAdminConnection) {
      if (existingAdminConnection.state === HubConnectionState.Disconnected) {
        existingAdminConnection.start().then(() => {
          logInfo('Admin hub: Reconnected successfully');
        });
      }
      return () => {};
    }
    connectionRef.current = new HubConnectionBuilder()
      .withUrl(adminHubUrl, { accessTokenFactory: () => adminLoginRes.token }) // Use Admin token
      .build();

    connectionRef.current
      .start()
      .then(() => {
        logInfo('Admin hub connected');
        if (eventHandlers)
          Object.keys(eventHandlers).forEach((eventName) => {
            connectionRef.current!.off(eventName);
            connectionRef.current!.on(eventName, (...args) => {
              eventHandlers[eventName].forEach((h) => h(...args));
            });
          });
      })
      .catch((error) => {
        logWarn({ Title: 'Admin hub connect failed', error });
      });
    window.myGlobalVariable = {
      ...window.myGlobalVariable,
      adminHubConnection: connectionRef.current,
      eventHandlers,
    };

    return () => {
      if (connectionRef.current && [HubConnectionState.Connected].includes(connectionRef.current.state))
        connectionRef.current.stop();
    };
  }, [adminLoginRes]);

  return { connectionRef, registerHandler, unregisterHandler };
};

export default useAdminSignalRConnection;
export type { UseAdminSignalRConnectionProps };
