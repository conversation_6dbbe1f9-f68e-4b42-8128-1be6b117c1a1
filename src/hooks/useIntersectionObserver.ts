// libs
import { useEffect, useRef } from 'react';

type UseIntersectionObserverProps<T extends Element> = {
  callback?: (entry: IntersectionObserverEntry) => void;
  root?: T;
  rootMargin?: string;
  threshold?: number;
};

const useIntersectionObserer = <T extends Element>(useProps?: UseIntersectionObserverProps<T>) => {
  // props
  const { callback, root, threshold = 1.0, rootMargin = '0px' } = useProps || {};

  // refs
  const elementRef = useRef<T>(null);
  const observer = useRef<IntersectionObserver>();

  useEffect(() => {
    const elementCurrent = elementRef.current;
    if (!elementCurrent) return;

    observer.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // io.disconnect();
          }
          if (callback) callback(entry);
        });
      },
      {
        root: root ?? null,
        threshold,
        rootMargin,
      },
    );

    observer.current.observe(elementCurrent);
  }, [root, threshold, rootMargin, callback]);

  return { elementRef, observer };
};

export default useIntersectionObserer;
export type { UseIntersectionObserverProps };
