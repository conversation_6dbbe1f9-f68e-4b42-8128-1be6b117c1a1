import { useEffect, useRef } from 'react';
import { logInfo } from '@/utils';

const useTestRenderTimes = (isTest = false, componentName = 'Render test') => {
  // refs
  const renderCount = useRef(0);

  useEffect(() => {
    if (!isTest || !import.meta.env.DEV) return;
    renderCount.current += 1;
    logInfo(`${componentName}: ${renderCount.current}`);
  });
};

export default useTestRenderTimes;
