// libs
import { useEffect, useState } from 'react';
import { HubConnection } from '@microsoft/signalr';

// utils
import { logWarn } from '@/utils';

type RegisterConnectionProps = {
  connection: HubConnection | undefined | null;
  connectionName?: string;
};

const useRegisterConnection = (useProps: RegisterConnectionProps) => {
  // props
  const { connection, connectionName } = useProps;

  // states
  const registeredConnectionStates = useState(connection);
  const [registeredConnection, setRegisteredConnection] = registeredConnectionStates;

  useEffect(() => {
    if (!registeredConnection) return;
    registeredConnection.onclose((error) => {
      if (error) logWarn({ Title: `${connectionName} hub close error`, error });
      setRegisteredConnection(registeredConnection);
    });
    registeredConnection.onreconnecting((error) => {
      if (error) logWarn({ Title: `${connectionName} hub reconnecting error`, error });
      setRegisteredConnection(registeredConnection);
    });
    registeredConnection.onreconnected((error) => {
      if (error) logWarn({ Title: `${connectionName} hub reconnected error`, error });
      setRegisteredConnection(registeredConnection);
    });
  }, [connectionName, registeredConnection, setRegisteredConnection]);

  return registeredConnectionStates;
};

export default useRegisterConnection;
