// libs
import { useEffect } from 'react';

// hooks
import useChatHubBuilder, { UseChatHubBuilderProps } from './useChatHubBuilder';

type ReceiveMessageSignal = {
  conversationId: number;
  message: string;
  receiverId: null | string;
  senderId: null | string;
  sentByAnonymous: boolean;
  sentByClient: boolean;
};

type ChatRegisterProps = {
  ReceiveMessage?: boolean;
  onReceiveMessage?: (message: ReceiveMessageSignal) => void;
} & UseChatHubBuilderProps;

const useChatRegister = (useProps: ChatRegisterProps) => {
  // props
  const { ReceiveMessage, onReceiveMessage, ...builderProps } = useProps;

  // hooks
  const { connectionRef } = useChatHubBuilder({ ...builderProps });

  useEffect(() => {
    const connectCurrent = connectionRef.current;
    if (!ReceiveMessage || !connectCurrent) return () => {};

    connectCurrent.on('ReceiveMessage', (message: ReceiveMessageSignal) => {
      if (onReceiveMessage) onReceiveMessage(message);
    });

    return () => {
      if (connectCurrent) connectCurrent.off('ReceiveMessage');
    };
  }, [ReceiveMessage, connectionRef, onReceiveMessage]);
};

export default useChatRegister;
export type { ReceiveMessageSignal };
