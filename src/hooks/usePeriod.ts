// libs
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';

// components
import type { DateRangeOptions } from '@/components/DateRange';

type UsePeriodProps = {
  defaultKey?: PeriodKeys;
};
const usePeriod = (useProps?: UsePeriodProps) => {
  // props
  const { defaultKey } = useProps || {};

  // states
  const [periodKey, setPeriodKey] = useState(defaultKey);
  const [dateRange, setDateRange] = useState<DateRangeOptions>();

  // compute
  const calaulateFromTo = useMemo(() => {
    if (periodKey === '24h') return { from: dayjs().subtract(24, 'h'), to: dayjs() };
    if (periodKey === '7d') return { from: dayjs().subtract(7, 'd'), to: dayjs() };
    if (periodKey === '30d') return { from: dayjs().subtract(30, 'd'), to: dayjs() };
    return { from: dayjs(), to: dayjs() };
  }, [periodKey]);

  // init
  // 當預設變動時重設 periodKey
  useEffect(() => {
    setPeriodKey(defaultKey);
  }, [defaultKey, setPeriodKey]);
  // 當type變動時自動運算 from to
  useEffect(() => {
    if (periodKey === 'manual') return;
    setDateRange(calaulateFromTo);
  }, [periodKey, calaulateFromTo]);

  return { periodKey, setPeriodKey, calaulateFromTo, dateRange, setDateRange };
};

export default usePeriod;
export type { UsePeriodProps };
