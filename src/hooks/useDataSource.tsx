// libs
import { useMemo, useState, useCallback, useEffect } from 'react';
import axios from 'axios';

// api
import { OrderListRes } from '@/api/order';

// hooks
import { WithdrawRowInterface } from '@/pages/withdraw/useWithdrawColumns';

type DataSourceProps<T = WithdrawRowInterface, R extends { items: any[] } = OrderListRes> = {
  txInfo: R | undefined;
  mapper?: (item: R['items'][number]) => T;
  isError?: boolean;
};

const useDataSource = <T = WithdrawRowInterface, R extends { items: any[] } = OrderListRes>({
  txInfo,
  mapper,
  isError,
}: DataSourceProps<T, R>) => {
  // states
  const [internalTxInfo, setInternalTxInfo] = useState<R | undefined>(txInfo);
  const [filters, setFilters] = useState<{ [key: string]: any[] }>({});

  // Sync internal state with txInfo
  useEffect(() => {
    if (txInfo) {
      setInternalTxInfo(txInfo);
    }
  }, [txInfo]);

  // Memoize dataSource calculation with filtering
  const dataSource = useMemo(() => {
    if (!internalTxInfo || !internalTxInfo.items || axios.isAxiosError(internalTxInfo.items) || isError) return [];
    // Map items if needed
    const mappedItems = internalTxInfo.items.map((item) => {
      if (mapper) {
        return mapper(item);
      }

      return {
        orderUid: item.orderUid,
        systemOrderId: item.systemOrderId,
        merchantOrderId: item.merchantOrderId,
        merchantNumber: item.merchantNumber,
        merchantName: item.merchantName,
        transactionHash: item.hash,
        requireAmount: item.requireAmount,
        actualAmount: item.actualAmount,
        transactionType: item.transactionType,
        cryptoType: item.cryptoType,
        status: item.status,
        to: item.to,
        from: item.from,
        remark: item.remark,
        gas: item.gas,
        fee: item.fee,
        confirmedAt: item.confirmedAt,
        createdAt: item.createdAt,
        isFeeSettled: item.isFeeSettled,
        isSentUSDT: item.isSentUSDT,
      } as T;
    });

    // Apply filters
    return mappedItems.filter((item) =>
      Object.keys(filters).every((key) => {
        const filterValues: Array<string> = filters[key];
        if (!filterValues || filterValues.length === 0) return true;
        return filterValues.includes(item[key as keyof typeof item] as unknown as string);
      }),
    );
  }, [internalTxInfo, isError, mapper, filters]);

  // Update specific item
  const updateItem = useCallback(
    (selector: (item: T) => boolean, updatedData: Partial<T>) => {
      setInternalTxInfo((prev) => {
        if (!prev || !prev.items || isError) return prev;

        const updatedItems = prev.items.map((item) => (selector(item) ? { ...item, ...updatedData } : item));

        return { ...prev, items: updatedItems };
      });
    },
    [isError],
  );

  // Replace txInfo completely
  const replaceTxInfo = useCallback((newTxInfo: R) => {
    setInternalTxInfo(newTxInfo);
  }, []);

  // Apply filters
  const applyFilters = useCallback((newFilters: { [key: string]: any[] }) => {
    setFilters(newFilters);
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  return { dataSource, updateItem, replaceTxInfo, applyFilters, clearFilters };
};

export default useDataSource;
export type { DataSourceProps };
