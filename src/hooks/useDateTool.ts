// libs
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

const dateFormator = {
  accurate: 'YYYY-MM-DD HH:mm:ss',
  day: 'dddd',
  clock: 'YYYY MM-DD dddd. HH:mm:ss [(Asia/Taipei SDT +08:00)]',
  date: 'YYYY.MM.DD',
};

type UseCurreDateToolProps = {
  initClock?: boolean;
};
const useDateTool = (useProps: UseCurreDateToolProps) => {
  // props
  const { initClock } = useProps;

  // states
  const [currentDate, setCurrentDate] = useState(dayjs().tz('Asia/Taipei'));

  useEffect(() => {
    if (!initClock) {
      return undefined;
    }

    const intervalID = setInterval(() => {
      setCurrentDate(dayjs().tz('Asia/Taipei'));
    }, 1000);

    return () => {
      clearInterval(intervalID);
    };
  }, [setCurrentDate, initClock]);

  return {
    currentDate,
    setCurrentDate,
    isExpired: (date: Dayjs | string | number | NOU) => {
      if (typeof date === 'string' || typeof date === 'number') {
        const targetDate = dayjs(date);
        if (!targetDate.isValid()) return true;
        return targetDate.isAfter(dayjs());
      }

      if (date instanceof Dayjs) {
        return date.isAfter(dayjs());
      }

      return true; // 當 date 的類型無法識別時，預設返回 true
    },
    formator: (type: keyof typeof dateFormator, date?: Dayjs) => {
      if (date) {
        return date.format(dateFormator[type]);
      }

      return currentDate.format(dateFormator[type]);
    },
  };
};

export { useDateTool, dateFormator };
