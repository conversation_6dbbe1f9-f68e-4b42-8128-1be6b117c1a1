// libs
import { useCallback, useMemo } from 'react';

// 如果存在 null 或是 undefined 則返回 true
const useIsFailed = (args?: Array<string | number | NOU | object>) => {
  const getIsFailed = useCallback((argsProps?: Array<string | number | NOU | object>) => {
    return !argsProps ? true : argsProps?.some((arg) => [null, undefined, ''].some((failedType) => failedType === arg));
  }, []);
  // compute
  const result = useMemo(() => getIsFailed(args), [args, getIsFailed]);
  return {
    getIsFailed,
    result,
  };
};

export default useIsFailed;
