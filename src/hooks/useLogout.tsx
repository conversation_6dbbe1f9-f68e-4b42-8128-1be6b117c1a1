// libs
import { useCallback } from 'react';

// store
import { useUserStore } from '@/store';

// utils
import { forage } from '@/utils';

const useLogout = () => {
  // hooks
  const { reset: resetUser, setIsLoading } = useUserStore();

  // handlers
  const handleClean = useCallback(() => {
    setIsLoading(true);
    if ('caches' in window) {
      caches.keys().then((cacheNames) => {
        cacheNames.forEach((cacheName) => {
          caches.delete(cacheName);
        });
      });
    }
    forage().clear(() => {
      localStorage.clear();
      resetUser();
      setIsLoading(false);
    });
  }, [resetUser, setIsLoading]);

  return { handleClean };
};

export default useLogout;
