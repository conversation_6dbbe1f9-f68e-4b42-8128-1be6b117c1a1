// libs
import { HttpError, HubConnection, HubConnectionBuilder, HubConnectionState } from '@microsoft/signalr';
import { useEffect, useState } from 'react';

// store
import { useUserStore } from '@/store';

// utils
import { logInfo, logWarn } from '@/utils';

// hooks
import useRegisterConnection from './useRegisterConnection';
import useLogout from './useLogout';

window.myGlobalVariable = { ...(window.myGlobalVariable || {}), adminHubConnection: null };
const adminHubUrl = `${import.meta.env.VITE_HUBS_ROOT}/adminHub`;

type AdminHubBuilderProps = {};

const useAdminHubBuilder = (useProps: AdminHubBuilderProps) => {
  // props
  const {} = useProps;

  // states
  const [reconnectDelay, setReconnectDelay] = useState(false);
  const [reconnectTimes, setReconnectTimes] = useState(0);

  // hooks
  const { loginRes } = useUserStore();
  const { handleClean } = useLogout();
  const [conn, setConn] = useRegisterConnection({
    connection: window.myGlobalVariable?.adminHubConnection as HubConnection | null,
    connectionName: 'Admin',
  });

  // init
  useEffect(() => {
    if (!loginRes) {
      // When LoginRes is cleared, it means the session has expired.
      if (conn && ![HubConnectionState.Disconnected].includes(conn.state)) {
        // The existing connection needs to be disconnected.
        logInfo('Admin hub: Preparing to disconnect from the Server');
        conn
          ?.stop()
          .then(() => {
            logInfo('Admin hub: Disconnection success');
          })
          .catch((error: HttpError) => {
            logWarn({ Title: 'Admin hub disconnection error', error });
          })
          .finally(() => {});
      } else if (conn) {
        window.myGlobalVariable = { ...(window.myGlobalVariable || {}), adminHubConnection: null };
        setConn(undefined);
      }
      // Confirm that there is no connection without logging in.
      return () => {};
    }

    // Restart the connection if it exists but is disconnected
    if (conn) {
      // Avoid the state being 'connecting' when it should be 'disconnected' before attempting to reconnect.
      if ([HubConnectionState.Disconnected].includes(conn.state) && !reconnectDelay && reconnectTimes < 3) {
        setReconnectDelay(true);
        conn
          .start()
          .then(() => {
            setReconnectTimes(0);
            logInfo('Admin hub: Reconnected successfully');
          })
          .catch((error) => {
            logWarn({ Title: 'Admin hub reconnect error', error });
            if (error instanceof HttpError && error.statusCode === 401) {
              handleClean();
            }
            // Count failed reconnections to avoid excessive waste of system resources.
            setReconnectTimes((pre) => pre + 1);
          })
          .finally(() => {
            // Avoid rapid and repeated reconnections.
            setTimeout(() => {
              setReconnectDelay(false);
            }, 1000 * 10);
          });
      }
      return () => {};
    }

    // The current phase is where all connections have been cleared and new connections are being established.
    const newConnection = new HubConnectionBuilder()
      .withUrl(adminHubUrl, { accessTokenFactory: () => loginRes.token }) // Use Admin token
      .build();

    newConnection
      .start()
      .then(() => {
        logInfo('Admin hub connected');
      })
      .catch((error) => {
        if (error instanceof HttpError && error.statusCode === 401) {
          handleClean();
        }
        logWarn({ Title: 'Admin hub connect failed', error });
      });

    // The connection state exists in the webpage's memory and needs to be registered in the state managed by React in order to be monitored
    window.myGlobalVariable = { ...(window.myGlobalVariable || {}), adminHubConnection: newConnection };
    setConn(newConnection);
    return () => {
      if (newConnection && [HubConnectionState.Connected].includes(newConnection.state)) newConnection.stop();
    };
  }, [conn, handleClean, loginRes, reconnectDelay, reconnectTimes, setConn]);

  return { conn, setConn };
};

export default useAdminHubBuilder;
