// libs
import { memo, useEffect } from 'react';
import { isEqual } from 'lodash';

type HandlesConcatProps<SignalMessage> = {
  allHandlers: {
    originHandlers: { [key: string]: (message: SignalMessage) => void } | undefined;
    newHandlers: { [key: string]: (message: SignalMessage) => void } | undefined;
  };
};

const useHandlesConcat = <SignalMessage = unknown,>(useProps: HandlesConcatProps<SignalMessage>) => {
  // props
  const { allHandlers } = useProps;

  useEffect(() => {
    const { newHandlers, originHandlers } = allHandlers;
    if (!newHandlers) return;
    if (!originHandlers) allHandlers.originHandlers = newHandlers;
    else allHandlers.originHandlers = { ...allHandlers.originHandlers, ...newHandlers };
  }, [allHandlers]);
};

const HandlersConcat = <SignalMessage = unknown,>() =>
  memo(
    (props: HandlesConcatProps<SignalMessage>) => {
      useHandlesConcat(props);
      return undefined;
    },
    (preProps, nextProps) => {
      const { originHandlers: preOgHs, newHandlers: preNewHs } = preProps.allHandlers;
      const { originHandlers: nextOgHs, newHandlers: nextNewHs } = nextProps.allHandlers;
      return isEqual(preOgHs, nextOgHs) && isEqual(preNewHs, nextNewHs);
    },
  );

export default HandlersConcat;
