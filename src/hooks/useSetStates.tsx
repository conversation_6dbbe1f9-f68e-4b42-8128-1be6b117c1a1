// libs
import { useCallback } from 'react';

type SetStatesProps = {};

const useSetStates = (useProps: SetStatesProps) => {
  // props
  const {} = useProps;

  const getSetStateResult: <T = unknown>(stateSet: React.SetStateAction<T>, pre: T) => T = useCallback(
    (stateSet, pre) => {
      if (stateSet instanceof Function) return stateSet(pre);
      return stateSet;
    },
    [],
  );

  const getStateWhenSet: <T = unknown>(statesSet: React.SetStateAction<T> | undefined, pre: T) => T = useCallback(
    (stateSet, pre) => {
      if (stateSet === undefined) return pre;
      return getSetStateResult(stateSet, pre);
    },
    [getSetStateResult],
  );

  return { getSetStateResult, getStateWhenSet };
};

export default useSetStates;
export type { SetStatesProps };
