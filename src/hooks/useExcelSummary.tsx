interface IExcelSummaryParams<T> {
  dataSource: T[];
  offset: number;
  keysToSum: (keyof T)[];
}
/**
 * Calculates the sum of specified fields in the provided data array and returns
 * the result as an array with empty strings prepended based on the offset.
 *
 * @param {Object[]} param.dataSource - The array of objects where each object
 * represents a row of data.
 *
 * @param {number} param.offset - The column index in Excel where the first
 * value should be placed. This determines how many empty strings will be
 * prepended to the result array.
 *
 * @param {Array<keyof T>} param.keysToSum - The keys of the objects in
 * `dataSource` whose values will be summed.
 *
 * @returns {(string | number)[]} - The resulting array with empty strings
 * (based on the offset) followed by the summed values for each key.
 */
export default function useExcelSummary<T>({
  dataSource,
  offset,
  keysToSum,
}: IExcelSummaryParams<T>): (string | number)[] {
  const initialTotals = keysToSum.reduce((acc, key) => ({ ...acc, [key]: 0 }), {} as Record<keyof T, number>);
  const totals = dataSource.reduce((acc, row) => {
    keysToSum.forEach((key) => {
      const value = row[key];
      acc[key] += typeof value === 'number' ? value : 0;
    });
    return acc;
  }, initialTotals);

  return Array(offset).fill('').concat(Object.values(totals));
}
