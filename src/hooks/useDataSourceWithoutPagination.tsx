// libs
import { useMemo, useState, useCallback, useEffect } from 'react';

type DataSourceProps<T, R> = {
  txInfo: R[] | undefined; // Input data is a pure array of type R
  mapper?: (item: R) => T; // Optional mapper to transform R to T
};

const useDataSource = <T, R>({ txInfo, mapper }: DataSourceProps<T, R>) => {
  // states
  const [internalTxInfo, setInternalTxInfo] = useState<R[]>(txInfo || []);

  // compute
  const dataSource = useMemo(() => {
    if (!internalTxInfo || !mapper) return [];
    return internalTxInfo.map((item) => {
      const mappedItem = mapper(item);
      // Merge local updates on top of mapped data
      return {
        ...mappedItem,
        ...item, // Apply local updates if any
      };
    });
  }, [internalTxInfo, mapper]);

  const updateItem = useCallback(
    (selector: (item: T) => boolean, updatedData: Partial<T>) => {
      setInternalTxInfo(
        (prev) =>
          prev.map((item) => {
            const transformed = mapper ? mapper(item) : (item as unknown as T);
            return selector(transformed)
              ? { ...transformed, ...updatedData } // Update matched item
              : transformed;
          }) as unknown as R[],
      );
    },
    [mapper],
  );

  const replaceTxInfo = useCallback((newTxInfo: R[]) => {
    setInternalTxInfo(newTxInfo);
  }, []);

  useEffect(() => {
    if (txInfo) {
      setInternalTxInfo(txInfo);
    }
  }, [txInfo]);

  return { dataSource, updateItem, replaceTxInfo };
};

export default useDataSource;
export type { DataSourceProps };
