// libs
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

// types
import {
  DepositAnomalyDto,
  EnergyLeaseFailedDto,
  MemberOrderNotifyDto,
  MerchantBalanceNotifyDto,
  MerchantTransDto,
  SupervisorTransDto,
} from '@/pages/withdraw/type';

// utils
import { logInfo } from '@/utils';

// hooks
import useAdminHubBuilder from './useAdminHubBuilder';
import useHandlesConcat from './useHandlesConcat';

type AdminHubRegisterProps = {
  MemberOrderNotify?: boolean | 'NoLogger';
  onMemberOrderNotify?: { [name: string]: (message: MemberOrderNotifyDto) => void };
  AbnormalDepositNotify?: boolean | 'NoLogger';
  onAbnormalDepositNotify?: { [name: string]: (message: DepositAnomalyDto) => void };
  SupervisorOrderNotify?: boolean | 'NoLogger';
  onSupervisorOrderNotify?: { [name: string]: (message: SupervisorTransDto) => void };
  MerchantOrderNotify?: boolean | 'NoLogger';
  onMerchantOrderNotify?: { [name: string]: (message: MerchantTransDto) => void };
  MerchantBalanceUpdateNotify?: boolean | 'NoLogger';
  onMerchantBalanceUpdateNotify?: { [name: string]: (message: MerchantBalanceNotifyDto) => void };
  EnergyLeaseFailedNotify?: boolean | 'NoLogger';
  onEnergyLeaseFailedNotify?: { [name: string]: (message: EnergyLeaseFailedDto) => void };
};

const useAdminHubRegister = (useProps: AdminHubRegisterProps) => {
  // hooks
  const { conn } = useAdminHubBuilder({});
  const queryClient = useQueryClient();

  // MemberOrderNotify
  const { MemberOrderNotify, onMemberOrderNotify } = useProps;
  useHandlesConcat<MemberOrderNotifyDto>()({
    allHandlers: {
      originHandlers: window.myGlobalVariable?.eventHandlersObj?.MemberOrderNotify,
      newHandlers: onMemberOrderNotify,
    },
  });
  useEffect(() => {
    if (!MemberOrderNotify || !conn) return () => {};

    conn.on('MemberOrderNotify', (message: MemberOrderNotifyDto) => {
      if (MemberOrderNotify !== 'NoLogger')
        logInfo({ Title: 'Get MemberOrderNotify Signal', message }, { origin: true });
      if (window.myGlobalVariable?.eventHandlersObj?.MemberOrderNotify) {
        Object.values(window.myGlobalVariable.eventHandlersObj.MemberOrderNotify).forEach((eachFunc) => {
          eachFunc(message);
        });
      }
    });
    return () => {
      if (conn) conn.off('MemberOrderNotify');
    };
  }, [MemberOrderNotify, conn, onMemberOrderNotify]);

  // AbnormalDepositNotify
  const { AbnormalDepositNotify, onAbnormalDepositNotify } = useProps;
  useHandlesConcat<DepositAnomalyDto>()({
    allHandlers: {
      originHandlers: window.myGlobalVariable?.eventHandlersObj?.AbnormalDepositNotify,
      newHandlers: onAbnormalDepositNotify,
    },
  });
  useEffect(() => {
    if (!AbnormalDepositNotify || !conn) return () => {};

    conn.on('AbnormalDepositNotify', (message: DepositAnomalyDto) => {
      if (AbnormalDepositNotify !== 'NoLogger')
        logInfo({ Title: 'Get AbnormalDepositNotify Signal', message }, { origin: true });
      if (window.myGlobalVariable?.eventHandlersObj?.AbnormalDepositNotify) {
        Object.values(window.myGlobalVariable.eventHandlersObj.AbnormalDepositNotify).forEach((eachFunc) => {
          eachFunc(message);
        });
      }
    });
    return () => {
      if (conn) conn.off('AbnormalDepositNotify');
    };
  }, [AbnormalDepositNotify, conn, onAbnormalDepositNotify]);

  // SupervisorOrderNotify
  const { SupervisorOrderNotify, onSupervisorOrderNotify } = useProps;
  useHandlesConcat<SupervisorTransDto>()({
    allHandlers: {
      originHandlers: window.myGlobalVariable?.eventHandlersObj?.SupervisorOrderNotify,
      newHandlers: onSupervisorOrderNotify,
    },
  });
  useEffect(() => {
    if (!SupervisorOrderNotify || !conn) return () => {};

    conn.on('SupervisorOrderNotify', (message: DepositAnomalyDto) => {
      if (SupervisorOrderNotify !== 'NoLogger')
        logInfo({ Title: 'Get SupervisorOrderNotify Signal', message }, { origin: true });
      if (window.myGlobalVariable?.eventHandlersObj?.SupervisorOrderNotify) {
        Object.values(window.myGlobalVariable.eventHandlersObj.SupervisorOrderNotify).forEach((eachFunc) => {
          eachFunc(message);
        });
      }
    });
    return () => {
      if (conn) conn.off('SupervisorOrderNotify');
    };
  }, [SupervisorOrderNotify, conn, onSupervisorOrderNotify]);

  // MerchantOrderNotify
  const { MerchantOrderNotify, onMerchantOrderNotify } = useProps;
  useHandlesConcat<MerchantTransDto>()({
    allHandlers: {
      originHandlers: window.myGlobalVariable?.eventHandlersObj?.MerchantOrderNotify,
      newHandlers: onMerchantOrderNotify,
    },
  });
  useEffect(() => {
    if (!MerchantOrderNotify || !conn) return () => {};

    conn.on('MerchantOrderNotify', (message: DepositAnomalyDto) => {
      if (MerchantOrderNotify !== 'NoLogger')
        logInfo({ Title: 'Get MerchantOrderNotify Signal', message }, { origin: true });
      if (window.myGlobalVariable?.eventHandlersObj?.MerchantOrderNotify) {
        Object.values(window.myGlobalVariable.eventHandlersObj.MerchantOrderNotify).forEach((eachFunc) => {
          eachFunc(message);
        });
      }
    });
    return () => {
      if (conn) conn.off('MerchantOrderNotify');
    };
  }, [MerchantOrderNotify, conn, onMerchantOrderNotify]);

  // MerchantBalanceUpdateNotify
  const { MerchantBalanceUpdateNotify, onMerchantBalanceUpdateNotify } = useProps;
  useHandlesConcat<MerchantBalanceNotifyDto>()({
    allHandlers: {
      originHandlers: window.myGlobalVariable?.eventHandlersObj?.MerchantBalanceUpdateNotify,
      newHandlers: onMerchantBalanceUpdateNotify,
    },
  });
  useEffect(() => {
    if (!MerchantBalanceUpdateNotify || !conn) return () => {};

    conn.on('MerchantBalanceUpdateNotify', (message: DepositAnomalyDto) => {
      if (MerchantBalanceUpdateNotify !== 'NoLogger')
        logInfo({ Title: 'Get MerchantBalanceUpdateNotify Signal', message }, { origin: true });
      if (window.myGlobalVariable?.eventHandlersObj?.MerchantBalanceUpdateNotify) {
        Object.values(window.myGlobalVariable.eventHandlersObj.MerchantBalanceUpdateNotify).forEach((eachFunc) => {
          eachFunc(message);
        });
      }
    });
    return () => {
      if (conn) conn.off('MerchantBalanceUpdateNotify');
    };
  }, [MerchantBalanceUpdateNotify, conn, onMerchantBalanceUpdateNotify]);

  // EnergyLeaseFailedNotify
  const { EnergyLeaseFailedNotify, onEnergyLeaseFailedNotify } = useProps;
  useHandlesConcat<EnergyLeaseFailedDto>()({
    allHandlers: {
      originHandlers: window.myGlobalVariable?.eventHandlersObj?.EnergyLeaseFailed,
      newHandlers: onEnergyLeaseFailedNotify,
    },
  });
  useEffect(() => {
    if (!EnergyLeaseFailedNotify || !conn) return () => {};

    queryClient.invalidateQueries({ queryKey: ['lease', 'order', 'list'] });
    conn.on('EnergyLeaseFailedNotify', (message: EnergyLeaseFailedDto) => {
      if (EnergyLeaseFailedNotify !== 'NoLogger')
        logInfo({ Title: 'Get EnergyLeaseFailedNotify Signal', message }, { origin: true });
      if (window.myGlobalVariable?.eventHandlersObj?.EnergyLeaseFailed) {
        Object.values(window.myGlobalVariable.eventHandlersObj.EnergyLeaseFailed).forEach((eachFunc) => {
          eachFunc(message);
        });
      }
      queryClient.invalidateQueries({ queryKey: ['lease', 'order', 'list'] });
    });
    return () => {
      if (conn) conn.off('EnergyLeaseFailedNotify');
    };
  }, [EnergyLeaseFailedNotify, conn, onEnergyLeaseFailedNotify, queryClient]);
};

export default useAdminHubRegister;
export type { AdminHubRegisterProps };
