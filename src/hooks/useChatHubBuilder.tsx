// libs
import { useEffect, useRef } from 'react';
import { HubConnection, HubConnectionBuilder, HubConnectionState } from '@microsoft/signalr';

// store
import { useUserStore } from '@/store';

// utils
import { logInfo, logWarn } from '@/utils';

const url = `${import.meta.env.VITE_HUBS_ROOT}/chatHub`;

type UseChatHubBuilderProps = {};
const useChatHubBuilder = (useProps: UseChatHubBuilderProps) => {
  // props
  const {} = useProps;

  // refs
  const connectionRef = useRef<HubConnection>();

  // hooks
  const { loginRes } = useUserStore();

  useEffect(() => {
    const connectionCurrent = window.myGlobalVariable?.chatHubConnection as HubConnection | undefined;
    if (!loginRes) {
      if (connectionCurrent) {
        logInfo('Chat hub: Preparing to disconnection from the Server');
        connectionCurrent
          ?.stop()
          .then(() => {
            logInfo('Chat hub: Disconnection success');
          })
          .catch((error) => {
            logWarn({ Title: 'Chat hub disconnection error', error });
          });
      }
      return;
    }
    if (connectionCurrent) {
      if (connectionCurrent.state === HubConnectionState.Disconnected) connectionCurrent.start();
      return;
    }

    connectionRef.current = new HubConnectionBuilder()
      .withUrl(url, { accessTokenFactory: () => loginRes.token })
      .build();
    connectionRef.current
      .start()
      .then(() => {
        logInfo('Chat hub connected');
      })
      .catch((error) => {
        logWarn({ Title: 'Chat hub connect failed', error });
      });

    window.myGlobalVariable = { ...window.myGlobalVariable, chatHubConnection: connectionRef.current };
  }, [loginRes]);

  return { connectionRef };
};

export default useChatHubBuilder;
export type { UseChatHubBuilderProps };
