// libs
import dayjs from 'dayjs';

// utils
import { ClientEnvironmentEnum, CryptoCurrencyEnum, CryptoProtocolEnum, LimitTimeInterval } from '@/utils';

type FormInitProps = {
  type: 'createClient' | 'setCallback' | 'createWallet' | 'calReport' | 'login';
  isTest: boolean;
};

const useFormInit = (useProps: FormInitProps) => {
  // props
  const { type, isTest } = useProps;

  if (type === 'login' && isTest && import.meta.env.DEV)
    return {
      userID: 'admin1',
      code: 'Aa123456',
    };

  if (type === 'calReport' && isTest && import.meta.env.DEV)
    return {
      currency: CryptoCurrencyEnum.USDT,
      protocol: CryptoProtocolEnum.TRC20,
      dateRange: { from: dayjs().startOf('d').subtract(7, 'd'), to: dayjs().startOf('d') },
    };

  if (type === 'calReport')
    return {
      currency: CryptoCurrencyEnum.USDT,
      protocol: CryptoProtocolEnum.TRC20,
      dateRange: { from: dayjs().startOf('d').subtract(7, 'd'), to: dayjs().startOf('d') },
    };

  if (type === 'setCallback' && isTest && import.meta.env.DEV)
    return {
      callbackUrl: 'http://127.0.0.1:3048/callback',
    };

  if (type === 'createClient' && isTest && import.meta.env.DEV)
    return {
      init: { clientName: 'TestClient' },
      getItem: (count: number) => {
        return {
          merchantName: `MerchantName ${count}`,
          merchantEnvType: ClientEnvironmentEnum.Formal,
          入金: {
            amountRange: { from: 1234, to: 2345 },
            trc20Fee: 23,
            erc20Fee: 23,
            vfyAutoMode: true,
            autoOffset: 23,
            payTimeLimit: LimitTimeInterval.OneH,
            limitTimes: {
              limitTime: LimitTimeInterval.OneH,
              limitTimes: 3,
            },
            dailyLimit: {
              limitTimes: 5,
              resetAt: dayjs().set('h', 0).set('m', 0).set('s', 0),
            },
            white: [{ address: 'address' }],
          },
        };
      },
    };

  if (type === 'createClient')
    return {
      init: { clientName: undefined },
      getItem: () => undefined,
    };

  return {};
};

export default useFormInit;
export type { FormInitProps };
