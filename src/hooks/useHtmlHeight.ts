// libs
import { useCallback } from 'react';

const useHtmlHeight = () => {
  const addSuffixAttributes = useCallback((element: HTMLElement, suffix = '__suffix') => {
    const children = element.querySelectorAll('*');

    children.forEach((child) => {
      if (child.id) {
        child.setAttribute('id', `${child.id}${suffix}`);
      }
    });
  }, []);

  const getElementHeight = useCallback(
    (element: HTMLElement, width = '500px') => {
      const tempDiv = document.createElement('div');
      const pureElement = element.cloneNode(true);
      addSuffixAttributes(pureElement as HTMLElement);

      tempDiv.appendChild(pureElement);

      // 設置 div 樣式避免影響頁面布局
      tempDiv.style.position = 'absolute';
      tempDiv.style.visibility = 'hidden';
      tempDiv.style.overflow = 'hidden';
      tempDiv.style.left = '0px';
      tempDiv.style.top = '200px';
      tempDiv.style.width = width;
      tempDiv.style.height = 'fit-content';

      document.body.appendChild(tempDiv);
      const height = tempDiv.scrollHeight;
      document.body.removeChild(tempDiv);

      return height;
    },
    [addSuffixAttributes],
  );

  const getHeightByCanvas = useCallback((paintingsHTML: string) => {
    // 創建一個 canvas 元素
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    if (!context) {
      throw new Error('Unable to get canvas context');
    }

    // 設定 canvas 的寬度和高度
    canvas.width = 800; // 設定適合的寬度
    canvas.height = 10000; // 設定一個足夠大的高度來容納內容

    // 設定文本樣式
    context.font = '16px Arial'; // 設定文本的字體和大小
    context.textBaseline = 'top';

    // 使用 canvas 渲染文本來計算高度
    const lines = paintingsHTML.split('\n');
    let maxHeight = 0;

    lines.forEach((line) => {
      const metrics = context.measureText(line);
      const lineHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;

      maxHeight += lineHeight;
    });

    // 返回計算出的高度
    return maxHeight;
  }, []);

  return { addSuffixAttributes, getElementHeight, getHeightByCanvas };
};

export default useHtmlHeight;
