// libs
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

interface IUseProps {
  translator: string;
}

const useTranslateExcelTitleRow = ({ translator }: IUseProps) => {
  const { t } = useTranslation(translator);

  const translateExcelTitleRow = useCallback((titleRows: Array<string>) => titleRows.map((row) => t(row)), [t]);

  return { translateExcelTitleRow };
};

export default useTranslateExcelTitleRow;
export type { IUseProps };
