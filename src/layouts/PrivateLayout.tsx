import { Layout, Space } from 'antd';
import { Outlet } from 'react-router-dom';
// prettier-ignore
import { HomeOutlined, SettingOutlined, LogoutOutlined, LoginOutlined, WalletOutlined, FieldTimeOutlined, FileExclamationOutlined, AuditOutlined, UserOutlined, FileTextOutlined, } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useUserStore } from '@/store';
import Menu from '@/components/Menu';
import PrivateHeader from './components/PrivateHeader';

interface IPrivateLayoutProps {}
const PrivateLayout: React.FunctionComponent<IPrivateLayoutProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { isDark, info } = useUserStore();
  const { t } = useTranslation('privateLayout');

  return (
    <Layout className='min-h-screen'>
      <Layout.Header className='sticky top-0 z-10 flex items-center justify-between shadow-sm '>
        <PrivateHeader />
      </Layout.Header>

      <Layout>
        <Layout.Sider theme={isDark ? 'dark' : 'light'}>
          <Menu
            prefix='/private'
            containerProps={{ className: 'mt-4' }}
            vertical
            items={[
              {
                label: (
                  <Space>
                    <HomeOutlined />
                    {t('overview')}
                  </Space>
                ),
                to: '/private',
                key: '',
              },
              {
                label: (
                  <Space>
                    <LoginOutlined />
                    {t('deposit')}
                  </Space>
                ),
                to: 'deposit',
                key: 'deposit',
              },
              {
                label: (
                  <Space>
                    <LogoutOutlined />
                    {t('withdraw')}
                  </Space>
                ),
                to: 'withdraw',
                key: 'withdraw',
              },
              ...(info?.roles.includes('SystemSupervisor')
                ? [
                    {
                      label: (
                        <Space>
                          <UserOutlined />
                          {t('supervisor')}
                        </Space>
                      ),
                      to: 'supervisor',
                      key: 'supervisor',
                    },
                  ]
                : []),
              {
                label: (
                  <Space>
                    <AuditOutlined />
                    {t('merchantWithdraw')}
                  </Space>
                ),
                to: 'merchantOrder',
                key: 'merchantOrder',
              },
              {
                label: (
                  <Space>
                    <WalletOutlined />
                    {t('uuppWallet')}
                  </Space>
                ),
                to: 'wallet',
                key: 'wallet',
              },
              {
                label: (
                  <Space>
                    <FileExclamationOutlined />
                    {t('anomalyDeposit')}
                  </Space>
                ),
                to: 'abDep', // Abnormal Deposit
                key: 'abDep',
              },
              {
                label: (
                  <Space>
                    <SettingOutlined />
                    {t('setting')}
                  </Space>
                ),
                to: 'admin',
                key: 'admin',
              },
              {
                label: (
                  <Space>
                    <FieldTimeOutlined />
                    {t('shift')}
                  </Space>
                ),
                to: 'shift',
                key: 'shift',
              },
              {
                label: (
                  <Space>
                    <FileTextOutlined />
                    {t('lease')}
                  </Space>
                ),
                to: 'lease',
                key: 'lease',
              },
            ]}
          />
        </Layout.Sider>

        <Layout.Content className='p-3'>
          <Outlet />
        </Layout.Content>
      </Layout>
    </Layout>
  );
};

export default PrivateLayout;
