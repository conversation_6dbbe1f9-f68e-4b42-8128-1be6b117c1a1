// libs
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Drawer, List, Space, Badge, Button, Descriptions } from 'antd';
import {
  BellOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  SyncOutlined,
  TransactionOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// hooks
import useAdminSignalRConnection from '@/hooks/useAdminSignalRConnection';
import { useIsDemo } from '@/hooks';

// store
import { useUserStore } from '@/store';

// types
import {
  MemberOrderNotifyDto,
  MerchantTransDto,
  OrderStatus,
  SupervisorTransaction,
  TransactionStatus,
  TransactionType,
} from '@/pages/withdraw/type';

// utils
import { CryptoEnum } from '@/utils';

// components
import { TagCryptoType, TagTxStatus } from '@/components/TagAlpha';
import TagMerchantTxStatus from '@/components/TagAlpha/TagMerchantTransactionStatus';
import { Txt } from '@/components/TypographyMaster';
import NotificationCard from './NotificationCard';

const Notification: React.FC = () => {
  // states
  const [notifications, setNotifications] = useState<any[]>([]);
  const [readNotifications, setReadNotifications] = useState<any[]>([]); // used to calculate the number of new notifications
  const [newNotificationCount, setNewNotificationCount] = useState<number>(0); // used to display the number of new notifications
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);

  // hooks
  const { registerHandler, unregisterHandler } = useAdminSignalRConnection({});
  const { soundEnable } = useUserStore();
  const { isDark } = useUserStore();
  const navigate = useNavigate();
  const { t } = useTranslation('notification');
  const isDemo = useIsDemo();

  // handlers
  const handleShowDrawer = () => {
    setReadNotifications(notifications); // marks all notifications as read
    setIsDrawerVisible(true);
  };
  const handleCloseDrawer = () => setIsDrawerVisible(false);

  // --- Play Sound ---
  const playSound = (type: string) => {
    switch (type) {
      case 'Deposit':
        window.myGlobalVariable?.audio?.deposit?.play();
        break;
      case 'Withdrawal':
        window.myGlobalVariable?.audio?.withdrawal?.play();
        break;
      case 'Anomaly':
        window.myGlobalVariable?.audio?.anamoly?.play();
        break;
      default:
        break;
    }
  };
  const formatNotificationDetails = (item: any) => {
    const { originalData, isMemberNotification } = item;

    return (
      <Descriptions
        bordered
        size='small'
        column={1} // Display details in a single column
      >
        {isMemberNotification ? (
          <>
            <Descriptions.Item label={t('merchantLabel')}>
              <div className='flex flex-col'>
                <Space>
                  <Txt type='secondary'>{t('serialNumber')}</Txt>
                  <Txt>{originalData.merchantNumber}</Txt>
                </Space>
                <Space>
                  <Txt type='secondary'>{t('name')}</Txt>
                  <Txt>{originalData.merchantName}</Txt>
                </Space>
              </div>
            </Descriptions.Item>
            <Descriptions.Item label={t('orderUidLabel')}>{originalData.orderUid}</Descriptions.Item>
            <Descriptions.Item label={t('merchantOrderIdLabel')}>{originalData.merchantOrderId}</Descriptions.Item>
            <Descriptions.Item label={t('systemOrderIdLabel')}>{originalData.systemOrderId}</Descriptions.Item>
            <Descriptions.Item label={t('amountLabel')}>
              {originalData.requireAmount}
              <span className='relative top-[-1px] ml-1'>
                <TagCryptoType
                  customSize={16}
                  includeName={false}
                  cryptoType={originalData.order.cryptoType}
                />
              </span>
            </Descriptions.Item>
            <Descriptions.Item label={t('statusLabel')}>
              <TagTxStatus status={originalData.order.status} />
            </Descriptions.Item>
            <Descriptions.Item label={t('createAtLabel')}>
              {new Date(originalData.createdAt).toLocaleString()}
            </Descriptions.Item>
          </>
        ) : (
          <>
            <Descriptions.Item label={t('merchantLabel')}>
              <div className='flex flex-col'>
                <Space>
                  <Txt type='secondary'>{t('serialNumber')}</Txt>
                  <Txt>{originalData.merchantNumber}</Txt>
                </Space>
                <Space>
                  <Txt type='secondary'>{t('name')}</Txt>
                  <Txt>{originalData.merchantName}</Txt>
                </Space>
              </div>
            </Descriptions.Item>
            <Descriptions.Item label={t('idLabel')}>{originalData.id}</Descriptions.Item>
            <Descriptions.Item label={t('fromLabel')}>{originalData.from ?? '--'}</Descriptions.Item>
            <Descriptions.Item label={t('toLabel')}>{originalData.order.to}</Descriptions.Item>
            <Descriptions.Item label={t('actualAmountLabel')}>
              {originalData.actualAmount}

              <span className='relative top-[-1px] ml-1'>
                <TagCryptoType
                  customSize={16}
                  includeName={false}
                  cryptoType={originalData.order.cryptoType}
                />
              </span>
            </Descriptions.Item>
            <Descriptions.Item label={t('statusLabel')}>
              <TagMerchantTxStatus status={originalData.order.status} />
            </Descriptions.Item>
            <Descriptions.Item label={t('createAtLabel')}>
              {new Date(originalData.createdAt).toLocaleString()}
            </Descriptions.Item>
          </>
        )}
      </Descriptions>
    );
  };
  // --- Generate Notification Content ---
  const generateNotificationContent = useCallback(
    (data: any, isMemberNotification: boolean) => {
      const {
        transactionType: type,
        status,
        requireAmount: amount,
        cryptoType,
      } = data.order as {
        transactionType: TransactionType;
        status: OrderStatus | TransactionStatus;
        requireAmount: number;
        actualAmonut: number;
        cryptoType: CryptoEnum;
      };

      const transactionType = type === TransactionType.Deposit ? 'deposit' : 'withdrawal';
      let statusMessage = '';
      if (isMemberNotification) {
        switch (status as OrderStatus) {
          case OrderStatus.Created:
            statusMessage = t('createdStatusMessage');
            break;
          case OrderStatus.BlockchainBroadcast:
            statusMessage = t('blockchainBroadcastStatusMessage');
            break;
          case OrderStatus.BlockchainConfirmed:
            statusMessage = t('blockchainConfirmedStatusMessage');
            break;
          case OrderStatus.Completed:
            statusMessage = t('completedStatusMessage');
            break;
          case OrderStatus.Canceled:
            statusMessage = t('canceledStatusMessage');
            break;
          case OrderStatus.BlockchainFailed:
            statusMessage = t('blockchainFailedStatusMessage');
            break;
          case OrderStatus.CallbackFail:
            statusMessage = t('callbackFailStatusMessage');
            break;
          case OrderStatus.Timeout:
            statusMessage = t('timeoutStatusMessage');
            break;
          case OrderStatus.Retry:
            statusMessage = t('retryStatusMessage');
            break;
          default:
            statusMessage = t('unknownStatusMessage');
            break;
        }
      } else {
        switch (status as TransactionStatus) {
          case TransactionStatus.PendingReview:
            statusMessage = t('pendingReviewStatusMessage');
            break;
          case TransactionStatus.Approved:
            statusMessage = t('approvedStatusMessage');
            break;
          case TransactionStatus.InProgress:
            statusMessage = t('inProgressStatusMessage');
            break;
          case TransactionStatus.BlockchainBroadcast:
            statusMessage = t('blockchainBroadcastStatusMessage');
            break;
          case TransactionStatus.BlockchainConfirmed:
            statusMessage = t('blockchainConfirmedStatusMessage');
            break;
          case TransactionStatus.Canceled:
            statusMessage = t('canceledStatusMessage');
            break;
          case TransactionStatus.ResourceInsufficient:
            statusMessage = t('resourceInsufficientStatusMessage');
            break;
          case TransactionStatus.BlockchainFailed:
            statusMessage = t('blockchainFailedStatusMessage');
            break;
          case TransactionStatus.OtherError:
            statusMessage = t('otherErrorStatusMessage');
            break;
          default:
            statusMessage = t('unknownStatusMessage');
            break;
        }
      }

      return (
        <span>
          <strong>{t(transactionType)}</strong> {t('of')} <strong>{amount}</strong>{' '}
          <span className='relative top-[-1px]'>
            <TagCryptoType
              includeName={false}
              customSize={18}
              cryptoType={cryptoType}
            />
            {statusMessage}
          </span>
        </span>
      );
    },
    [t],
  );

  // --- Get Tag Color ---
  const getIcon = (status: OrderStatus | TransactionStatus, isMemberNotification: boolean) => {
    if (isMemberNotification) {
      switch (status) {
        case OrderStatus.Created:
          return <LoadingOutlined style={{ color: 'gold' }} />;
        case OrderStatus.BlockchainBroadcast:
          return <SyncOutlined style={{ color: 'cyan' }} />;
        case OrderStatus.BlockchainConfirmed:
          return <CheckCircleOutlined style={{ color: 'blue' }} />;
        case OrderStatus.Completed:
          return <CheckCircleOutlined style={{ color: 'green' }} />;
        case OrderStatus.Canceled:
          return <CloseCircleOutlined style={{ color: 'red' }} />;
        case OrderStatus.BlockchainFailed:
          return <WarningOutlined style={{ color: 'volcano' }} />;
        case OrderStatus.CallbackFail:
          return <ExclamationCircleOutlined style={{ color: 'purple' }} />;
        default:
          return <BellOutlined style={{ color: isDark ? 'white' : 'black' }} />;
      }
    } else {
      switch (status) {
        case TransactionStatus.PendingReview:
          return <ClockCircleOutlined style={{ color: 'gold' }} />;
        case TransactionStatus.Approved:
          return <CheckCircleOutlined style={{ color: 'blue' }} />;
        case TransactionStatus.InProgress:
          return <SyncOutlined style={{ color: 'cyan' }} />;
        case TransactionStatus.BlockchainBroadcast:
          return <TransactionOutlined style={{ color: 'purple' }} />;
        case TransactionStatus.BlockchainConfirmed:
          return <CheckCircleOutlined style={{ color: 'green' }} />;
        case TransactionStatus.Canceled:
          return <CloseCircleOutlined style={{ color: 'red' }} />;
        case TransactionStatus.ResourceInsufficient:
          return <WarningOutlined style={{ color: 'orange' }} />;
        case TransactionStatus.BlockchainFailed:
          return <WarningOutlined style={{ color: 'volcano' }} />;
        case TransactionStatus.OtherError:
          return <ExclamationCircleOutlined style={{ color: 'gray' }} />;
        default:
          return <BellOutlined style={{ color: isDark ? 'white' : 'black' }} />;
      }
    }
  };
  const getTagColor = (status: OrderStatus | TransactionStatus, isMemberNotification: boolean) => {
    if (isMemberNotification) {
      switch (status) {
        case OrderStatus.Created:
          return 'gold';
        case OrderStatus.BlockchainBroadcast:
          return 'cyan';
        case OrderStatus.BlockchainConfirmed:
          return 'blue';
        case OrderStatus.Completed:
          return 'green';
        case OrderStatus.Canceled:
          return 'red';
        case OrderStatus.BlockchainFailed:
          return 'volcano';
        case OrderStatus.CallbackFail:
          return 'purple';
        default:
          return 'default';
      }
    } else {
      switch (status) {
        case TransactionStatus.PendingReview:
          return 'gold';
        case TransactionStatus.Approved:
          return 'blue';
        case TransactionStatus.InProgress:
          return 'cyan';
        case TransactionStatus.BlockchainBroadcast:
          return 'purple';
        case TransactionStatus.BlockchainConfirmed:
          return 'green';
        case TransactionStatus.Canceled:
          return 'red';
        case TransactionStatus.ResourceInsufficient:
          return 'orange';
        case TransactionStatus.BlockchainFailed:
          return 'volcano';
        case TransactionStatus.OtherError:
          return 'gray';
        default:
          return 'default';
      }
    }
  };

  // --- Get Card Style ---
  const getCardStyle = (status: OrderStatus | TransactionStatus, isMemberNotification: boolean) => {
    const baseStyle = (border: string, background: string) => ({
      border,
      background: isDark ? 'rgba(0,0,0,0)' : background,
    });

    if (isMemberNotification) {
      switch (status) {
        case OrderStatus.Created:
          return baseStyle('1px solid gold', '#fffbe6'); // Soft gold for pending processing
        case OrderStatus.BlockchainBroadcast:
          return baseStyle('1px solid cyan', '#e6f7ff'); // Light cyan for broadcasted
        case OrderStatus.BlockchainConfirmed:
          return baseStyle('1px solid blue', '#e6f7ff'); // Calm blue for blockchain confirmed
        case OrderStatus.Completed:
          return baseStyle('1px solid green', '#f6ffed'); // Soft green for success
        case OrderStatus.Canceled:
          return baseStyle('1px solid red', '#fff1f0'); // Light red for canceled
        case OrderStatus.BlockchainFailed:
          return baseStyle('1px solid volcano', '#fff2e8'); // Orange-red for blockchain failure
        case OrderStatus.CallbackFail:
          return baseStyle('1px solid purple', '#f9f0ff'); // Light purple for callback failure
        default:
          return baseStyle('1px solid gray', '#f5f5f5'); // Neutral gray for unknown
      }
    } else {
      switch (status) {
        case TransactionStatus.PendingReview:
          return baseStyle('1px solid gold', '#fffbe6'); // Soft gold for admin review
        case TransactionStatus.Approved:
          return baseStyle('1px solid blue', '#e6f7ff'); // Calm blue for approved
        case TransactionStatus.InProgress:
          return baseStyle('1px solid cyan', '#e6f7ff'); // Light cyan for in-progress
        case TransactionStatus.BlockchainBroadcast:
          return baseStyle('1px solid purple', '#f9f0ff'); // Light purple for blockchain broadcast
        case TransactionStatus.BlockchainConfirmed:
          return baseStyle('1px solid green', '#f6ffed'); // Soft green for blockchain confirmed
        case TransactionStatus.Canceled:
          return baseStyle('1px solid red', '#fff1f0'); // Light red for canceled
        case TransactionStatus.ResourceInsufficient:
          return baseStyle('1px solid orange', '#fff7e6'); // Light orange for resource insufficient
        case TransactionStatus.BlockchainFailed:
          return baseStyle('1px solid volcano', '#fff2e8'); // Orange-red for blockchain failure
        case TransactionStatus.OtherError:
          return baseStyle('1px solid gray', '#f5f5f5'); // Neutral gray for errors
        default:
          return baseStyle('1px solid gray', '#f5f5f5'); // Default fallback style
      }
    }
  };
  // --- Handle Notification ---
  const handleMemberNotification = useCallback(
    (data: MemberOrderNotifyDto) => {
      const type = data.transactionType === TransactionType.Deposit ? 'deposit' : 'withdrawal';

      const notification = {
        id: data.orderUid,
        type,
        originalData: data,
        content: generateNotificationContent(data, true),
        status: data.status,
        isMemberNotification: true,
        time: new Date(data.confirmedAt || data.createdAt),
      };

      setNotifications((prev) => [notification, ...prev]);
      if (soundEnable) playSound(type);
    },
    [soundEnable, generateNotificationContent],
  );

  const handleMerchantNotification = useCallback(
    (data: MerchantTransDto) => {
      const type = data.transactionType === TransactionType.Deposit ? 'deposit' : 'withdrawal';

      const notification = {
        id: data.id,
        type,
        originalData: data,
        content: generateNotificationContent(data, false),
        status: data.status,
        isMemberNotification: false,
        time: new Date(data.confirmedAt || data.createdAt),
      };

      setNotifications((prev) => [notification, ...prev]);
      if (soundEnable) playSound(type);
    },
    [soundEnable, generateNotificationContent],
  );
  const handleSupervisorNotification = useCallback(
    (data: SupervisorTransaction) => {
      const type = data.transactionType === TransactionType.Deposit ? 'deposit' : 'withdrawal';

      const notification = {
        id: data.id,
        type,
        originalData: data,
        content: generateNotificationContent(data, false),
        status: data.status,
        isMemberNotification: false,
        time: new Date(data.confirmedAt || data.createdAt),
      };

      setNotifications((prev) => [notification, ...prev]);
      if (soundEnable) playSound(type);
    },
    [soundEnable, generateNotificationContent],
  );

  // updates the number of new notifications when they arrive or are read
  useEffect(() => {
    setNewNotificationCount(notifications.length - readNotifications.length);
  }, [notifications.length, readNotifications.length]);
  useEffect(() => {
    registerHandler('MemberOrderNotify', handleMemberNotification);
    registerHandler('MerchantOrderNotify', handleMerchantNotification);
    registerHandler('SupervisorOrderNotify', handleSupervisorNotification);

    return () => {
      unregisterHandler('MemberOrderNotify', handleMemberNotification);
      unregisterHandler('MerchantOrderNotify', handleMerchantNotification);
      unregisterHandler('SupervisorOrderNotify', handleSupervisorNotification);
    };
  }, [
    handleMemberNotification,
    handleMerchantNotification,
    registerHandler,
    soundEnable,
    unregisterHandler,
    handleSupervisorNotification,
  ]);

  const handleNavigate = (item: any) => {
    const { type, originalData, isMemberNotification } = item;

    let baseRoute = '';
    let state = {};

    if (isMemberNotification) {
      baseRoute = type === 'Deposit' ? '/private/deposit' : '/private/withdrawal';
      state = { OrderUid: originalData.orderUid };
    } else {
      baseRoute = type === 'Deposit' ? '/private/deposit' : '/private/merchant'; // Withdrawals go to merchant
      state = { OrderUid: originalData.id };
    }
    setIsDrawerVisible(false);
    navigate(baseRoute, { replace: true, state });
  };

  const colorStyle = useMemo(() => {
    if (isDemo && isDark) return '#fff';
    if (isDemo && !isDark) return '#fff';
    if (isDark) return '';
    if (!isDark) return '';
    return '';
  }, [isDark, isDemo]);

  return (
    <>
      <Badge count={newNotificationCount}>
        <Button
          type='text'
          icon={<BellOutlined style={{ fontSize: '18px' }} />}
          onClick={handleShowDrawer}
          style={{ color: colorStyle }}
        />
      </Badge>
      <Drawer
        title={t('title')}
        placement='right'
        onClose={handleCloseDrawer}
        open={isDrawerVisible}
        width={450}
      >
        <List
          itemLayout='horizontal'
          dataSource={notifications}
          renderItem={(item) => (
            <NotificationCard
              item={item}
              handleNavigate={handleNavigate}
              getCardStyle={getCardStyle}
              getTagColor={getTagColor}
              getIcon={getIcon}
              formatNotificationDetails={formatNotificationDetails}
              isDark
            />
          )}
        />
      </Drawer>
    </>
  );
};

export default Notification;
