// libs
import { <PERSON><PERSON><PERSON>, Flex, Avatar, Divider } from 'antd';
import { useTranslation } from 'react-i18next';

// assets
import usdt from '@/assets/usdt.png';

// store
import { useUserStore } from '@/store';

// utils
import { CryptoEnum, nTot } from '@/utils';

// components
import { Txt } from '@/components/TypographyMaster';
import { useIsDemo } from '@/hooks';
import { useMemo } from 'react';

interface IBalanceIconProps {
  type: CryptoEnum;
  balance: number | string | undefined;
  avb: number | string | undefined;
}

const BalanceIcon: React.FunctionComponent<IBalanceIconProps> = (props) => {
  // props
  const { type, balance, avb } = props || {};

  // hooks
  const { isDark } = useUserStore();
  const { t } = useTranslation('balanceIcon');
  const isDemo = useIsDemo();

  const borderStyle = useMemo(() => {
    if (isDemo && isDark) return '1px solid #fff';
    if (isDemo && !isDark) return '1px solid #fff';
    if (isDark) return '1px solid #434343';
    if (!isDark) return '1px solid #d9d9d9';
    return '';
  }, [isDark, isDemo]);

  const colorStyle = useMemo(() => {
    if (isDemo && isDark) return '#fff';
    if (isDemo && !isDark) return '#fff';
    if (isDark) return '';
    if (!isDark) return '';
    return '';
  }, [isDark, isDemo]);

  if (type === CryptoEnum.TRC20_USDT)
    return (
      <Tooltip title={t('trc20Tooltip')}>
        <Flex
          gap={5}
          align='center'
          className='mr-2 rounded-full pr-2'
          style={{ border: borderStyle }}
        >
          <Avatar
            src={usdt}
            size='small'
          />
          <Txt type='secondary'>{nTot({ value: balance, digitsType: type })}</Txt>
          <Divider
            type='vertical'
            className='top-0 m-0'
          />
          <Txt style={{ color: colorStyle }}>{nTot({ value: avb, digitsType: type })}</Txt>
        </Flex>
      </Tooltip>
    );
  return undefined;
};

export default BalanceIcon;
