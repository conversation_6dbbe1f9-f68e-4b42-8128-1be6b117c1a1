// libs
import { <PERSON><PERSON>, But<PERSON>, Card, Divider, List, Space, Tag, Typography } from 'antd';
import { ArrowRightOutlined } from '@ant-design/icons';
import { useAnimation, motion } from 'framer-motion';

// types
import { OrderStatus, TransactionStatus } from '@/pages/withdraw/type';

// utils
import { MerchantTransactionStatus } from '@/utils';
import { getEnumKeyByEnumValue } from '@/utils/getEnumKeyByValue';

interface INotificationCardProps {
  item: any;
  isDark: boolean;
  handleNavigate: (itTraem: any) => void;
  getCardStyle: (status: OrderStatus | TransactionStatus, isMemberNotification: boolean) => React.CSSProperties;
  getTagColor: (status: OrderStatus | TransactionStatus, isMemberNotification: boolean) => string;
  getIcon: (status: OrderStatus | TransactionStatus, isMemberNotification: boolean) => React.ReactNode;
  formatNotificationDetails: (item: any) => React.ReactNode;
}

const NotificationCard: React.FC<INotificationCardProps> = ({
  item,
  handleNavigate,
  getCardStyle,
  getTagColor,
  getIcon,
  formatNotificationDetails,
}: INotificationCardProps) => {
  // hooks
  const overlayControls = useAnimation();

  // compute
  const makeTransparentColor = (baseColor: string, opacity: number) => {
    const colors: { [key: string]: string } = {
      gold: 'rgba(255, 193, 7',
      cyan: 'rgba(24, 144, 255',
      blue: 'rgba(64, 158, 255',
      green: 'rgba(82, 196, 26',
      red: 'rgba(245, 34, 45',
      volcano: 'rgba(245, 108, 108',
      purple: 'rgba(120, 85, 255',
      orange: 'rgba(255, 140, 0',
      gray: 'rgba(128, 128, 128',
      default: 'rgba(200, 200, 200',
    };

    const rgba = colors[baseColor] || colors.default;
    return `${rgba}, ${opacity})`;
  };

  // handlers
  const handleHoverStart = () => {
    overlayControls.start({ x: 0 });
  };
  const handleHoverEnd = () => {
    overlayControls.start({ x: '-100%' });
  };

  return (
    <motion.div
      style={{
        position: 'relative',
        overflow: 'hidden',
        borderRadius: '8px',
        marginBottom: '16px',
      }}
      onMouseEnter={handleHoverStart}
      onMouseLeave={handleHoverEnd}
    >
      <Card
        style={{
          ...getCardStyle(item.originalData.order.status, item.isMemberNotification),
          borderRadius: '8px',
          overflow: 'hidden',
        }}
        styles={{
          body: {
            padding: '12px',
          },
        }}
      >
        <List.Item>
          <List.Item.Meta
            title={
              <div className='flex gap-2'>
                <Avatar
                  className='flex-shrink-0 bg-inherit'
                  icon={getIcon(item.originalData.order.status, item.isMemberNotification)}
                  size={50}
                />
                <div className='flex flex-col gap-2'>
                  <Space>
                    <Typography.Text strong>{item.content}</Typography.Text>
                  </Space>
                  <div className='flex justify-between gap-2'>
                    <Typography.Text type='secondary'>{item.time.toLocaleString()}</Typography.Text>

                    <Tag color={getTagColor(item.originalData.order.status, item.isMemberNotification)}>
                      {item.isMemberNotification
                        ? getEnumKeyByEnumValue(OrderStatus, item.originalData.order.status)
                        : getEnumKeyByEnumValue(MerchantTransactionStatus, item.originalData.order.status)}
                    </Tag>
                  </div>
                </div>
              </div>
            }
            description={
              <>
                <Divider style={{ margin: '12px 0' }} />
                {formatNotificationDetails(item)}
              </>
            }
          />
        </List.Item>
      </Card>

      <motion.div
        initial={{ x: '-100%' }}
        animate={overlayControls}
        transition={{ duration: 0.3 }}
        onClick={() => handleNavigate(item)}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          height: '22%',
          width: '100%',
          background: makeTransparentColor(getTagColor(item.originalData.order.status, item.isMemberNotification), 1), // Light blue for withdrawals
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1,
          borderRadius: '8px',
          cursor: 'pointer',
        }}
      >
        <Button
          type='primary'
          shape='circle'
          icon={<ArrowRightOutlined />}
          size='large'
          style={{
            backgroundColor: makeTransparentColor(
              getTagColor(item.originalData.order.status, item.isMemberNotification),
              0.6,
            ),
            color: 'white',
            borderColor: 'white',
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
          }}
        />
      </motion.div>
    </motion.div>
  );
};

export default NotificationCard;
export type { INotificationCardProps };
