import DropdownLocale from '@/components/DropdownAlpha/DropdownLocale';
import { Txt } from '@/components/TypographyMaster';
import { useCallback, useMemo } from 'react';
import { Space, Flex, Button, Switch, Tooltip } from 'antd';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { MoonOutlined, MutedOutlined, SoundOutlined, SunOutlined } from '@ant-design/icons';
import { useCreateCs, useCreateSupervisor, useCreateMerchantAdmin } from '@/api/auth';
import { useWalletAssets } from '@/api';
import logoLight from '@/assets/medium-logo-light2.png';
import logoDark from '@/assets/medium-logo-dark2.png';
import logoDemo from '@/assets/full-logo-wide-demo.png';
import useLogout from '@/hooks/useLogout';
import { useUserStore } from '@/store';
import { CryptoEnum } from '@/utils';
import { useIsDemo } from '@/hooks';

// components
import Notification from './Notification';
import BalanceIcon from './BalanceIcon';

interface IPrivateHeaderProps {}
const PrivateHeader: React.FunctionComponent<IPrivateHeaderProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { soundEnable, setSoundEnable, isDark, setIsDark, info } = useUserStore();
  const { t } = useTranslation('privateHeader');
  const isDemo = useIsDemo();

  // mutations
  const { mutate: createCs, isPending: inCcs } = useCreateCs({});
  const { mutate: createSupervisor, isPending: inCSupervisor } = useCreateSupervisor({});
  const { mutate: createMerchantAdmin, isPending: inCMerchantAdmin } = useCreateMerchantAdmin({});
  const { data: walletAssets } = useWalletAssets({});

  // handlers
  const { handleClean } = useLogout();
  const handleCreateCAS1_5 = useCallback(() => {
    Array(5)
      .fill(null)
      .forEach((_, index) => {
        createCs({
          userName: `cs${index + 1}`,
          password: '123456Aa',
          nickName: `Auto create cs ${index + 1}`,
        });
        createSupervisor({
          userName: `supervisor${index + 1}`,
          password: '123456Aa',
          nickName: `Auto create supervisor  ${index + 1}`,
        });
        createMerchantAdmin({
          userName: `MerchantAdmin${index + 1}`,
          password: '123456Aa',
          nickName: `Auto create merchant Admin  ${index + 1}`,
          merchantId: index,
        });
      });
  }, [createCs, createMerchantAdmin, createSupervisor]);

  const logoSrc = useMemo(() => {
    if (isDemo && isDark) return logoDemo;
    if (isDemo && !isDark) return logoDemo;
    if (isDark) return logoDark;
    if (!isDark) return logoLight;
    return '';
  }, [isDark, isDemo]);

  const colorStyle = useMemo(() => {
    if (isDemo && isDark) return '#fff';
    if (isDemo && !isDark) return '#fff';
    if (isDark) return '';
    if (!isDark) return '';
    return '';
  }, [isDark, isDemo]);

  return (
    <>
      {/* left */}
      <Space
        align='center'
        size='large'
      >
        <Link
          to='/'
          className='text-nowrap flex h-9 items-center'
        >
          <img
            src={logoSrc}
            alt='logo'
            className='h-full'
          />
        </Link>
        <Switch
          checkedChildren={<SoundOutlined />}
          unCheckedChildren={<MutedOutlined />}
          defaultChecked={soundEnable}
          onChange={() => {
            setSoundEnable(!soundEnable);
          }}
        />
        <Flex
          gap={5}
          align='center'
        >
          <BalanceIcon
            type={CryptoEnum.TRC20_USDT}
            balance={walletAssets?.usdtBalance}
            avb={walletAssets?.usdtBalance}
          />
        </Flex>
      </Space>

      {/* Right */}
      <Space>
        {import.meta.env.DEV && info?.roles.includes('SystemAdmin') && (
          <Button
            loading={inCcs || inCSupervisor || inCMerchantAdmin}
            type='dashed'
            onClick={handleCreateCAS1_5}
          >
            建立 C & S 1-5
          </Button>
        )}

        <Notification />

        <Tooltip
          placement='bottom'
          title={<Txt type='warning'>{t('operationPasswordWarning')}</Txt>}
          open={!info?.operationPasswordEnabled}
        >
          <Link to='user'>
            <Button type='link'>
              <Txt
                style={{ color: colorStyle }}
                className={`underline underline-offset-4
							${isDark ? 'text-white' : 'text-black'}
							${isDark ? 'decoration-blue-200' : 'decoration-blue-800'}`}
              >
                {t('welcome')}！{info?.nickName}
              </Txt>
            </Button>
          </Link>
        </Tooltip>

        <Button
          onClick={() => handleClean()}
          type='text'
          style={{ color: colorStyle }}
        >
          {t('signOut')}
        </Button>

        <Button
          type='text'
          icon={isDark ? <MoonOutlined /> : <SunOutlined />}
          style={{ color: colorStyle }}
          onClick={() => setIsDark(!isDark)}
        />

        <DropdownLocale />
      </Space>
    </>
  );
};

export default PrivateHeader;
