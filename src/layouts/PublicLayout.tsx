// libs
import { useMemo } from 'react';
import { Outlet } from 'react-router-dom';
import { Button, Divider, Layout } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';

// assets
import logoLight from '@/assets/medium-logo-light2.png';
import logoDark from '@/assets/medium-logo-dark2.png';

// store
import { useUserStore } from '@/store';

// components
import DropdownLocale from '@/components/DropdownAlpha/DropdownLocale';

interface IPublicLayoutProps {}

const PublicLayout: React.FunctionComponent<IPublicLayoutProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { isDark, setIsDark } = useUserStore();

  // compute
  const isDevEnvironment = import.meta.env.DEV;
  const isDemoApi = useMemo(() => {
    const apiBase = import.meta.env.VITE_AXIOS_ROOT;
    return apiBase.toLowerCase().includes('demo');
  }, []);

  return (
    <Layout className='min-h-screen items-center'>
      <div className='mt-20 p-2 lg:min-w-[1000px]'>
        <Layout.Header className='flex gap-x-4 bg-transparent p-0'>
          <img
            src={isDark ? logoDark : logoLight}
            alt='logo'
          />

          <span
            style={{ fontWeight: '800' }}
            className='text-[28px] text-green-600'
          >
            {isDevEnvironment ? 'DEV' : ''} {isDemoApi ? 'DEMO' : ''}
          </span>
        </Layout.Header>
        <Layout.Content>
          <Outlet />
        </Layout.Content>
        <Layout.Footer>
          <Divider />
          <footer className='flex items-center justify-end'>
            <Button
              type='text'
              icon={isDark ? <MoonOutlined /> : <SunOutlined />}
              onClick={() => setIsDark(!isDark)}
            />
            <Divider type='vertical' />

            <DropdownLocale dontStickDown='up' />
          </footer>
        </Layout.Footer>
      </div>
      <div style={{ position: 'fixed', left: 20, bottom: 20 }}>Version: {import.meta.env.VITE_VERSION}</div>
    </Layout>
  );
};

export default PublicLayout;
